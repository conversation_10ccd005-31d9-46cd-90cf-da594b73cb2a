# 前端环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改

# ================================
# API 配置
# ================================

# 用户前端 API 地址
API_URL=http://localhost:3001

# 管理后台 API 地址（分离部署时使用）
ADMIN_API_URL=http://localhost:3001

# ================================
# 构建配置
# ================================

# 构建类型: all(开发) | user(用户前端) | admin(管理后台)
BUILD_TYPE=all

# 环境类型
NODE_ENV=development

# ================================
# 可选配置
# ================================

# 网站地址（用于 SEO 和分享）
NUXT_PUBLIC_SITE_URL=http://localhost:3000

# CDN 地址（用于静态资源加速）
NUXT_PUBLIC_CDN_URL=

# ================================
# 生产环境示例
# ================================

# 用户前端生产配置：
# API_URL=https://api.yourdomain.com
# BUILD_TYPE=user
# NODE_ENV=production
# NUXT_PUBLIC_SITE_URL=https://yourdomain.com

# 管理后台生产配置：
# ADMIN_API_URL=https://admin-api.yourdomain.com
# BUILD_TYPE=admin
# NODE_ENV=production
# NUXT_PUBLIC_SITE_URL=https://admin.yourdomain.com

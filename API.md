# Jable.TV API 接口文檔

## 📋 概述

Jable.TV 提供兩套API接口：
1. **前端API**: 供網站前端使用，無需認證
2. **Python上傳API**: 供Python腳本使用，需要API密鑰認證

## 🔑 認證方式

### 前端API
- **認證**: 無需認證
- **訪問**: 公開訪問
- **用途**: 網站前端數據展示

### Python上傳API
- **認證**: <PERSON><PERSON> (API密鑰)
- **格式**: `Authorization: Bearer jbl_upload_{timestamp}_{random}`
- **用途**: Python腳本上傳和管理影片數據

## 📊 前端API接口

### 1. 獲取影片列表

```http
GET /api/videos
```

**查詢參數:**
| 參數 | 類型 | 必需 | 默認值 | 說明 |
|------|------|------|--------|------|
| page | number | 否 | 1 | 頁碼 |
| limit | number | 否 | 24 | 每頁數量 |
| category | number | 否 | - | 分類ID |
| tag | string | 否 | - | 標籤名稱 |
| search | string | 否 | - | 搜索關鍵詞 |
| sort | string | 否 | latest | 排序方式: latest/popular/rating |

**響應示例:**
```json
{
  "success": true,
  "data": {
    "videos": [
      {
        "id": 1,
        "title": "SSIS-123 美女教師的秘密課程",
        "coverUrl": "https://example.com/cover1.jpg",
        "videoUrl": "https://example.com/video1.mp4",
        "category": "角色劇情",
        "categoryId": 1,
        "tags": ["教師", "制服"],
        "duration": "2:15:30",
        "views": 125000,
        "rating": 9.2,
        "description": "影片描述...",
        "uploadDate": "2024-01-15T10:30:00Z",
        "status": "active",
        "featured": true
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 24,
      "total": 1250,
      "totalPages": 53
    }
  }
}
```

### 2. 獲取影片詳情

```http
GET /api/videos/:id
```

**路徑參數:**
| 參數 | 類型 | 必需 | 說明 |
|------|------|------|------|
| id | number | 是 | 影片ID |

**響應示例:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "SSIS-123 美女教師的秘密課程",
    "coverUrl": "https://example.com/cover1.jpg",
    "videoUrl": "https://example.com/video1.mp4",
    "category": "角色劇情",
    "categoryId": 1,
    "tags": ["教師", "制服"],
    "duration": "2:15:30",
    "views": 125000,
    "rating": 9.2,
    "description": "詳細的影片描述...",
    "uploadDate": "2024-01-15T10:30:00Z",
    "status": "active",
    "featured": true,
    "relatedVideos": [
      {
        "id": 2,
        "title": "相關影片標題",
        "coverUrl": "https://example.com/cover2.jpg"
      }
    ]
  }
}
```

### 3. 獲取分類列表

```http
GET /api/categories
```

**響應示例:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "角色劇情",
      "slug": "role-play",
      "description": "各種角色扮演劇情",
      "coverUrl": "https://example.com/category1.jpg",
      "videoCount": 1250,
      "sort": 1,
      "status": "active"
    },
    {
      "id": 2,
      "name": "制服誘惑",
      "slug": "uniform",
      "description": "制服主題影片",
      "coverUrl": "https://example.com/category2.jpg",
      "videoCount": 980,
      "sort": 2,
      "status": "active"
    }
  ]
}
```

### 4. 搜索影片

```http
GET /api/search
```

**查詢參數:**
| 參數 | 類型 | 必需 | 默認值 | 說明 |
|------|------|------|--------|------|
| q | string | 是 | - | 搜索關鍵詞 |
| page | number | 否 | 1 | 頁碼 |
| limit | number | 否 | 24 | 每頁數量 |

**響應示例:**
```json
{
  "success": true,
  "data": {
    "keyword": "教師",
    "videos": [
      {
        "id": 1,
        "title": "SSIS-123 美女教師的秘密課程",
        "coverUrl": "https://example.com/cover1.jpg",
        "category": "角色劇情",
        "duration": "2:15:30",
        "views": 125000,
        "rating": 9.2
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 24,
      "total": 156,
      "totalPages": 7
    }
  }
}
```

### 5. 獲取排行榜

```http
GET /api/rankings
```

**查詢參數:**
| 參數 | 類型 | 必需 | 默認值 | 說明 |
|------|------|------|--------|------|
| type | string | 否 | hot | 排行類型: hot/latest/rating/views |
| limit | number | 否 | 50 | 返回數量 |

**響應示例:**
```json
{
  "success": true,
  "data": {
    "type": "hot",
    "videos": [
      {
        "id": 1,
        "title": "SSIS-123 美女教師的秘密課程",
        "coverUrl": "https://example.com/cover1.jpg",
        "category": "角色劇情",
        "duration": "2:15:30",
        "views": 125000,
        "rating": 9.2,
        "rankingValue": 98500
      }
    ]
  }
}
```

### 6. 獲取標籤列表

```http
GET /api/tags
```

**查詢參數:**
| 參數 | 類型 | 必需 | 默認值 | 說明 |
|------|------|------|--------|------|
| category | string | 否 | - | 標籤分類: role/uniform/scene/other |
| hot | boolean | 否 | false | 是否只返回熱門標籤 |

**響應示例:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "美女教師",
      "category": "role",
      "count": 1250,
      "isHot": true
    },
    {
      "id": 2,
      "name": "制服誘惑",
      "category": "uniform",
      "count": 980,
      "isHot": true
    }
  ]
}
```

## 🔧 Python上傳API接口

### 認證頭部
所有Python上傳API都需要在請求頭中包含API密鑰：

```http
Authorization: Bearer jbl_upload_1705392847123_k8j9h2f5d3a
Content-Type: application/json
```

### 1. 上傳單個影片

```http
POST /api/upload/video
```

**請求體:**
```json
{
  "title": "SSIS-123 美女教師的秘密課程",
  "videoUrl": "https://oss.example.com/videos/ssis123.mp4",
  "coverUrl": "https://oss.example.com/covers/ssis123.jpg",
  "category": "角色劇情",
  "tags": ["教師", "制服", "劇情"],
  "duration": "2:15:30",
  "description": "美女教師的秘密課程，精彩劇情不容錯過...",
  "featured": false
}
```

**響應示例:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "SSIS-123 美女教師的秘密課程",
    "status": "active",
    "uploadDate": "2024-01-15T10:30:00Z"
  },
  "message": "影片上傳成功"
}
```

### 2. 批量上傳影片

```http
POST /api/upload/batch
```

**請求體:**
```json
{
  "videos": [
    {
      "title": "SSIS-123 美女教師的秘密課程",
      "videoUrl": "https://oss.example.com/videos/ssis123.mp4",
      "coverUrl": "https://oss.example.com/covers/ssis123.jpg",
      "category": "角色劇情",
      "tags": ["教師", "制服"],
      "duration": "2:15:30",
      "description": "影片描述..."
    },
    {
      "title": "MIDE-456 辦公室戀情物語",
      "videoUrl": "https://oss.example.com/videos/mide456.mp4",
      "coverUrl": "https://oss.example.com/covers/mide456.jpg",
      "category": "制服誘惑",
      "tags": ["辦公室", "制服"],
      "duration": "1:58:45",
      "description": "辦公室戀情物語..."
    }
  ]
}
```

**響應示例:**
```json
{
  "success": true,
  "data": {
    "uploaded": 2,
    "failed": 0,
    "results": [
      {
        "id": 1,
        "title": "SSIS-123 美女教師的秘密課程",
        "status": "success"
      },
      {
        "id": 2,
        "title": "MIDE-456 辦公室戀情物語",
        "status": "success"
      }
    ]
  },
  "message": "批量上傳完成"
}
```

### 3. 更新影片信息

```http
PUT /api/upload/update/:id
```

**路徑參數:**
| 參數 | 類型 | 必需 | 說明 |
|------|------|------|------|
| id | number | 是 | 影片ID |

**請求體:**
```json
{
  "title": "新的影片標題",
  "description": "更新後的影片描述",
  "tags": ["新標籤1", "新標籤2"],
  "featured": true
}
```

**響應示例:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "新的影片標題",
    "updatedAt": "2024-01-15T11:30:00Z"
  },
  "message": "影片信息更新成功"
}
```

### 4. 刪除影片

```http
DELETE /api/upload/delete/:id
```

**路徑參數:**
| 參數 | 類型 | 必需 | 說明 |
|------|------|------|------|
| id | number | 是 | 影片ID |

**響應示例:**
```json
{
  "success": true,
  "message": "影片刪除成功"
}
```

### 5. 獲取上傳統計

```http
GET /api/upload/stats
```

**響應示例:**
```json
{
  "success": true,
  "data": {
    "totalVideos": 1250,
    "todayUploads": 15,
    "weekUploads": 89,
    "monthUploads": 234,
    "storageUsed": "125.6 GB",
    "apiCalls": {
      "today": 156,
      "week": 1024,
      "month": 4567
    }
  }
}
```

## ❌ 錯誤響應

### 錯誤格式
```json
{
  "success": false,
  "error": {
    "code": "INVALID_API_KEY",
    "message": "API密鑰無效或已過期"
  }
}
```

### 常見錯誤碼

| 錯誤碼 | HTTP狀態碼 | 說明 |
|--------|------------|------|
| INVALID_API_KEY | 401 | API密鑰無效 |
| MISSING_AUTHORIZATION | 401 | 缺少認證頭部 |
| RATE_LIMIT_EXCEEDED | 429 | 請求頻率超限 |
| INVALID_PARAMETERS | 400 | 請求參數無效 |
| VIDEO_NOT_FOUND | 404 | 影片不存在 |
| DUPLICATE_VIDEO | 409 | 影片已存在 |
| SERVER_ERROR | 500 | 服務器內部錯誤 |

## 📝 使用示例

### Python腳本示例

```python
import requests
import json

# API配置
API_BASE_URL = "https://91JSPG.COM"
API_KEY = "jbl_upload_1705392847123_k8j9h2f5d3a"

headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

# 上傳單個影片
def upload_video(video_data):
    url = f"{API_BASE_URL}/api/upload/video"
    response = requests.post(url, json=video_data, headers=headers)
    return response.json()

# 批量上傳影片
def batch_upload(videos):
    url = f"{API_BASE_URL}/api/upload/batch"
    data = {"videos": videos}
    response = requests.post(url, json=data, headers=headers)
    return response.json()

# 使用示例
video_data = {
    "title": "SSIS-123 美女教師的秘密課程",
    "videoUrl": "https://oss.example.com/videos/ssis123.mp4",
    "coverUrl": "https://oss.example.com/covers/ssis123.jpg",
    "category": "角色劇情",
    "tags": ["教師", "制服"],
    "duration": "2:15:30",
    "description": "影片描述..."
}

result = upload_video(video_data)
print(json.dumps(result, indent=2, ensure_ascii=False))
```

### JavaScript前端示例

```javascript
// 獲取影片列表
async function getVideos(page = 1, category = null) {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: '24'
  });
  
  if (category) {
    params.append('category', category.toString());
  }
  
  const response = await fetch(`/api/videos?${params}`);
  const data = await response.json();
  return data;
}

// 搜索影片
async function searchVideos(keyword, page = 1) {
  const params = new URLSearchParams({
    q: keyword,
    page: page.toString(),
    limit: '24'
  });
  
  const response = await fetch(`/api/search?${params}`);
  const data = await response.json();
  return data;
}

// 使用示例
getVideos(1, 1).then(data => {
  console.log('影片列表:', data);
});

searchVideos('教師').then(data => {
  console.log('搜索結果:', data);
});
```

## 🔒 安全注意事項

1. **API密鑰保護**: 
   - 不要在前端代碼中暴露API密鑰
   - 定期更換API密鑰
   - 使用HTTPS傳輸

2. **請求頻率限制**:
   - 每分鐘最多60次請求
   - 批量操作建議使用批量接口

3. **數據驗證**:
   - 所有輸入數據都會進行嚴格驗證
   - 文件URL必須是有效的HTTP/HTTPS地址
   - 影片時長格式: HH:MM:SS

4. **錯誤處理**:
   - 始終檢查響應的success字段
   - 根據錯誤碼進行相應的錯誤處理
   - 實現重試機制處理臨時錯誤

---

**更新日期**: 2024-01-15  
**API版本**: v1.0.0

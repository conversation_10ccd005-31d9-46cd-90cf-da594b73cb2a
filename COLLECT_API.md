# 91JSPG.COM 采集接口文档

## 概述

91JSPG.COM 提供兼容苹果CMS格式的采集接口，支持JSON和XML格式输出，无频率限制，欢迎其他影视站点对接采集。

## 接口信息

- **接口版本**: v1.0
- **兼容格式**: 苹果CMS v10
- **输出格式**: JSON / XML
- **频率限制**: 无限制
- **接口地址**: `https://your-domain.com/api/collect`

## 快速开始

### 1. 获取视频列表
```
GET /api/collect/vod?ac=list
```

### 2. 获取视频详情
```
GET /api/collect/vod?ac=detail&ids=1,2,3
```

### 3. 搜索视频
```
GET /api/collect/vod?ac=list&wd=关键字
```

## API 参数说明

### 视频采集接口 `/api/collect/vod`

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ac | string | 是 | 操作类型：list(列表) \| detail(详情) \| videolist(内容) |
| t | int | 否 | 分类ID，获取指定分类的视频 |
| pg | int | 否 | 页码，默认为1 |
| wd | string | 否 | 搜索关键字 |
| h | int | 否 | 获取几小时内的数据 |
| ids | string | 否 | 视频ID，多个用逗号分隔，用于获取详情 |
| at | string | 否 | 输出格式：json(默认) \| xml |

## 示例请求

### 获取视频列表（第1页）
```
GET /api/collect/vod?ac=list&pg=1
```

### 获取指定分类视频
```
GET /api/collect/vod?ac=list&t=1&pg=1
```

### 搜索视频
```
GET /api/collect/vod?ac=list&wd=动作&pg=1
```

### 获取视频详情
```
GET /api/collect/vod?ac=detail&ids=1,2,3
```

### 获取24小时内更新的视频
```
GET /api/collect/vod?ac=detail&h=24
```

### XML格式输出
```
GET /api/collect/vod?ac=list&at=xml&pg=1
```

## 响应格式

### JSON格式响应（列表）
```json
{
  "code": 1,
  "msg": "数据列表",
  "page": 1,
  "pagecount": 10,
  "limit": "20",
  "total": 200,
  "list": [
    {
      "vod_id": 1,
      "vod_name": "视频标题",
      "type_id": 1,
      "type_name": "动作片",
      "vod_en": "video-slug",
      "vod_time": "2025-07-24 19:00:00",
      "vod_remarks": "高清",
      "vod_play_from": "default"
    }
  ],
  "class": [
    {
      "type_id": 1,
      "type_name": "动作片"
    }
  ]
}
```

### JSON格式响应（详情）
```json
{
  "code": 1,
  "msg": "数据列表",
  "page": 1,
  "pagecount": 1,
  "limit": "1",
  "total": 1,
  "list": [
    {
      "vod_id": 1,
      "vod_name": "视频标题",
      "type_id": 1,
      "type_name": "动作片",
      "vod_en": "video-slug",
      "vod_time": "2025-07-24 19:00:00",
      "vod_remarks": "高清",
      "vod_play_from": "default",
      "vod_pic": "https://example.com/cover.jpg",
      "vod_area": "大陆",
      "vod_lang": "国语",
      "vod_year": "2025",
      "vod_serial": "0",
      "vod_actor": "",
      "vod_director": "",
      "vod_content": "视频描述内容",
      "vod_play_url": "正片$https://example.com/video.mp4"
    }
  ]
}
```

### XML格式响应
```xml
<?xml version="1.0" encoding="utf-8"?>
<rss version="2.0">
<channel>
<title>91JSPG.COM</title>
<link>https://91jspg.com</link>
<description>影视采集接口</description>
<lastBuildDate>2025-07-24 19:00:00</lastBuildDate>
<generator>91JSPG.COM</generator>
<item>
<title><![CDATA[视频标题]]></title>
<link>https://91jspg.com/play/1</link>
<description><![CDATA[视频描述]]></description>
<pubDate>2025-07-24 19:00:00</pubDate>
<category>动作片</category>
</item>
</channel>
</rss>
```

## 错误响应

当请求出错时，接口会返回以下格式的错误信息：

```json
{
  "code": 0,
  "msg": "错误描述",
  "error": "详细错误信息"
}
```

## 分类列表

接口支持以下分类（具体分类ID请通过接口获取）：

- 动作片
- 喜剧片
- 爱情片
- 科幻片
- 恐怖片
- 剧情片
- 战争片
- 国产剧
- 港台剧
- 日韩剧
- 欧美剧

## 使用说明

1. **无频率限制**: 本接口不设置访问频率限制，但请合理使用
2. **兼容性**: 完全兼容苹果CMS v10的采集格式
3. **数据更新**: 数据实时更新，支持按时间筛选最新内容
4. **多格式支持**: 同时支持JSON和XML格式输出

## 技术支持

如果您在使用过程中遇到任何问题，请联系我们：

- 网站：https://91jspg.com
- 接口文档：https://91jspg.com/collect

## 免责声明

本接口完全免费提供，请合理使用，共同维护良好的采集环境。使用本接口即表示您同意遵守相关法律法规和使用条款。

---

© 2025 91JSPG.COM - 专业的影视资源采集接口

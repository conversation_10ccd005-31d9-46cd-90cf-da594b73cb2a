# Nuxt 3 调试配置说明

## 🔧 已启用的调试功能

### 1. Nuxt 核心调试
```javascript
debug: process.env.NODE_ENV === 'development'
```
- **功能**: 启用 Nuxt 调试模式
- **效果**: 显示钩子名称和执行时间
- **环境**: 仅在开发环境启用

### 2. Nitro 调试配置
```javascript
nitro: {
  logLevel: process.env.NODE_ENV === 'development' ? 4 : 3,
  timing: process.env.NODE_ENV === 'development'
}
```
- **logLevel**: 日志详细程度
  - `4` (debug): 开发环境 - 显示所有调试信息
  - `3` (info): 生产环境 - 显示基本信息
- **timing**: 启用时间信息和性能统计

### 3. Vite 调试配置
```javascript
vite: {
  logLevel: process.env.NODE_ENV === 'development' ? 'info' : 'warn',
  build: {
    sourcemap: process.env.NODE_ENV === 'development'
  }
}
```
- **logLevel**: Vite 构建日志级别
- **sourcemap**: 开发环境启用源映射，便于调试

## 📊 日志级别说明

### Nitro 日志级别
- `0` - silent: 静默模式
- `1` - error: 仅错误信息
- `2` - warn: 警告和错误
- `3` - info: 信息、警告和错误
- `4` - debug: 所有调试信息

### Vite 日志级别
- `'silent'` - 静默
- `'error'` - 仅错误
- `'warn'` - 警告和错误
- `'info'` - 详细信息

## 🚀 使用方法

### 开发环境启动
```bash
# 启动开发服务器（自动启用调试）
npm run dev

# 或者明确设置环境变量
NODE_ENV=development npm run dev
```

### 查看调试信息
启动后，你将看到：
1. **Nuxt 钩子信息**: 显示各个生命周期钩子的执行时间
2. **Nitro 详细日志**: API 路由、代理请求等详细信息
3. **Vite 构建信息**: 模块热更新、构建过程等
4. **时间统计**: 各个操作的耗时信息

### 生产环境
```bash
# 生产环境构建（调试功能自动关闭）
NODE_ENV=production npm run build
```

## 🔍 调试技巧

### 1. API 代理调试
当你访问 `/api/**` 路由时，会看到详细的代理信息：
```
[nitro] Proxying /api/videos to http://localhost:3001/api/videos
```

### 2. 路由调试
查看路由匹配和处理过程：
```
[nuxt] Route matched: /videos/123
[nitro] Handler: /videos/[id].vue
```

### 3. 性能调试
启用 timing 后可以看到各个操作的耗时：
```
[nitro] Request processed in 45ms
[nuxt] Page rendered in 120ms
```

## 🛠️ 自定义调试

### 临时启用更详细的日志
```bash
# 设置更高的日志级别
DEBUG=* npm run dev
```

### 在代码中添加调试信息
```javascript
// 在页面或组件中
console.log('[DEBUG]', '调试信息', data)

// 在服务器端
import { logger } from '#nitro/logger'
logger.debug('服务器调试信息', data)
```

## 📝 注意事项

1. **性能影响**: 调试模式会影响性能，仅在开发环境使用
2. **日志大小**: 详细日志可能产生大量输出，注意控制台性能
3. **生产环境**: 生产环境自动关闭调试功能，确保性能和安全
4. **源映射**: 开发环境的源映射有助于调试，但会增加构建时间

## 🔧 故障排除

### 如果调试信息太多
```javascript
// 临时降低日志级别
nitro: {
  logLevel: 2 // 只显示警告和错误
}
```

### 如果需要更多调试信息
```javascript
// 临时启用所有调试
debug: true,
nitro: {
  logLevel: 4,
  timing: true
}
```

# 🎬 影视CMS系统

一个基于 Nuxt.js 3 和 Node.js 的现代化影视内容管理系统，支持用户前端和管理后台的分离构建部署。

## ✨ 特性

- 🎬 **完整的影视管理系统**：视频上传、分类管理、用户系统
- 🔐 **安全的管理后台**：JWT 认证、权限控制、分离部署
- 📱 **响应式设计**：支持桌面端和移动端
- ⚡ **高性能**：SSR/SSG 支持、代码分割、缓存优化
- 🔒 **安全分离**：用户前端和管理后台可独立构建部署

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- MySQL >= 8.0
- npm >= 8.0.0

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <项目地址>
   cd nuxtjs影视cms
   ```

2. **安装依赖**
   ```bash
   # 前端依赖
   npm install
   
   # 后端依赖
   cd backend
   npm install
   cd ..
   ```

3. **配置数据库**
   ```sql
   CREATE DATABASE video_cms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```
   
   ```bash
   # 导入数据表
   cd backend
   mysql -u root -p video_cms < database/schema.sql
   ```

4. **配置环境变量**
   ```bash
   # 前端环境变量
   cp .env.example .env
   
   # 后端环境变量
   cp backend/.env.example backend/.env
   ```

5. **启动项目**
   ```bash
   # 启动后端 (终端1)
   cd backend
   npm run dev
   
   # 启动前端 (终端2)
   npm run dev
   ```

6. **访问系统**
   - 用户前端：http://localhost:3000
   - 管理后台：http://localhost:3000/admin
   - 后端 API：http://localhost:3001

## 🏗️ 分离构建

### 构建命令

```bash
# 用户前端构建（公开部署）
npm run build:user
npm run verify:user

# 管理后台构建（内网部署）
npm run build:admin
npm run verify:admin

# 完整构建（开发测试）
npm run build:all
```

### 部署示例

**用户前端部署：**
```bash
# 构建用户前端
npm run build:user

# 启动服务
node .output/server/index.mjs

# 验证：访问 /admin 应该返回 404
curl http://localhost:3000/admin
```

**管理后台部署：**
```bash
# 构建管理后台
npm run build:admin

# 启动服务
node .output/server/index.mjs

# 验证：访问 / 应该重定向到 /admin/login
curl http://localhost:3000/
```

## 📁 项目结构

```
├── assets/                 # 静态资源
├── components/            # Vue 组件
├── composables/          # 组合函数
├── config/               # 配置文件
│   └── site.js          # 网站配置
├── layouts/              # 布局组件
│   ├── default.vue      # 用户前端布局
│   └── admin.vue        # 管理后台布局
├── pages/                # 页面组件
│   ├── index.vue        # 用户首页
│   ├── search.vue       # 搜索页面
│   └── admin/          # 管理后台页面
├── scripts/              # 构建脚本
├── backend/              # 后端代码
│   ├── src/             # 源代码
│   ├── uploads/         # 上传文件
│   └── database/        # 数据库文件
└── nuxt.config.ts        # Nuxt 配置
```

## ⚙️ 配置管理

### 网站配置

编辑 `config/site.js` 文件：

```javascript
export const siteConfig = {
  basic: {
    siteName: '91JSPG.COM',              // 网站名称
    siteDescription: '免費高清AV在線看',  // 网站描述
    copyright: '© 2024 91JSPG.COM. 保留所有权利.'
  },
  display: {
    themeColor: 'orange',              // 主题色彩
    videosPerPage: 24                  // 每页视频数量
  },
  api: {
    development: 'http://localhost:3001',
    production: process.env.API_URL || 'https://api.yourdomain.com'
  }
}
```

### 环境变量

**前端 (.env)：**
```bash
API_URL=http://localhost:3001
ADMIN_API_URL=http://localhost:3001
BUILD_TYPE=all
```

**后端 (backend/.env)：**
```bash
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=root
DB_NAME=video_cms
JWT_SECRET=your-secret-key
PORT=3001
```

## 🔧 开发指南

### 添加新页面

1. **用户前端页面**：
   ```bash
   # 创建页面文件
   touch pages/new-page.vue
   
   # 添加导航链接到 layouts/default.vue
   <NuxtLink to="/new-page">新页面</NuxtLink>
   ```

2. **管理后台页面**：
   ```bash
   # 创建管理页面
   mkdir pages/admin/new-feature
   touch pages/admin/new-feature/index.vue
   
   # 添加认证保护
   definePageMeta({ middleware: 'auth' })
   ```

### API 开发

1. **创建控制器**：
   ```javascript
   // backend/src/controllers/newController.js
   exports.getData = async (req, res) => {
     try {
       const data = await Model.getData()
       res.json({ success: true, data })
     } catch (error) {
       res.status(500).json({ success: false, message: error.message })
     }
   }
   ```

2. **添加路由**：
   ```javascript
   // backend/src/routes/api/index.js
   router.get('/new-data', newController.getData)
   ```

3. **前端调用**：
   ```javascript
   const { apiUser } = useApi()
   const response = await apiUser('/api/new-data')
   ```

## 🚀 部署指南

### 生产部署

1. **用户前端部署**：
   ```bash
   # 设置环境变量
   export API_URL=https://api.yourdomain.com
   export BUILD_TYPE=user
   
   # 构建和部署
   npm run build:user
   scp -r .output/ user@server:/var/www/user-frontend/
   ```

2. **管理后台部署**：
   ```bash
   # 设置环境变量
   export ADMIN_API_URL=https://admin-api.yourdomain.com
   export BUILD_TYPE=admin
   
   # 构建和部署
   npm run build:admin
   scp -r .output/ admin@internal-server:/var/www/admin-dashboard/
   ```

3. **使用 PM2 管理**：
   ```bash
   # 安装 PM2
   npm install -g pm2
   
   # 启动服务
   pm2 start .output/server/index.mjs --name "user-frontend"
   pm2 start backend/src/app.js --name "video-cms-api"
   
   # 设置开机自启
   pm2 startup
   pm2 save
   ```

## 🔍 故障排除

### 常见问题

**构建错误：**
```bash
# 清理缓存重新构建
rm -rf node_modules/.cache
npm run build:user
```

**API 连接失败：**
```bash
# 检查后端服务状态
pm2 status
pm2 logs video-cms-api
```

**数据库连接失败：**
```bash
# 检查数据库服务
sudo systemctl status mysql
mysql -u root -p -e "SHOW DATABASES;"
```

## 📚 文档

- **完整文档**：[PROJECT_GUIDE.md](./PROJECT_GUIDE.md)
- **API 文档**：访问 http://localhost:3000/api-docs
- **技术栈文档**：
  - [Nuxt.js](https://nuxt.com/docs)
  - [Vue.js](https://vuejs.org/guide/)
  - [Node.js](https://nodejs.org/docs/)

## 🤝 贡献

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如果您遇到问题或需要帮助：

1. 查看 [完整文档](./PROJECT_GUIDE.md)
2. 搜索 [GitHub Issues](../../issues)
3. 创建新的 Issue

---

**开发团队** | **最后更新：2024年1月20日**

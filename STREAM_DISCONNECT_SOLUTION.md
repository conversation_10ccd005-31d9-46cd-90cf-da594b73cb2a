# 直播流断开连接解决方案

## 问题
用户切换直播房间时，之前的流连接没有完全断开，仍在传输数据。

## 根本原因
服务端代理连接没有被断开。前端播放器断开连接，但服务端到原始流的连接仍然活跃。

## 解决方案

### 核心修改（4个文件）

#### 1. 后端代理控制器 (`backend/src/controllers/proxyController.js`)
- 添加活跃连接管理：`activeConnections Map`
- 新增强制断开API：`forceDisconnectStream()`
- 为每个连接分配唯一ID并在响应头返回
- 优雅的错误处理，避免ECONNABORTED错误

#### 2. 后端代理路由 (`backend/src/routes/api/proxy.js`)
- 添加断开连接端点：`POST /api/proxy/disconnect`

#### 3. 前端房间页面 (`pages/live/[platform]/[room].vue`)
- 添加服务端断开函数：`forceDisconnectProxyStream()`
- 在路由切换时调用断开API
- 移除复杂的前端清理逻辑，依赖组件自身管理

#### 4. 后端应用入口 (`backend/src/app.js`)
- 添加全局错误处理，忽略正常的网络断开错误

## 工作原理

```
用户切换房间
    ↓
调用 forceDisconnectProxyStream()
    ↓
POST /api/proxy/disconnect?url=原始流URL
    ↓
服务端查找并强制断开所有相关连接
    ↓
response.data.destroy() + res.end()
    ↓
网络传输完全停止
```

## 关键代码

### 前端断开调用
```javascript
const forceDisconnectProxyStream = async (streamUrl) => {
  const response = await apiUser('/api/proxy/disconnect', {
    method: 'POST',
    query: { url: streamUrl }
  })
}
```

### 服务端强制断开
```javascript
const forceDisconnectStream = (req, res) => {
  const streamUrl = decodeURIComponent(req.query.url)
  
  for (const [connectionId, connectionInfo] of activeConnections.entries()) {
    if (connectionInfo.streamUrl === streamUrl) {
      connectionInfo.response.data.destroy()
      connectionInfo.res.end()
      activeConnections.delete(connectionId)
    }
  }
}
```

## 测试验证

1. 打开浏览器开发者工具Network面板
2. 播放直播房间，观察代理请求
3. 切换房间，确认之前的连接立即断开
4. 控制台应显示：`✅ 服务端代理连接已断开`

## 效果
- ✅ 切换房间时网络传输立即停止
- ✅ 服务端连接正确清理
- ✅ 内存使用量保持稳定
- ✅ 无需刷新页面

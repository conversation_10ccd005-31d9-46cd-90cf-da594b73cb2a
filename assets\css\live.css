/* 直播页面专用样式 */

/* 平台网格布局 - 移动端优先设计 */
.platform-grid {
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

/* 直播间网格布局 - 移动端优先设计 */
.room-grid {
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
}

/* 推荐直播间网格布局 - 移动端优先设计 */
.recommended-grid {
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
}

/* 响应式调整 - 从大屏幕到小屏幕 */
@media (min-width: 1280px) {
  .platform-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }

  .room-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .recommended-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .platform-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .room-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  }

  .recommended-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  }
}

@media (max-width: 640px) {
  .platform-grid {
    grid-template-columns: 1fr;
  }

  .room-grid {
    grid-template-columns: 1fr 1fr;
  }

  .recommended-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 480px) {
  .room-grid {
    grid-template-columns: 1fr;
  }

  .recommended-grid {
    grid-template-columns: 1fr;
  }
}

/* 移动端容器优化 */
@media (max-width: 640px) {
  .platform-grid,
  .room-grid,
  .recommended-grid {
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .platform-grid,
  .room-grid,
  .recommended-grid {
    gap: 0.5rem;
  }
}

/* 直播状态动画 */
@keyframes live-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.live-indicator {
  animation: live-pulse 2s infinite;
}

/* 在线状态指示器动画 */
@keyframes online-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.online-dot {
  animation: online-pulse 2s infinite;
}

/* 悬停效果 */
.hover-scale {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 播放按钮动画 */
.play-button {
  transition: all 0.3s ease;
}

.play-button:hover {
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(168, 85, 247, 0.5);
}

/* 文本截断 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 视频比例 */
.aspect-video {
  aspect-ratio: 16 / 9;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

/* 渐变背景 */
.gradient-orange {
  background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
}

.gradient-red {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.gradient-orange-red {
  background: linear-gradient(135deg, #f97316 0%, #ef4444 100%);
}

/* 毛玻璃效果 */
.backdrop-blur-custom {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* 自定义滚动条 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(55, 65, 81, 0.3);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(168, 85, 247, 0.5);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(168, 85, 247, 0.7);
}

/* 加载动画 */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

/* 脉冲动画 */
@keyframes pulse-custom {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse-custom {
  animation: pulse-custom 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 弹跳动画 */
@keyframes bounce-custom {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.animate-bounce-custom {
  animation: bounce-custom 1s infinite;
}

/* 旋转动画 */
@keyframes spin-custom {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-custom {
  animation: spin-custom 1s linear infinite;
}

/* 淡入动画 */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

/* 滑入动画 */
@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-out;
}

/* 缩放动画 */
@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-scale-in {
  animation: scale-in 0.2s ease-out;
}

/* 直播间状态样式 */
.live-status {
  position: relative;
}

.live-status::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #ef4444, #f97316, #eab308, #22c55e, #3b82f6, #8b5cf6, #ef4444);
  background-size: 400% 400%;
  border-radius: inherit;
  z-index: -1;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* 观看人数动画 */
.viewer-count {
  transition: all 0.3s ease;
}

.viewer-count:hover {
  transform: scale(1.05);
  color: #a855f7;
}

/* 平台卡片特殊效果 */
.platform-card {
  position: relative;
  overflow: hidden;
}

.platform-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.platform-card:hover::before {
  left: 100%;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局溢出控制 - 防止水平滚动 */
@layer base {
  html {
    scroll-behavior: smooth;
    overflow-x: hidden;
    max-width: 100vw;
  }

  body {
    transition: color 0.3s ease;
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* 确保所有容器不超出视口 */
  * {
    box-sizing: border-box;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f5f9;
  }

  ::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 9999px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
}

/* 视频网格响应式布局 */
.video-grid {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .video-grid {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .video-grid {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (min-width: 2200px) {
  .video-grid {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
}

/* 移动端视频网格优化 */
@media (max-width: 640px) {
  .video-grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .video-grid {
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }
}


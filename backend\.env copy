# 服务器配置
NODE_ENV=production
PORT=3001
HOST=0.0.0.0

# 数据库配置 (MySQL 8)
DB_HOST=*************
DB_PORT=3306
DB_NAME=video_cms
DB_USER=video_cms
DB_PASSWORD=WzMEL5GPfKX3aAHn
DB_SSL=false

# Redis配置 (可选，如果没有Redis会跳过)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your_super_secure_jwt_secret_key_change_this_in_production_91jspg_2024
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# API密钥配置
API_KEY_PREFIX=91jspg_upload_
API_KEY_LENGTH=50

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=104857600
ALLOWED_IMAGE_TYPES=jpeg,jpg,png,gif,webp
ALLOWED_VIDEO_TYPES=mp4,avi,mov,wmv,flv,mkv

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=5000
UPLOAD_RATE_LIMIT_MAX=500

# 网站配置
SITE_NAME=91JSPG
SITE_URL=https://91jspg.com
ADMIN_EMAIL=<EMAIL>

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# 缓存配置
CACHE_TTL=300
SESSION_TTL=1800

# 生产配置
DEBUG=false
ENABLE_CORS=true
CORS_ORIGIN=https://91jspg.com,https://www.91jspg.com




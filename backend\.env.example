# 服务器配置
NODE_ENV=development
PORT=3001
HOST=localhost

# 数据库配置 (MySQL)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=video_cms
DB_USER=root
DB_PASSWORD=root
DB_CONNECTION_LIMIT=10

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# API密钥配置
API_KEY_PREFIX=91jspg_upload_
API_KEY_LENGTH=50

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=104857600
ALLOWED_IMAGE_TYPES=jpeg,jpg,png,gif,webp
ALLOWED_VIDEO_TYPES=mp4,avi,mov,wmv,flv,mkv

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>
FROM_NAME=91JSPG.COM

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
UPLOAD_RATE_LIMIT_MAX=10

# 网站配置
SITE_NAME=91JSPG.COM
SITE_URL=https://91JSPG.COM
ADMIN_EMAIL=<EMAIL>

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# 缓存配置
CACHE_TTL=3600
SESSION_TTL=1800

# 第三方服务
GOOGLE_ANALYTICS_ID=
BAIDU_ANALYTICS_ID=

# 开发配置
DEBUG=false
ENABLE_CORS=true
CORS_ORIGIN=http://localhost:3000

# 生产环境部署修复指南

## 🚨 当前问题分析

根据日志分析，生产服务器存在以下问题：

1. **数据库连接失败** - 系统运行在Mock模式
2. **管理员登录失败** - `user_not_found` 错误
3. **API查询失败** - 所有数据库查询都在Mock模式下执行

## 🔧 解决方案

### 步骤1：检查生产服务器的环境配置

在生产服务器上检查 `.env` 文件是否存在且配置正确：

```bash
# 在生产服务器上执行
cd /www/wwwroot/api/backend
cat .env
```

### 步骤2：使用正确的生产环境配置

将 `production.env` 文件上传到生产服务器并重命名为 `.env`：

```bash
# 在生产服务器上执行
cd /www/wwwroot/api/backend
cp production.env .env
```

**重要配置项检查：**
```bash
DB_HOST=*************
DB_PORT=3306
DB_NAME=video_cms
DB_USER=video_cms
DB_PASSWORD=86trmPEF4NpJQPF3
DB_SSL=false
```

### 步骤3：测试数据库连接

```bash
# 在生产服务器上执行
cd /www/wwwroot/api/backend
node src/scripts/diagnose-production.js
```

### 步骤4：初始化生产环境数据

如果数据库连接正常但缺少数据：

```bash
# 在生产服务器上执行
cd /www/wwwroot/api/backend
node src/scripts/init-production.js
```

### 步骤5：重启后端服务

```bash
# 重启Node.js应用
pm2 restart all
# 或者
systemctl restart your-app-service
```

## 📋 验证步骤

### 1. 检查日志
```bash
# 查看应用日志
tail -f /www/wwwroot/api/backend/logs/app.log
```

应该看到：
- `✅ MySQL database connection established`
- 不再有 `Database query in mock mode` 警告

### 2. 测试管理员登录

使用以下凭据测试登录：
- **用户名**: `admin`
- **密码**: `123456`

### 3. 测试API接口

```bash
# 测试健康检查
curl http://*************:3001/health

# 测试管理员登录
curl -X POST http://*************:3001/api/admin/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"123456"}'
```

## 🔍 常见问题排查

### 问题1：数据库连接被拒绝
```
Error: connect ECONNREFUSED
```
**解决方案：**
- 检查MySQL服务是否启动
- 检查防火墙设置
- 验证数据库主机地址和端口

### 问题2：认证失败
```
Error: ER_ACCESS_DENIED_ERROR
```
**解决方案：**
- 检查数据库用户名和密码
- 确认用户有访问权限

### 问题3：数据库不存在
```
Error: ER_BAD_DB_ERROR
```
**解决方案：**
- 创建数据库：`CREATE DATABASE video_cms;`
- 运行数据库迁移脚本

## 📞 紧急修复命令

如果需要快速修复，在生产服务器上依次执行：

```bash
# 1. 进入项目目录
cd /www/wwwroot/api/backend

# 2. 备份当前配置
cp .env .env.backup

# 3. 使用生产配置
cat > .env << 'EOF'
NODE_ENV=production
PORT=3001
HOST=0.0.0.0
DB_HOST=*************
DB_PORT=3306
DB_NAME=video_cms
DB_USER=video_cms
DB_PASSWORD=86trmPEF4NpJQPF3
DB_SSL=false
REDIS_HOST=*************
REDIS_PORT=6379
REDIS_PASSWORD=86trmPEF4NpJQPF3
REDIS_DB=0
JWT_SECRET=your_super_secure_jwt_secret_key_change_this_in_production_91jspg_2024
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=5000
CORS_ORIGIN=https://91jspg.com,https://www.91jspg.com
EOF

# 4. 测试数据库连接
node src/scripts/diagnose-production.js

# 5. 初始化数据（如果需要）
node src/scripts/init-production.js

# 6. 重启服务
pm2 restart all
```

## ✅ 成功标志

修复成功后，您应该看到：

1. **日志中显示**：
   - `MySQL database connection established`
   - `LOGIN_SUCCESS` 而不是 `LOGIN_FAILED`

2. **管理员登录成功**：
   - 用户名：`admin`
   - 密码：`123456`

3. **API正常响应**：
   - 分类接口返回数据而不是500错误
   - 视频接口正常工作

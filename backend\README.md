# Jable.TV 后端API服务

## 📖 项目简介

Jable.TV 后端API服务，基于 Node.js + Express 开发，为前端提供完整的数据接口和管理功能。

## 🏗️ 技术栈

- **Node.js 18+** - 运行环境
- **Express.js** - Web框架
- **PostgreSQL** - 主数据库
- **Redis** - 缓存和会话存储
- **Winston** - 日志管理
- **JWT** - 身份认证
- **Multer** - 文件上传
- **Sharp** - 图片处理

## 📁 项目结构

```
backend/
├── src/
│   ├── app.js              # 主应用文件
│   ├── config/             # 配置文件
│   │   ├── database.js     # 数据库配置
│   │   └── redis.js        # Redis配置
│   ├── controllers/        # 控制器
│   │   └── videosController.js
│   ├── models/             # 数据模型
│   │   ├── Video.js
│   │   ├── Category.js
│   │   └── ApiKey.js
│   ├── routes/             # 路由
│   │   ├── api/
│   │   ├── upload.js
│   │   └── admin.js
│   ├── middleware/         # 中间件
│   │   ├── validate.js
│   │   ├── cache.js
│   │   ├── errorHandler.js
│   │   └── notFound.js
│   ├── utils/              # 工具函数
│   │   └── logger.js
│   └── scripts/            # 脚本文件
│       └── migrate.js      # 数据库迁移
├── uploads/                # 上传文件目录
├── logs/                   # 日志文件
├── .env.example            # 环境变量示例
├── package.json
└── README.md
```

## 🚀 快速开始

### 1. 安装依赖

```bash
cd backend
npm install
```

### 2. 环境配置

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑环境变量
nano .env
```

### 3. 数据库设置

```bash
# 创建PostgreSQL数据库
createdb jable_tv

# 运行数据库迁移
npm run migrate
```

### 4. 启动服务

```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

## 📊 数据库结构

### 主要数据表

#### videos (视频表)
```sql
CREATE TABLE videos (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  cover_url VARCHAR(500),
  video_url VARCHAR(500),
  category_id INTEGER REFERENCES categories(id),
  tags JSONB DEFAULT '[]',
  duration VARCHAR(20),
  views INTEGER DEFAULT 0,
  rating DECIMAL(3,2) DEFAULT 0,
  status VARCHAR(20) DEFAULT 'active',
  featured BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### categories (分类表)
```sql
CREATE TABLE categories (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  description TEXT,
  cover_url VARCHAR(500),
  sort_order INTEGER DEFAULT 0,
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### api_keys (API密钥表)
```sql
CREATE TABLE api_keys (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  key_value VARCHAR(100) UNIQUE NOT NULL,
  description TEXT,
  status VARCHAR(20) DEFAULT 'active',
  last_used_at TIMESTAMP,
  usage_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔌 API接口

### 前端API (无需认证)

#### 视频接口
- `GET /api/videos` - 获取视频列表
- `GET /api/videos/:id` - 获取视频详情
- `POST /api/videos/:id/view` - 增加观看次数
- `GET /api/videos/:id/recommended` - 获取推荐视频

#### 分类接口
- `GET /api/categories` - 获取分类列表
- `GET /api/categories/:id` - 获取分类详情

#### 搜索接口
- `GET /api/search` - 搜索视频

#### 排行榜接口
- `GET /api/rankings` - 获取排行榜

#### 标签接口
- `GET /api/tags` - 获取标签列表

### Python上传API (需要API密钥)

#### 视频管理
- `POST /api/upload/video` - 上传单个视频
- `POST /api/upload/batch` - 批量上传视频
- `PUT /api/upload/update/:id` - 更新视频信息
- `DELETE /api/upload/delete/:id` - 删除视频
- `GET /api/upload/stats` - 获取上传统计

### 管理后台API (需要管理员认证)

#### 仪表盘
- `GET /api/admin/dashboard` - 获取仪表盘数据

#### 视频管理
- `GET /api/admin/videos` - 获取视频列表
- `POST /api/admin/videos` - 创建视频
- `PUT /api/admin/videos/:id` - 更新视频
- `DELETE /api/admin/videos/:id` - 删除视频

#### 分类管理
- `GET /api/admin/categories` - 获取分类列表
- `POST /api/admin/categories` - 创建分类
- `PUT /api/admin/categories/:id` - 更新分类
- `DELETE /api/admin/categories/:id` - 删除分类

#### API密钥管理
- `GET /api/admin/api-keys` - 获取API密钥列表
- `POST /api/admin/api-keys` - 创建API密钥
- `PUT /api/admin/api-keys/:id` - 更新API密钥
- `DELETE /api/admin/api-keys/:id` - 删除API密钥

## 🔒 认证机制

### API密钥认证 (Python上传API)
```http
Authorization: Bearer jbl_upload_1705392847123_k8j9h2f5d3a
```

### JWT认证 (管理后台)
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 📝 环境变量

### 必需配置
```env
# 数据库
DB_HOST=localhost
DB_PORT=5432
DB_NAME=video_cms
DB_USER=postgres
DB_PASSWORD=your_password

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT
JWT_SECRET=your_super_secret_jwt_key
```

### 可选配置
```env
# 服务器
PORT=3001
NODE_ENV=development

# 文件上传
UPLOAD_DIR=uploads
MAX_FILE_SIZE=104857600

# 日志
LOG_LEVEL=info
LOG_FILE=logs/app.log
```

## 🛠️ 开发工具

### 数据库迁移
```bash
# 运行迁移
npm run migrate

# 查看迁移状态
node src/scripts/migrate.js --status
```

### 日志查看
```bash
# 查看实时日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log
```

### 缓存管理
```bash
# 清除所有缓存
redis-cli FLUSHALL

# 查看缓存键
redis-cli KEYS "cache:*"
```

## 🔧 性能优化

### 缓存策略
- **视频列表**: 5分钟缓存
- **视频详情**: 10分钟缓存
- **分类列表**: 30分钟缓存
- **排行榜**: 15分钟缓存

### 数据库优化
- 为常用查询字段添加索引
- 使用连接池管理数据库连接
- 实现读写分离（可选）

### 安全措施
- API频率限制
- 输入数据验证
- SQL注入防护
- XSS攻击防护

## 📈 监控和日志

### 日志级别
- **error**: 错误信息
- **warn**: 警告信息
- **info**: 一般信息
- **debug**: 调试信息

### 监控指标
- API响应时间
- 数据库查询性能
- 缓存命中率
- 错误率统计

## 🚀 部署指南

### Docker部署
```bash
# 构建镜像
docker build -t jable-tv-backend .

# 运行容器
docker run -p 3001:3001 jable-tv-backend
```

### PM2部署
```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start src/app.js --name jable-tv-backend

# 查看状态
pm2 status
```

## 📞 技术支持

- **项目地址**: [GitHub Repository]
- **问题反馈**: [Issues]
- **技术文档**: [API Documentation]

---

**版本**: v1.0.0  
**更新日期**: 2024-01-15

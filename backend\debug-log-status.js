// 加载环境变量
require('dotenv').config();

const CollectTask = require('./src/models/CollectTask');
const CollectLog = require('./src/models/CollectLog');
const db = require('./src/config/database');

async function debugLogStatus() {
  console.log('=== 调试日志状态更新问题 ===\n');

  try {
    // 初始化数据库连接
    console.log('初始化数据库连接...');
    await db.connect();
    console.log('✅ 数据库连接成功\n');
    // 1. 查看任务ID 13的详细信息
    console.log('1. 查看任务ID 13的详细信息...');
    const task = await CollectTask.findById(13);
    
    if (!task) {
      console.log('❌ 任务ID 13不存在');
      return;
    }
    
    console.log('任务信息:');
    console.log(`- ID: ${task.id}`);
    console.log(`- 名称: ${task.taskName}`);
    console.log(`- 状态: ${task.status}`);
    console.log(`- 采集源ID: ${task.sourceId}`);
    console.log(`- 最后运行时间: ${task.lastRunTime}`);
    console.log(`- 运行次数: ${task.runCount}`);
    console.log(`- 成功次数: ${task.successCount}`);
    console.log(`- 失败次数: ${task.failCount}`);

    // 2. 查看该采集源的所有日志
    console.log('\n2. 查看该采集源的所有日志...');
    const allLogs = await CollectLog.findAll({
      sourceId: task.sourceId,
      limit: 10
    });
    
    console.log(`采集源 ${task.sourceId} 的日志总数: ${allLogs.logs.length}`);
    
    if (allLogs.logs.length > 0) {
      console.log('\n最近的日志记录:');
      allLogs.logs.forEach((log, index) => {
        console.log(`\n日志 ${index + 1}:`);
        console.log(`- ID: ${log.id}`);
        console.log(`- 状态: ${log.status}`);
        console.log(`- 类型: ${log.type}`);
        console.log(`- 开始时间: ${log.startTime}`);
        console.log(`- 结束时间: ${log.endTime || '未结束'}`);
        console.log(`- 采集统计: 发现${log.totalFound || 0}, 采集${log.totalCollected || 0}, 跳过${log.totalSkipped || 0}, 失败${log.totalFailed || 0}`);
        console.log(`- 持续时间: ${log.duration || 'N/A'}秒`);
      });
    }

    // 3. 查看运行中的日志
    console.log('\n3. 查看运行中的日志...');
    const runningLogs = await CollectLog.findAll({
      sourceId: task.sourceId,
      status: 'running',
      limit: 5
    });
    
    console.log(`采集源 ${task.sourceId} 运行中的日志数: ${runningLogs.logs.length}`);
    
    if (runningLogs.logs.length > 0) {
      console.log('\n运行中的日志:');
      runningLogs.logs.forEach((log, index) => {
        console.log(`\n运行中日志 ${index + 1}:`);
        console.log(`- ID: ${log.id}`);
        console.log(`- 开始时间: ${log.startTime}`);
        console.log(`- 采集统计: 发现${log.totalFound || 0}, 采集${log.totalCollected || 0}, 跳过${log.totalSkipped || 0}, 失败${log.totalFailed || 0}`);
        
        // 计算运行时长
        const startTime = new Date(log.startTime);
        const now = new Date();
        const runningMinutes = Math.floor((now - startTime) / (1000 * 60));
        console.log(`- 已运行: ${runningMinutes}分钟`);
      });
    }

    // 4. 检查是否有孤立的运行中日志
    console.log('\n4. 检查所有运行中的日志...');
    const allRunningLogs = await CollectLog.findAll({
      status: 'running',
      limit: 10
    });
    
    console.log(`全系统运行中的日志数: ${allRunningLogs.logs.length}`);
    
    if (allRunningLogs.logs.length > 0) {
      console.log('\n所有运行中的日志:');
      allRunningLogs.logs.forEach((log, index) => {
        const startTime = new Date(log.startTime);
        const now = new Date();
        const runningMinutes = Math.floor((now - startTime) / (1000 * 60));
        
        console.log(`\n运行中日志 ${index + 1}:`);
        console.log(`- ID: ${log.id}, 采集源: ${log.sourceId} (${log.sourceName})`);
        console.log(`- 开始时间: ${log.startTime}`);
        console.log(`- 已运行: ${runningMinutes}分钟`);
        console.log(`- 采集统计: 发现${log.totalFound || 0}, 采集${log.totalCollected || 0}, 跳过${log.totalSkipped || 0}, 失败${log.totalFailed || 0}`);
        
        // 如果运行超过30分钟，可能是僵尸日志
        if (runningMinutes > 30) {
          console.log(`  ⚠️ 可能是僵尸日志（运行超过30分钟）`);
        }
      });
    }

    // 5. 提供修复建议
    console.log('\n=== 问题分析和修复建议 ===');
    
    if (runningLogs.logs.length > 0) {
      console.log('🔍 发现问题：');
      console.log('- 存在状态为 "running" 的日志记录');
      console.log('- 这些日志应该在任务完成时更新为 "success" 或 "failed"');
      
      console.log('\n🔧 可能的原因：');
      console.log('1. completeTask 方法没有被正确调用');
      console.log('2. 日志查询条件不够精确，找不到对应的日志');
      console.log('3. 数据库更新操作失败但没有抛出错误');
      console.log('4. 任务异常终止，没有执行完成逻辑');
      
      console.log('\n💡 修复建议：');
      console.log('1. 改进日志查询逻辑，使用更精确的条件');
      console.log('2. 添加更详细的日志记录和错误处理');
      console.log('3. 手动更新僵尸日志的状态');
      
      // 提供手动修复脚本
      const oldLogs = runningLogs.logs.filter(log => {
        const startTime = new Date(log.startTime);
        const now = new Date();
        const runningMinutes = Math.floor((now - startTime) / (1000 * 60));
        return runningMinutes > 10; // 运行超过10分钟的认为是已完成
      });
      
      if (oldLogs.length > 0) {
        console.log('\n🛠️ 手动修复脚本：');
        console.log('可以手动更新这些日志的状态：');
        oldLogs.forEach(log => {
          console.log(`UPDATE collect_logs SET status = 'success', end_time = NOW(), duration = TIMESTAMPDIFF(SECOND, start_time, NOW()) WHERE id = ${log.id};`);
        });
      }
    } else {
      console.log('✅ 没有发现运行中的日志，状态更新正常');
    }

  } catch (error) {
    console.error('调试失败:', error);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  debugLogStatus();
}

module.exports = debugLogStatus;

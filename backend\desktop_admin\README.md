# 91JSPG.COM 桌面管理应用

这是一个基于Python和tkinter开发的桌面管理应用，用于管理91JSPG.COM视频CMS系统的后台。

## 功能特性

### 🎯 核心功能
- **仪表盘** - 系统概览、统计数据、图表展示
- **视频管理** - 视频的增删改查、搜索筛选、批量操作
- **分类管理** - 视频分类的管理和排序
- **API密钥管理** - API密钥的创建、重新生成、权限管理
- **管理员管理** - 管理员账户的管理和权限控制
- **系统设置** - 应用配置、主题切换、数据导入导出

### 🎨 界面特性
- 现代化的UI设计，支持多种主题
- 响应式布局，支持窗口缩放
- 实时数据刷新和状态监控
- 直观的操作界面和用户体验

### 🔧 技术特性
- 基于Python 3.7+开发
- 使用ttkbootstrap提供现代化UI组件
- 支持数据缓存和离线操作
- 完整的错误处理和日志记录
- 配置文件持久化存储

## 安装要求

### 系统要求
- Python 3.7 或更高版本
- Windows 10/11, macOS 10.14+, 或 Linux

### 依赖包
```bash
pip install -r requirements.txt
```

主要依赖：
- `ttkbootstrap` - 现代化UI组件库
- `requests` - HTTP客户端
- `matplotlib` - 图表绘制
- `pandas` - 数据处理
- `Pillow` - 图像处理

## 快速开始

### 1. 安装依赖
```bash
cd desktop_admin
pip install -r requirements.txt
```

### 2. 启动应用
```bash
python app.py
```

或者直接运行：
```bash
python main.py
```

### 3. 配置服务器
首次启动时，需要配置服务器连接：
1. 在登录窗口点击"服务器设置"
2. 输入后端服务器地址（如：http://localhost:3001）
3. 点击"测试连接"确认连接正常
4. 输入管理员账号密码登录

## 配置说明

### 配置文件
应用配置保存在 `config.json` 文件中，包含以下设置：

```json
{
  "server_url": "http://localhost:3001",
  "admin_username": "",
  "admin_password": "",
  "remember_login": false,
  "theme": "darkly",
  "window_size": "1200x800",
  "auto_refresh": true,
  "refresh_interval": 30,
  "page_size": 20,
  "enable_cache": true,
  "cache_time": 5,
  "timeout": 30,
  "debug_mode": false,
  "log_level": "INFO"
}
```

### 主题支持
支持以下主题：
- `darkly` - 深色主题（默认）
- `flatly` - 扁平化主题
- `journal` - 期刊风格
- `litera` - 文学风格
- `lumen` - 明亮主题
- `minty` - 薄荷主题
- `pulse` - 脉冲主题
- `sandstone` - 砂岩主题
- `united` - 联合主题
- `yeti` - 雪人主题

## 使用指南

### 登录系统
1. 启动应用后会自动显示登录窗口
2. 输入管理员用户名和密码
3. 可选择"记住登录"以保存凭据
4. 点击"登录"按钮

### 仪表盘
- 显示系统概览信息
- 实时统计数据和图表
- 最近活动记录
- 系统状态监控

### 视频管理
- **查看视频列表** - 支持分页浏览
- **搜索筛选** - 按标题、分类、状态筛选
- **添加视频** - 上传新视频内容
- **编辑视频** - 修改视频信息
- **删除视频** - 删除不需要的视频
- **批量操作** - 批量删除、修改状态等

### 分类管理
- **查看分类** - 分类列表和统计信息
- **添加分类** - 创建新的视频分类
- **编辑分类** - 修改分类信息
- **排序分类** - 调整分类显示顺序
- **删除分类** - 删除空分类

### API密钥管理
- **查看密钥** - 显示所有API密钥
- **创建密钥** - 生成新的API密钥
- **重新生成** - 重新生成现有密钥
- **权限管理** - 设置密钥权限范围
- **使用统计** - 查看密钥使用情况

### 管理员管理
- **管理员列表** - 查看所有管理员账户
- **添加管理员** - 创建新的管理员账户
- **编辑权限** - 修改管理员权限
- **重置密码** - 重置管理员密码
- **启用/禁用** - 控制账户状态

### 系统设置
- **服务器设置** - 配置后端服务器连接
- **界面设置** - 主题、字体、刷新设置
- **数据设置** - 分页、缓存配置
- **高级设置** - 调试模式、日志级别
- **配置导入导出** - 备份和恢复配置

## 故障排除

### 常见问题

**1. 无法连接服务器**
- 检查服务器地址是否正确
- 确认后端服务是否正常运行
- 检查网络连接和防火墙设置

**2. 登录失败**
- 确认用户名和密码正确
- 检查管理员账户是否被禁用
- 查看后端日志确认错误原因

**3. 界面显示异常**
- 尝试切换不同主题
- 检查屏幕分辨率和缩放设置
- 重启应用程序

**4. 数据加载缓慢**
- 启用数据缓存功能
- 减少每页显示数量
- 检查网络连接速度

### 日志查看
应用运行时会生成日志文件，可以通过以下方式查看：
- 启用调试模式查看详细日志
- 检查控制台输出信息
- 查看系统错误对话框

## 开发说明

### 项目结构
```
desktop_admin/
├── app.py              # 主启动脚本
├── main.py             # 主应用程序
├── config.py           # 配置管理
├── api_client.py       # API客户端
├── login_window.py     # 登录窗口
├── dashboard_frame.py  # 仪表盘框架
├── videos_frame.py     # 视频管理框架
├── categories_frame.py # 分类管理框架
├── api_keys_frame.py   # API密钥管理框架
├── admins_frame.py     # 管理员管理框架
├── settings_frame.py   # 设置框架
├── requirements.txt    # 依赖包列表
├── config.json         # 配置文件（运行时生成）
└── README.md          # 说明文档
```

### 扩展开发
如需添加新功能：
1. 创建新的框架类继承基础框架
2. 在main.py中注册新框架
3. 添加导航按钮和路由
4. 实现相应的API接口调用

## 许可证

本项目仅供学习和研究使用。

## 支持

如有问题或建议，请联系开发团队。

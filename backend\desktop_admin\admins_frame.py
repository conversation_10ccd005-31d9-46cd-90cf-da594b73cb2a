# 管理员管理框架
import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import threading
from datetime import datetime

from api_client import api_client

class AdminsFrame:
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.frame = None
        self.is_visible = False
        self.admins_data = []
        self.current_page = 1
        self.total_pages = 1
        self.page_size = 20
        
        self.create_interface()
    
    def create_interface(self):
        """创建管理员管理界面"""
        self.frame = ttk_bs.Frame(self.parent)
        
        # 标题和工具栏
        self.create_toolbar()
        
        # 管理员列表
        self.create_admins_list()
        
        # 分页控件
        self.create_pagination()
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar_frame = ttk_bs.Frame(self.frame)
        toolbar_frame.pack(fill=X, padx=20, pady=20)
        
        # 标题
        ttk_bs.Label(
            toolbar_frame,
            text="管理员管理",
            font=("Arial", 18, "bold"),
            bootstyle="primary"
        ).pack(side=LEFT)
        
        # 右侧按钮组
        btn_frame = ttk_bs.Frame(toolbar_frame)
        btn_frame.pack(side=RIGHT)
        
        # 添加管理员按钮
        add_btn = ttk_bs.Button(
            btn_frame,
            text="+ 添加管理员",
            bootstyle="success",
            command=self.add_admin
        )
        add_btn.pack(side=RIGHT, padx=5)
        
        # 刷新按钮
        refresh_btn = ttk_bs.Button(
            btn_frame,
            text="刷新",
            bootstyle="outline-primary",
            command=self.refresh
        )
        refresh_btn.pack(side=RIGHT, padx=5)
    
    def create_admins_list(self):
        """创建管理员列表"""
        list_frame = ttk_bs.Frame(self.frame)
        list_frame.pack(fill=BOTH, expand=True, padx=20, pady=(0, 20))
        
        # 创建Treeview
        columns = ("id", "username", "email", "role", "permissions", "last_login", "status", "created_at")
        self.admins_tree = ttk_bs.Treeview(
            list_frame,
            columns=columns,
            show="headings",
            height=15
        )
        
        # 设置列标题和宽度
        column_config = {
            "id": ("ID", 60),
            "username": ("用户名", 120),
            "email": ("邮箱", 200),
            "role": ("角色", 100),
            "permissions": ("权限", 200),
            "last_login": ("最后登录", 150),
            "status": ("状态", 80),
            "created_at": ("创建时间", 150)
        }
        
        for col, (text, width) in column_config.items():
            self.admins_tree.heading(col, text=text)
            self.admins_tree.column(col, width=width)
        
        # 添加滚动条
        v_scrollbar = tk.Scrollbar(list_frame, orient="vertical", command=self.admins_tree.yview)
        h_scrollbar = tk.Scrollbar(list_frame, orient="horizontal", command=self.admins_tree.xview)
        self.admins_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.admins_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定双击事件
        self.admins_tree.bind("<Double-1>", self.on_admin_double_click)
        
        # 绑定右键菜单
        self.admins_tree.bind("<Button-3>", self.show_context_menu)
        
        # 创建右键菜单
        self.context_menu = tk.Menu(self.admins_tree, tearoff=0)
        self.context_menu.add_command(label="编辑", command=self.edit_admin)
        self.context_menu.add_command(label="重置密码", command=self.reset_password)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="启用/禁用", command=self.toggle_admin_status)
        self.context_menu.add_command(label="删除", command=self.delete_admin)
    
    def create_pagination(self):
        """创建分页控件"""
        pagination_frame = ttk_bs.Frame(self.frame)
        pagination_frame.pack(fill=X, padx=20, pady=(0, 20))
        
        # 左侧：显示信息
        info_frame = ttk_bs.Frame(pagination_frame)
        info_frame.pack(side=LEFT)
        
        self.info_label = ttk_bs.Label(info_frame, text="")
        self.info_label.pack()
        
        # 右侧：分页按钮
        nav_frame = ttk_bs.Frame(pagination_frame)
        nav_frame.pack(side=RIGHT)
        
        # 首页
        first_btn = ttk_bs.Button(
            nav_frame,
            text="首页",
            bootstyle="outline-primary",
            command=lambda: self.go_to_page(1)
        )
        first_btn.pack(side=LEFT, padx=2)
        
        # 上一页
        self.prev_btn = ttk_bs.Button(
            nav_frame,
            text="上一页",
            bootstyle="outline-primary",
            command=self.prev_page
        )
        self.prev_btn.pack(side=LEFT, padx=2)
        
        # 页码输入
        ttk_bs.Label(nav_frame, text="第").pack(side=LEFT, padx=(10, 2))
        self.page_var = tk.StringVar(value="1")
        page_entry = ttk_bs.Entry(nav_frame, textvariable=self.page_var, width=5)
        page_entry.pack(side=LEFT, padx=2)
        page_entry.bind('<Return>', lambda e: self.go_to_page(int(self.page_var.get() or 1)))
        
        self.total_pages_label = ttk_bs.Label(nav_frame, text="/ 1 页")
        self.total_pages_label.pack(side=LEFT, padx=2)
        
        # 下一页
        self.next_btn = ttk_bs.Button(
            nav_frame,
            text="下一页",
            bootstyle="outline-primary",
            command=self.next_page
        )
        self.next_btn.pack(side=LEFT, padx=2)
        
        # 末页
        last_btn = ttk_bs.Button(
            nav_frame,
            text="末页",
            bootstyle="outline-primary",
            command=lambda: self.go_to_page(self.total_pages)
        )
        last_btn.pack(side=LEFT, padx=2)
    
    def show(self):
        """显示框架"""
        if not self.is_visible:
            self.frame.pack(fill=BOTH, expand=True)
            self.is_visible = True
            self.refresh()
    
    def hide(self):
        """隐藏框架"""
        if self.is_visible:
            self.frame.pack_forget()
            self.is_visible = False
    
    def refresh(self):
        """刷新管理员列表"""
        if not self.is_visible:
            return
        
        self.load_admins()
    
    def load_admins(self):
        """加载管理员数据"""
        def fetch_admins():
            try:
                result = api_client.get_admins(
                    page=self.current_page,
                    limit=self.page_size
                )
                
                if result.get("success"):
                    data = result.get("data", {})
                    self.admins_data = data.get("admins", [])
                    pagination = data.get("pagination", {})
                    self.total_pages = pagination.get("total_pages", 1)
                    
                    self.parent.after(0, self.update_admins_list)
                else:
                    self.main_app.set_status(f"加载管理员失败: {result.get('message', '未知错误')}")
            except Exception as e:
                self.main_app.set_status(f"加载管理员失败: {str(e)}")
        
        threading.Thread(target=fetch_admins, daemon=True).start()
    
    def update_admins_list(self):
        """更新管理员列表显示"""
        # 清除现有数据
        for item in self.admins_tree.get_children():
            self.admins_tree.delete(item)
        
        # 添加新数据
        for admin in self.admins_data:
            # 格式化数据
            permissions = ", ".join(admin.get("permissions", []))
            last_login = self.format_datetime(admin.get("last_login", ""))
            created_at = self.format_datetime(admin.get("created_at", ""))
            
            self.admins_tree.insert("", "end", values=(
                admin.get("id", ""),
                admin.get("username", ""),
                admin.get("email", ""),
                admin.get("role", ""),
                permissions,
                last_login,
                admin.get("status", ""),
                created_at
            ))
        
        # 更新分页信息
        self.update_pagination_info()
    
    def format_datetime(self, dt_str):
        """格式化日期时间"""
        if not dt_str:
            return "从未登录"
        try:
            dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
            return dt.strftime("%Y-%m-%d %H:%M")
        except:
            return dt_str
    
    def update_pagination_info(self):
        """更新分页信息"""
        total_items = len(self.admins_data)
        start_item = (self.current_page - 1) * self.page_size + 1
        end_item = min(start_item + total_items - 1, self.current_page * self.page_size)
        
        self.info_label.config(text=f"显示 {start_item}-{end_item} 项")
        self.page_var.set(str(self.current_page))
        self.total_pages_label.config(text=f"/ {self.total_pages} 页")
        
        # 更新按钮状态
        self.prev_btn.config(state="normal" if self.current_page > 1 else "disabled")
        self.next_btn.config(state="normal" if self.current_page < self.total_pages else "disabled")
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.load_admins()
    
    def next_page(self):
        """下一页"""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.load_admins()
    
    def go_to_page(self, page):
        """跳转到指定页"""
        if 1 <= page <= self.total_pages:
            self.current_page = page
            self.load_admins()
    
    def on_admin_double_click(self, event):
        """双击管理员事件"""
        self.edit_admin()
    
    def show_context_menu(self, event):
        """显示右键菜单"""
        item = self.admins_tree.selection()
        if item:
            self.context_menu.post(event.x_root, event.y_root)
    
    def edit_admin(self):
        """编辑管理员"""
        selection = self.admins_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个管理员")
            return
        
        item = self.admins_tree.item(selection[0])
        admin_id = item['values'][0]
        
        # TODO: 实现管理员编辑窗口
        messagebox.showinfo("提示", f"编辑管理员 ID: {admin_id}")
    
    def reset_password(self):
        """重置密码"""
        selection = self.admins_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个管理员")
            return
        
        item = self.admins_tree.item(selection[0])
        admin_id = item['values'][0]
        admin_username = item['values'][1]
        
        # TODO: 实现密码重置功能
        messagebox.showinfo("提示", f"重置管理员 '{admin_username}' 的密码")
    
    def toggle_admin_status(self):
        """切换管理员状态"""
        selection = self.admins_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个管理员")
            return
        
        item = self.admins_tree.item(selection[0])
        admin_id = item['values'][0]
        admin_username = item['values'][1]
        current_status = item['values'][6]
        
        new_status = "inactive" if current_status == "active" else "active"
        action = "禁用" if new_status == "inactive" else "启用"
        
        if messagebox.askyesno("确认操作", f"确定要{action}管理员 '{admin_username}' 吗？"):
            def do_toggle():
                try:
                    result = api_client.update_admin(admin_id, {"status": new_status})
                    if result.get("success"):
                        self.parent.after(0, lambda: [
                            self.main_app.set_status(f"管理员{action}成功"),
                            self.refresh()
                        ])
                    else:
                        self.parent.after(0, lambda: messagebox.showerror(
                            f"{action}失败", 
                            result.get('message', '未知错误')
                        ))
                except Exception as e:
                    self.parent.after(0, lambda: messagebox.showerror(
                        f"{action}失败", 
                        f"{action}管理员时发生错误: {str(e)}"
                    ))
            
            threading.Thread(target=do_toggle, daemon=True).start()
    
    def delete_admin(self):
        """删除管理员"""
        selection = self.admins_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个管理员")
            return
        
        item = self.admins_tree.item(selection[0])
        admin_id = item['values'][0]
        admin_username = item['values'][1]
        
        # 检查是否是当前登录的管理员
        current_admin = self.main_app.current_admin
        if current_admin and str(current_admin.get("id")) == str(admin_id):
            messagebox.showwarning("警告", "不能删除当前登录的管理员账户！")
            return
        
        if messagebox.askyesno("确认删除", f"确定要删除管理员 '{admin_username}' 吗？\n此操作不可撤销！"):
            def do_delete():
                try:
                    result = api_client.delete_admin(admin_id)
                    if result.get("success"):
                        self.parent.after(0, lambda: [
                            self.main_app.set_status("管理员删除成功"),
                            self.refresh()
                        ])
                    else:
                        self.parent.after(0, lambda: messagebox.showerror(
                            "删除失败", 
                            result.get('message', '未知错误')
                        ))
                except Exception as e:
                    self.parent.after(0, lambda: messagebox.showerror(
                        "删除失败", 
                        f"删除管理员时发生错误: {str(e)}"
                    ))
            
            threading.Thread(target=do_delete, daemon=True).start()
    
    def add_admin(self):
        """添加管理员"""
        # TODO: 实现添加管理员窗口
        messagebox.showinfo("提示", "添加管理员功能开发中...")

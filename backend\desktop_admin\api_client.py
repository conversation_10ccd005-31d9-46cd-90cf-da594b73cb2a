# API客户端
import requests
import json
from typing import Dict, Any, Optional, List
from config import config

class APIClient:
    def __init__(self):
        self.base_url = config.get("server_url", "http://localhost:3001")
        self.token = None
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': '91JSPG Desktop Admin v1.0'
        })
    
    def set_base_url(self, url: str):
        """设置服务器地址"""
        self.base_url = url.rstrip('/')
        config.set("server_url", self.base_url)
    
    def set_token(self, token: str):
        """设置认证令牌"""
        self.token = token
        self.session.headers.update({
            'Authorization': f'Bearer {token}'
        })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()

            # 尝试解析JSON
            try:
                return response.json()
            except json.JSONDecodeError:
                return {
                    "success": False,
                    "message": "服务器返回无效的JSON数据",
                    "status_code": response.status_code,
                    "response_text": response.text[:200]  # 只显示前200个字符
                }

        except requests.exceptions.ConnectionError as e:
            return {
                "success": False,
                "message": f"连接失败: 无法连接到服务器 {self.base_url}"
            }
        except requests.exceptions.Timeout as e:
            return {
                "success": False,
                "message": "请求超时: 服务器响应时间过长"
            }
        except requests.exceptions.HTTPError as e:
            return {
                "success": False,
                "message": f"HTTP错误: {e.response.status_code} - {e.response.reason}",
                "status_code": e.response.status_code
            }
        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "message": f"请求失败: {str(e)}"
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"未知错误: {str(e)}"
            }
    
    # 认证相关
    def login(self, username: str, password: str) -> Dict[str, Any]:
        """管理员登录"""
        data = {
            "username": username,
            "password": password
        }
        result = self._make_request('POST', '/api/admin/login', json=data)
        if result.get('success') and result.get('data', {}).get('token'):
            self.set_token(result['data']['token'])
        return result
    
    def get_current_admin(self) -> Dict[str, Any]:
        """获取当前管理员信息"""
        return self._make_request('GET', '/api/admin/me')
    
    def change_password(self, old_password: str, new_password: str) -> Dict[str, Any]:
        """修改密码"""
        data = {
            "old_password": old_password,
            "new_password": new_password
        }
        return self._make_request('POST', '/api/admin/change-password', json=data)
    
    # 仪表盘
    def get_dashboard(self) -> Dict[str, Any]:
        """获取仪表盘数据"""
        return self._make_request('GET', '/api/admin/dashboard')
    
    # 视频管理
    def get_videos(self, page: int = 1, limit: int = 20, search: str = "", category: str = "") -> Dict[str, Any]:
        """获取视频列表"""
        params = {"page": page, "limit": limit}
        if search:
            params["search"] = search
        if category:
            params["category"] = category
        return self._make_request('GET', '/api/admin/videos', params=params)
    
    def get_video_by_id(self, video_id: int) -> Dict[str, Any]:
        """获取视频详情"""
        return self._make_request('GET', f'/api/admin/videos/{video_id}')
    
    def create_video(self, video_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建视频"""
        return self._make_request('POST', '/api/admin/videos', json=video_data)
    
    def update_video(self, video_id: int, video_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新视频"""
        return self._make_request('PUT', f'/api/admin/videos/{video_id}', json=video_data)
    
    def delete_video(self, video_id: int) -> Dict[str, Any]:
        """删除视频"""
        return self._make_request('DELETE', f'/api/admin/videos/{video_id}')
    
    def get_video_stats(self) -> Dict[str, Any]:
        """获取视频统计"""
        return self._make_request('GET', '/api/admin/videos/stats')
    
    # 分类管理
    def get_categories(self, page: int = 1, limit: int = 20) -> Dict[str, Any]:
        """获取分类列表"""
        params = {"page": page, "limit": limit}
        return self._make_request('GET', '/api/admin/categories', params=params)
    
    def get_category_by_id(self, category_id: int) -> Dict[str, Any]:
        """获取分类详情"""
        return self._make_request('GET', f'/api/admin/categories/{category_id}')
    
    def create_category(self, category_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建分类"""
        return self._make_request('POST', '/api/admin/categories', json=category_data)
    
    def update_category(self, category_id: int, category_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新分类"""
        return self._make_request('PUT', f'/api/admin/categories/{category_id}', json=category_data)
    
    def delete_category(self, category_id: int) -> Dict[str, Any]:
        """删除分类"""
        return self._make_request('DELETE', f'/api/admin/categories/{category_id}')
    
    def get_category_stats(self) -> Dict[str, Any]:
        """获取分类统计"""
        return self._make_request('GET', '/api/admin/categories/stats')
    
    # API密钥管理
    def get_api_keys(self, page: int = 1, limit: int = 20) -> Dict[str, Any]:
        """获取API密钥列表"""
        params = {"page": page, "limit": limit}
        return self._make_request('GET', '/api/admin/api-keys', params=params)
    
    def create_api_key(self, api_key_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建API密钥"""
        return self._make_request('POST', '/api/admin/api-keys', json=api_key_data)
    
    def update_api_key(self, key_id: int, api_key_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新API密钥"""
        return self._make_request('PUT', f'/api/admin/api-keys/{key_id}', json=api_key_data)
    
    def delete_api_key(self, key_id: int) -> Dict[str, Any]:
        """删除API密钥"""
        return self._make_request('DELETE', f'/api/admin/api-keys/{key_id}')
    
    def regenerate_api_key(self, key_id: int) -> Dict[str, Any]:
        """重新生成API密钥"""
        return self._make_request('POST', f'/api/admin/api-keys/{key_id}/regenerate')
    
    # 健康检查
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return self._make_request('GET', '/health')

    # 管理员管理
    def get_admins(self, page: int = 1, limit: int = 20) -> Dict[str, Any]:
        """获取管理员列表"""
        params = {"page": page, "limit": limit}
        return self._make_request('GET', '/api/admin/admins', params=params)

    def create_admin(self, admin_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建管理员"""
        return self._make_request('POST', '/api/admin/admins', json=admin_data)

    def update_admin(self, admin_id: int, admin_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新管理员"""
        return self._make_request('PUT', f'/api/admin/admins/{admin_id}', json=admin_data)

    def delete_admin(self, admin_id: int) -> Dict[str, Any]:
        """删除管理员"""
        return self._make_request('DELETE', f'/api/admin/admins/{admin_id}')

# 全局API客户端实例
api_client = APIClient()

# API密钥管理框架
import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import threading
from datetime import datetime

from api_client import api_client

class ApiKeysFrame:
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.frame = None
        self.is_visible = False
        self.api_keys_data = []
        self.current_page = 1
        self.total_pages = 1
        self.page_size = 20
        
        self.create_interface()
    
    def create_interface(self):
        """创建API密钥管理界面"""
        self.frame = ttk_bs.Frame(self.parent)
        
        # 标题和工具栏
        self.create_toolbar()
        
        # API密钥列表
        self.create_api_keys_list()
        
        # 分页控件
        self.create_pagination()
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar_frame = ttk_bs.Frame(self.frame)
        toolbar_frame.pack(fill=X, padx=20, pady=20)
        
        # 标题
        ttk_bs.Label(
            toolbar_frame,
            text="API密钥管理",
            font=("Arial", 18, "bold"),
            bootstyle="primary"
        ).pack(side=LEFT)
        
        # 右侧按钮组
        btn_frame = ttk_bs.Frame(toolbar_frame)
        btn_frame.pack(side=RIGHT)
        
        # 创建API密钥按钮
        add_btn = ttk_bs.Button(
            btn_frame,
            text="+ 创建密钥",
            bootstyle="success",
            command=self.create_api_key
        )
        add_btn.pack(side=RIGHT, padx=5)
        
        # 刷新按钮
        refresh_btn = ttk_bs.Button(
            btn_frame,
            text="刷新",
            bootstyle="outline-primary",
            command=self.refresh
        )
        refresh_btn.pack(side=RIGHT, padx=5)
    
    def create_api_keys_list(self):
        """创建API密钥列表"""
        list_frame = ttk_bs.Frame(self.frame)
        list_frame.pack(fill=BOTH, expand=True, padx=20, pady=(0, 20))
        
        # 创建Treeview
        columns = ("id", "name", "key_preview", "permissions", "requests_count", "status", "expires_at", "created_at")
        self.api_keys_tree = ttk_bs.Treeview(
            list_frame,
            columns=columns,
            show="headings",
            height=15
        )
        
        # 设置列标题和宽度
        column_config = {
            "id": ("ID", 60),
            "name": ("名称", 150),
            "key_preview": ("密钥预览", 200),
            "permissions": ("权限", 150),
            "requests_count": ("请求次数", 100),
            "status": ("状态", 80),
            "expires_at": ("过期时间", 150),
            "created_at": ("创建时间", 150)
        }
        
        for col, (text, width) in column_config.items():
            self.api_keys_tree.heading(col, text=text)
            self.api_keys_tree.column(col, width=width)
        
        # 添加滚动条
        v_scrollbar = ttk_bs.Scrollbar(list_frame, orient="vertical", command=self.api_keys_tree.yview)
        h_scrollbar = ttk_bs.Scrollbar(list_frame, orient="horizontal", command=self.api_keys_tree.xview)
        self.api_keys_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.api_keys_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定双击事件
        self.api_keys_tree.bind("<Double-1>", self.on_api_key_double_click)
        
        # 绑定右键菜单
        self.api_keys_tree.bind("<Button-3>", self.show_context_menu)
        
        # 创建右键菜单
        self.context_menu = tk.Menu(self.api_keys_tree, tearoff=0)
        self.context_menu.add_command(label="查看详情", command=self.view_api_key_details)
        self.context_menu.add_command(label="编辑", command=self.edit_api_key)
        self.context_menu.add_command(label="重新生成", command=self.regenerate_api_key)
        self.context_menu.add_command(label="查看统计", command=self.view_api_key_stats)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="删除", command=self.delete_api_key)
    
    def create_pagination(self):
        """创建分页控件"""
        pagination_frame = ttk_bs.Frame(self.frame)
        pagination_frame.pack(fill=X, padx=20, pady=(0, 20))
        
        # 左侧：显示信息
        info_frame = ttk_bs.Frame(pagination_frame)
        info_frame.pack(side=LEFT)
        
        self.info_label = ttk_bs.Label(info_frame, text="")
        self.info_label.pack()
        
        # 右侧：分页按钮
        nav_frame = ttk_bs.Frame(pagination_frame)
        nav_frame.pack(side=RIGHT)
        
        # 首页
        first_btn = ttk_bs.Button(
            nav_frame,
            text="首页",
            bootstyle="outline-primary",
            command=lambda: self.go_to_page(1)
        )
        first_btn.pack(side=LEFT, padx=2)
        
        # 上一页
        self.prev_btn = ttk_bs.Button(
            nav_frame,
            text="上一页",
            bootstyle="outline-primary",
            command=self.prev_page
        )
        self.prev_btn.pack(side=LEFT, padx=2)
        
        # 页码输入
        ttk_bs.Label(nav_frame, text="第").pack(side=LEFT, padx=(10, 2))
        self.page_var = tk.StringVar(value="1")
        page_entry = ttk_bs.Entry(nav_frame, textvariable=self.page_var, width=5)
        page_entry.pack(side=LEFT, padx=2)
        page_entry.bind('<Return>', lambda e: self.go_to_page(int(self.page_var.get() or 1)))
        
        self.total_pages_label = ttk_bs.Label(nav_frame, text="/ 1 页")
        self.total_pages_label.pack(side=LEFT, padx=2)
        
        # 下一页
        self.next_btn = ttk_bs.Button(
            nav_frame,
            text="下一页",
            bootstyle="outline-primary",
            command=self.next_page
        )
        self.next_btn.pack(side=LEFT, padx=2)
        
        # 末页
        last_btn = ttk_bs.Button(
            nav_frame,
            text="末页",
            bootstyle="outline-primary",
            command=lambda: self.go_to_page(self.total_pages)
        )
        last_btn.pack(side=LEFT, padx=2)
    
    def show(self):
        """显示框架"""
        if not self.is_visible:
            self.frame.pack(fill=BOTH, expand=True)
            self.is_visible = True
            self.refresh()
    
    def hide(self):
        """隐藏框架"""
        if self.is_visible:
            self.frame.pack_forget()
            self.is_visible = False
    
    def refresh(self):
        """刷新API密钥列表"""
        if not self.is_visible:
            return
        
        self.load_api_keys()
    
    def load_api_keys(self):
        """加载API密钥数据"""
        def fetch_api_keys():
            try:
                result = api_client.get_api_keys(
                    page=self.current_page,
                    limit=self.page_size
                )
                
                if result.get("success"):
                    data = result.get("data", {})
                    self.api_keys_data = data.get("api_keys", [])
                    pagination = data.get("pagination", {})
                    self.total_pages = pagination.get("total_pages", 1)
                    
                    self.parent.after(0, self.update_api_keys_list)
                else:
                    self.main_app.set_status(f"加载API密钥失败: {result.get('message', '未知错误')}")
            except Exception as e:
                self.main_app.set_status(f"加载API密钥失败: {str(e)}")
        
        threading.Thread(target=fetch_api_keys, daemon=True).start()
    
    def update_api_keys_list(self):
        """更新API密钥列表显示"""
        # 清除现有数据
        for item in self.api_keys_tree.get_children():
            self.api_keys_tree.delete(item)
        
        # 添加新数据
        for api_key in self.api_keys_data:
            # 格式化数据
            key_preview = self.format_key_preview(api_key.get("key", ""))
            permissions = ", ".join(api_key.get("permissions", []))
            expires_at = self.format_datetime(api_key.get("expires_at", ""))
            created_at = self.format_datetime(api_key.get("created_at", ""))
            
            self.api_keys_tree.insert("", "end", values=(
                api_key.get("id", ""),
                api_key.get("name", ""),
                key_preview,
                permissions,
                api_key.get("requests_count", 0),
                api_key.get("status", ""),
                expires_at,
                created_at
            ))
        
        # 更新分页信息
        self.update_pagination_info()
    
    def format_key_preview(self, key):
        """格式化密钥预览"""
        if not key:
            return ""
        if len(key) > 20:
            return f"{key[:10]}...{key[-6:]}"
        return key
    
    def format_datetime(self, dt_str):
        """格式化日期时间"""
        if not dt_str:
            return "永不过期"
        try:
            dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
            return dt.strftime("%Y-%m-%d %H:%M")
        except:
            return dt_str
    
    def update_pagination_info(self):
        """更新分页信息"""
        total_items = len(self.api_keys_data)
        start_item = (self.current_page - 1) * self.page_size + 1
        end_item = min(start_item + total_items - 1, self.current_page * self.page_size)
        
        self.info_label.config(text=f"显示 {start_item}-{end_item} 项")
        self.page_var.set(str(self.current_page))
        self.total_pages_label.config(text=f"/ {self.total_pages} 页")
        
        # 更新按钮状态
        self.prev_btn.config(state="normal" if self.current_page > 1 else "disabled")
        self.next_btn.config(state="normal" if self.current_page < self.total_pages else "disabled")
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.load_api_keys()
    
    def next_page(self):
        """下一页"""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.load_api_keys()
    
    def go_to_page(self, page):
        """跳转到指定页"""
        if 1 <= page <= self.total_pages:
            self.current_page = page
            self.load_api_keys()
    
    def on_api_key_double_click(self, event):
        """双击API密钥事件"""
        self.view_api_key_details()
    
    def show_context_menu(self, event):
        """显示右键菜单"""
        item = self.api_keys_tree.selection()
        if item:
            self.context_menu.post(event.x_root, event.y_root)
    
    def view_api_key_details(self):
        """查看API密钥详情"""
        selection = self.api_keys_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个API密钥")
            return
        
        item = self.api_keys_tree.item(selection[0])
        api_key_id = item['values'][0]
        
        # TODO: 实现API密钥详情窗口
        messagebox.showinfo("提示", f"查看API密钥详情 ID: {api_key_id}")
    
    def edit_api_key(self):
        """编辑API密钥"""
        selection = self.api_keys_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个API密钥")
            return
        
        item = self.api_keys_tree.item(selection[0])
        api_key_id = item['values'][0]
        
        # TODO: 实现API密钥编辑窗口
        messagebox.showinfo("提示", f"编辑API密钥 ID: {api_key_id}")
    
    def regenerate_api_key(self):
        """重新生成API密钥"""
        selection = self.api_keys_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个API密钥")
            return
        
        item = self.api_keys_tree.item(selection[0])
        api_key_id = item['values'][0]
        api_key_name = item['values'][1]
        
        if messagebox.askyesno("确认重新生成", f"确定要重新生成API密钥 '{api_key_name}' 吗？\n旧密钥将立即失效！"):
            def do_regenerate():
                try:
                    result = api_client.regenerate_api_key(api_key_id)
                    if result.get("success"):
                        new_key = result.get("data", {}).get("key", "")
                        self.parent.after(0, lambda: [
                            self.show_new_key_dialog(new_key),
                            self.refresh()
                        ])
                    else:
                        self.parent.after(0, lambda: messagebox.showerror(
                            "重新生成失败", 
                            result.get('message', '未知错误')
                        ))
                except Exception as e:
                    self.parent.after(0, lambda: messagebox.showerror(
                        "重新生成失败", 
                        f"重新生成API密钥时发生错误: {str(e)}"
                    ))
            
            threading.Thread(target=do_regenerate, daemon=True).start()
    
    def show_new_key_dialog(self, new_key):
        """显示新密钥对话框"""
        dialog = ttk_bs.Toplevel(self.parent)
        dialog.title("新API密钥")
        dialog.geometry("500x300")
        dialog.resizable(False, False)
        dialog.transient(self.parent)
        dialog.grab_set()
        
        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")
        
        # 内容
        main_frame = ttk_bs.Frame(dialog)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        ttk_bs.Label(
            main_frame,
            text="新API密钥已生成",
            font=("Arial", 14, "bold"),
            bootstyle="success"
        ).pack(pady=(0, 20))
        
        ttk_bs.Label(
            main_frame,
            text="请立即复制并保存此密钥，它只会显示一次：",
            bootstyle="warning"
        ).pack(pady=(0, 10))
        
        # 密钥显示框
        key_frame = ttk_bs.Frame(main_frame)
        key_frame.pack(fill=X, pady=(0, 20))
        
        key_text = tk.Text(key_frame, height=3, wrap=tk.WORD)
        key_text.insert("1.0", new_key)
        key_text.config(state="disabled")
        key_text.pack(fill=X)
        
        # 按钮
        btn_frame = ttk_bs.Frame(main_frame)
        btn_frame.pack(fill=X)
        
        def copy_key():
            dialog.clipboard_clear()
            dialog.clipboard_append(new_key)
            messagebox.showinfo("成功", "密钥已复制到剪贴板")
        
        ttk_bs.Button(
            btn_frame,
            text="复制密钥",
            bootstyle="primary",
            command=copy_key
        ).pack(side=LEFT, padx=(0, 10))
        
        ttk_bs.Button(
            btn_frame,
            text="关闭",
            bootstyle="secondary",
            command=dialog.destroy
        ).pack(side=LEFT)
    
    def view_api_key_stats(self):
        """查看API密钥统计"""
        selection = self.api_keys_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个API密钥")
            return
        
        item = self.api_keys_tree.item(selection[0])
        api_key_id = item['values'][0]
        
        # TODO: 实现API密钥统计窗口
        messagebox.showinfo("提示", f"查看API密钥统计 ID: {api_key_id}")
    
    def delete_api_key(self):
        """删除API密钥"""
        selection = self.api_keys_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个API密钥")
            return
        
        item = self.api_keys_tree.item(selection[0])
        api_key_id = item['values'][0]
        api_key_name = item['values'][1]
        
        if messagebox.askyesno("确认删除", f"确定要删除API密钥 '{api_key_name}' 吗？\n此操作不可撤销！"):
            def do_delete():
                try:
                    result = api_client.delete_api_key(api_key_id)
                    if result.get("success"):
                        self.parent.after(0, lambda: [
                            self.main_app.set_status("API密钥删除成功"),
                            self.refresh()
                        ])
                    else:
                        self.parent.after(0, lambda: messagebox.showerror(
                            "删除失败", 
                            result.get('message', '未知错误')
                        ))
                except Exception as e:
                    self.parent.after(0, lambda: messagebox.showerror(
                        "删除失败", 
                        f"删除API密钥时发生错误: {str(e)}"
                    ))
            
            threading.Thread(target=do_delete, daemon=True).start()
    
    def create_api_key(self):
        """创建API密钥"""
        # TODO: 实现创建API密钥窗口
        messagebox.showinfo("提示", "创建API密钥功能开发中...")

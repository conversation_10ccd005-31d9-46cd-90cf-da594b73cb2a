#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
91JSPG.COM 视频CMS桌面管理应用
主启动脚本
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# 禁用DPI设置以避免Windows错误
os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '0'
os.environ['QT_SCALE_FACTOR'] = '1'

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def check_dependencies():
    """检查依赖包"""
    missing_packages = []
    optional_packages = []

    # 必需的依赖包
    try:
        import ttkbootstrap
        print("✓ ttkbootstrap 已加载")
    except ImportError:
        missing_packages.append("ttkbootstrap")

    try:
        import requests
        print("✓ requests 已加载")
    except ImportError:
        missing_packages.append("requests")

    try:
        from PIL import Image
        print("✓ Pillow 已加载")
    except ImportError:
        missing_packages.append("Pillow")

    # 可选的依赖包（用于图表功能）
    try:
        import matplotlib
        print("✓ matplotlib 已加载")
    except ImportError:
        optional_packages.append("matplotlib")

    try:
        import pandas
        print("✓ pandas 已加载")
    except ImportError:
        optional_packages.append("pandas")

    if missing_packages:
        error_msg = f"""
缺少必要的依赖包，请先安装：

pip install {' '.join(missing_packages)}

或者运行：
pip install -r requirements_minimal.txt
"""
        messagebox.showerror("依赖错误", error_msg)
        return False

    if optional_packages:
        print(f"⚠ 可选依赖包未安装: {', '.join(optional_packages)}")
        print("  图表功能可能受限，但应用仍可正常使用")

    return True

def main():
    """主函数"""
    try:
        # 检查依赖
        if not check_dependencies():
            sys.exit(1)

        print("正在启动应用...")

        # 检查是否有保存的登录状态
        from config import config

        # 导入主应用
        from main import VideoAdminApp

        print("创建应用实例...")
        # 创建并运行应用
        app = VideoAdminApp()

        # 如果有保存的登录状态，设置登录信息
        if config.get("is_logged_in") and config.get("current_admin"):
            print("检测到已保存的登录状态，自动登录...")
            app.current_admin = config.get("current_admin")
            app.is_logged_in = True
            # 清除保存的登录状态（一次性使用）
            config.set("is_logged_in", False)
            config.set("current_admin", None)

        print("启动应用界面...")
        app.run()

    except ImportError as e:
        error_msg = f"导入模块失败: {str(e)}\n\n请确保所有文件都在正确的位置。"
        print(f"❌ {error_msg}")
        try:
            messagebox.showerror("导入错误", error_msg)
        except:
            pass
        sys.exit(1)

    except Exception as e:
        error_msg = f"应用启动失败: {str(e)}\n\n详细错误信息:\n{traceback.format_exc()}"
        print(f"❌ {error_msg}")
        try:
            messagebox.showerror("启动错误", error_msg)
        except:
            pass
        sys.exit(1)

if __name__ == "__main__":
    main()

# 分类管理框架
import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import threading
from datetime import datetime

from api_client import api_client

class CategoriesFrame:
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.frame = None
        self.is_visible = False
        self.categories_data = []
        self.current_page = 1
        self.total_pages = 1
        self.page_size = 20
        
        self.create_interface()
    
    def create_interface(self):
        """创建分类管理界面"""
        self.frame = ttk_bs.Frame(self.parent)
        
        # 标题和工具栏
        self.create_toolbar()
        
        # 分类列表
        self.create_category_list()
        
        # 分页控件
        self.create_pagination()
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar_frame = ttk_bs.Frame(self.frame)
        toolbar_frame.pack(fill=X, padx=20, pady=20)
        
        # 标题
        ttk_bs.Label(
            toolbar_frame,
            text="分类管理",
            font=("Arial", 18, "bold"),
            bootstyle="primary"
        ).pack(side=LEFT)
        
        # 右侧按钮组
        btn_frame = ttk_bs.Frame(toolbar_frame)
        btn_frame.pack(side=RIGHT)
        
        # 添加分类按钮
        add_btn = ttk_bs.Button(
            btn_frame,
            text="+ 添加分类",
            bootstyle="success",
            command=self.add_category
        )
        add_btn.pack(side=RIGHT, padx=5)
        
        # 排序按钮
        sort_btn = ttk_bs.Button(
            btn_frame,
            text="排序",
            bootstyle="warning",
            command=self.sort_categories
        )
        sort_btn.pack(side=RIGHT, padx=5)
        
        # 刷新按钮
        refresh_btn = ttk_bs.Button(
            btn_frame,
            text="刷新",
            bootstyle="outline-primary",
            command=self.refresh
        )
        refresh_btn.pack(side=RIGHT, padx=5)
    
    def create_category_list(self):
        """创建分类列表"""
        list_frame = ttk_bs.Frame(self.frame)
        list_frame.pack(fill=BOTH, expand=True, padx=20, pady=(0, 20))
        
        # 创建Treeview
        columns = ("id", "name", "description", "video_count", "sort_order", "status", "created_at")
        self.category_tree = ttk_bs.Treeview(
            list_frame,
            columns=columns,
            show="headings",
            height=15
        )
        
        # 设置列标题和宽度
        column_config = {
            "id": ("ID", 60),
            "name": ("分类名称", 200),
            "description": ("描述", 300),
            "video_count": ("视频数量", 100),
            "sort_order": ("排序", 80),
            "status": ("状态", 80),
            "created_at": ("创建时间", 150)
        }
        
        for col, (text, width) in column_config.items():
            self.category_tree.heading(col, text=text)
            self.category_tree.column(col, width=width)
        
        # 添加滚动条
        v_scrollbar = tk.Scrollbar(list_frame, orient="vertical", command=self.category_tree.yview)
        h_scrollbar = tk.Scrollbar(list_frame, orient="horizontal", command=self.category_tree.xview)
        self.category_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.category_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定双击事件
        self.category_tree.bind("<Double-1>", self.on_category_double_click)
        
        # 绑定右键菜单
        self.category_tree.bind("<Button-3>", self.show_context_menu)
        
        # 创建右键菜单
        self.context_menu = tk.Menu(self.category_tree, tearoff=0)
        self.context_menu.add_command(label="编辑", command=self.edit_category)
        self.context_menu.add_command(label="查看视频", command=self.view_category_videos)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="删除", command=self.delete_category)
    
    def create_pagination(self):
        """创建分页控件"""
        pagination_frame = ttk_bs.Frame(self.frame)
        pagination_frame.pack(fill=X, padx=20, pady=(0, 20))
        
        # 左侧：显示信息
        info_frame = ttk_bs.Frame(pagination_frame)
        info_frame.pack(side=LEFT)
        
        self.info_label = ttk_bs.Label(info_frame, text="")
        self.info_label.pack()
        
        # 右侧：分页按钮
        nav_frame = ttk_bs.Frame(pagination_frame)
        nav_frame.pack(side=RIGHT)
        
        # 首页
        first_btn = ttk_bs.Button(
            nav_frame,
            text="首页",
            bootstyle="outline-primary",
            command=lambda: self.go_to_page(1)
        )
        first_btn.pack(side=LEFT, padx=2)
        
        # 上一页
        self.prev_btn = ttk_bs.Button(
            nav_frame,
            text="上一页",
            bootstyle="outline-primary",
            command=self.prev_page
        )
        self.prev_btn.pack(side=LEFT, padx=2)
        
        # 页码输入
        ttk_bs.Label(nav_frame, text="第").pack(side=LEFT, padx=(10, 2))
        self.page_var = tk.StringVar(value="1")
        page_entry = ttk_bs.Entry(nav_frame, textvariable=self.page_var, width=5)
        page_entry.pack(side=LEFT, padx=2)
        page_entry.bind('<Return>', lambda e: self.go_to_page(int(self.page_var.get() or 1)))
        
        self.total_pages_label = ttk_bs.Label(nav_frame, text="/ 1 页")
        self.total_pages_label.pack(side=LEFT, padx=2)
        
        # 下一页
        self.next_btn = ttk_bs.Button(
            nav_frame,
            text="下一页",
            bootstyle="outline-primary",
            command=self.next_page
        )
        self.next_btn.pack(side=LEFT, padx=2)
        
        # 末页
        last_btn = ttk_bs.Button(
            nav_frame,
            text="末页",
            bootstyle="outline-primary",
            command=lambda: self.go_to_page(self.total_pages)
        )
        last_btn.pack(side=LEFT, padx=2)
    
    def show(self):
        """显示框架"""
        if not self.is_visible:
            self.frame.pack(fill=BOTH, expand=True)
            self.is_visible = True
            self.refresh()
    
    def hide(self):
        """隐藏框架"""
        if self.is_visible:
            self.frame.pack_forget()
            self.is_visible = False
    
    def refresh(self):
        """刷新分类列表"""
        if not self.is_visible:
            return
        
        self.load_categories()
    
    def load_categories(self):
        """加载分类数据"""
        def fetch_categories():
            try:
                result = api_client.get_categories(
                    page=self.current_page,
                    limit=self.page_size
                )
                
                if result.get("success"):
                    data = result.get("data", {})
                    self.categories_data = data.get("categories", [])
                    pagination = data.get("pagination", {})
                    self.total_pages = pagination.get("total_pages", 1)
                    
                    self.parent.after(0, self.update_category_list)
                else:
                    self.main_app.set_status(f"加载分类失败: {result.get('message', '未知错误')}")
            except Exception as e:
                self.main_app.set_status(f"加载分类失败: {str(e)}")
        
        threading.Thread(target=fetch_categories, daemon=True).start()
    
    def update_category_list(self):
        """更新分类列表显示"""
        # 清除现有数据
        for item in self.category_tree.get_children():
            self.category_tree.delete(item)
        
        # 添加新数据
        for category in self.categories_data:
            created_at = self.format_datetime(category.get("created_at", ""))
            
            self.category_tree.insert("", "end", values=(
                category.get("id", ""),
                category.get("name", ""),
                category.get("description", ""),
                category.get("video_count", 0),
                category.get("sort_order", 0),
                category.get("status", ""),
                created_at
            ))
        
        # 更新分页信息
        self.update_pagination_info()
    
    def format_datetime(self, dt_str):
        """格式化日期时间"""
        if not dt_str:
            return ""
        try:
            dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
            return dt.strftime("%Y-%m-%d %H:%M")
        except:
            return dt_str
    
    def update_pagination_info(self):
        """更新分页信息"""
        total_items = len(self.categories_data)
        start_item = (self.current_page - 1) * self.page_size + 1
        end_item = min(start_item + total_items - 1, self.current_page * self.page_size)
        
        self.info_label.config(text=f"显示 {start_item}-{end_item} 项")
        self.page_var.set(str(self.current_page))
        self.total_pages_label.config(text=f"/ {self.total_pages} 页")
        
        # 更新按钮状态
        self.prev_btn.config(state="normal" if self.current_page > 1 else "disabled")
        self.next_btn.config(state="normal" if self.current_page < self.total_pages else "disabled")
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.load_categories()
    
    def next_page(self):
        """下一页"""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.load_categories()
    
    def go_to_page(self, page):
        """跳转到指定页"""
        if 1 <= page <= self.total_pages:
            self.current_page = page
            self.load_categories()
    
    def on_category_double_click(self, event):
        """双击分类事件"""
        self.edit_category()
    
    def show_context_menu(self, event):
        """显示右键菜单"""
        item = self.category_tree.selection()
        if item:
            self.context_menu.post(event.x_root, event.y_root)
    
    def edit_category(self):
        """编辑分类"""
        selection = self.category_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个分类")
            return
        
        item = self.category_tree.item(selection[0])
        category_id = item['values'][0]
        
        # TODO: 实现分类编辑窗口
        messagebox.showinfo("提示", f"编辑分类 ID: {category_id}")
    
    def view_category_videos(self):
        """查看分类下的视频"""
        selection = self.category_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个分类")
            return
        
        item = self.category_tree.item(selection[0])
        category_id = item['values'][0]
        category_name = item['values'][1]
        
        # TODO: 切换到视频管理页面并筛选该分类
        messagebox.showinfo("提示", f"查看分类 '{category_name}' 下的视频")
    
    def delete_category(self):
        """删除分类"""
        selection = self.category_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个分类")
            return
        
        item = self.category_tree.item(selection[0])
        category_id = item['values'][0]
        category_name = item['values'][1]
        video_count = item['values'][3]
        
        if int(video_count) > 0:
            messagebox.showwarning("警告", f"分类 '{category_name}' 下还有 {video_count} 个视频，无法删除！")
            return
        
        if messagebox.askyesno("确认删除", f"确定要删除分类 '{category_name}' 吗？\n此操作不可撤销！"):
            def do_delete():
                try:
                    result = api_client.delete_category(category_id)
                    if result.get("success"):
                        self.parent.after(0, lambda: [
                            self.main_app.set_status("分类删除成功"),
                            self.refresh()
                        ])
                    else:
                        self.parent.after(0, lambda: messagebox.showerror(
                            "删除失败", 
                            result.get('message', '未知错误')
                        ))
                except Exception as e:
                    self.parent.after(0, lambda: messagebox.showerror(
                        "删除失败", 
                        f"删除分类时发生错误: {str(e)}"
                    ))
            
            threading.Thread(target=do_delete, daemon=True).start()
    
    def add_category(self):
        """添加分类"""
        # TODO: 实现添加分类窗口
        messagebox.showinfo("提示", "添加分类功能开发中...")
    
    def sort_categories(self):
        """排序分类"""
        # TODO: 实现分类排序功能
        messagebox.showinfo("提示", "分类排序功能开发中...")

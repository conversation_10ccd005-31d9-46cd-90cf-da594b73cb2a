# 配置文件
import os
import json

class Config:
    def __init__(self):
        self.config_file = "config.json"
        self.default_config = {
            "server_url": "http://localhost:3001",
            "admin_username": "",
            "admin_password": "",
            "remember_login": False,
            "theme": "darkly",
            "auto_refresh": True,
            "refresh_interval": 30,
            "window_size": "1200x800",
            "window_position": "center"
        }
        self.config = self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置，确保所有键都存在
                for key, value in self.default_config.items():
                    if key not in config:
                        config[key] = value
                return config
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                return self.default_config.copy()
        else:
            return self.default_config.copy()
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get(self, key, default=None):
        """获取配置值"""
        return self.config.get(key, default)
    
    def set(self, key, value):
        """设置配置值"""
        self.config[key] = value
        self.save_config()
    
    def update(self, updates):
        """批量更新配置"""
        self.config.update(updates)
        self.save_config()

# 全局配置实例
config = Config()

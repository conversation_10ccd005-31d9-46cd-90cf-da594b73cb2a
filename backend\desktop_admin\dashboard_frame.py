# 仪表盘框架
import tkinter as tk
from tkinter import ttk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import threading
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.dates as mdates

from api_client import api_client

class DashboardFrame:
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.frame = None
        self.is_visible = False
        self.dashboard_data = {}
        
        self.create_interface()
    
    def create_interface(self):
        """创建仪表盘界面"""
        self.frame = ttk_bs.Frame(self.parent)
        
        # 创建滚动框架
        self.canvas = tk.Canvas(self.frame, highlightthickness=0)
        self.scrollbar = tk.Scrollbar(self.frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk_bs.Frame(self.canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        # 标题
        title_frame = ttk_bs.Frame(self.scrollable_frame)
        title_frame.pack(fill=X, padx=20, pady=20)
        
        ttk_bs.Label(
            title_frame,
            text="系统仪表盘",
            font=("Arial", 18, "bold"),
            bootstyle="primary"
        ).pack(side=LEFT)
        
        # 刷新按钮
        refresh_btn = ttk_bs.Button(
            title_frame,
            text="刷新数据",
            bootstyle="outline-primary",
            command=self.refresh
        )
        refresh_btn.pack(side=RIGHT)
        
        # 统计卡片区域
        self.create_stats_cards()
        
        # 图表区域
        self.create_charts_area()
        
        # 最新活动区域
        self.create_recent_activity()
        
        # 配置滚动
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")
        
        # 绑定鼠标滚轮
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
    
    def _on_mousewheel(self, event):
        """鼠标滚轮事件"""
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    
    def create_stats_cards(self):
        """创建统计卡片"""
        stats_frame = ttk_bs.Frame(self.scrollable_frame)
        stats_frame.pack(fill=X, padx=20, pady=10)
        
        # 统计卡片数据
        self.stats_cards = {}
        stats_config = [
            ("total_videos", "视频总数", "0", "primary", "📹"),
            ("total_categories", "分类总数", "0", "secondary", "📂"),
            ("total_views", "总观看次数", "0", "success", "👁"),
            ("total_admins", "管理员数量", "0", "info", "👤"),
            ("api_requests", "API请求数", "0", "warning", "🔗"),
            ("storage_used", "存储使用", "0 GB", "danger", "💾")
        ]
        
        # 创建两行，每行3个卡片
        for i in range(0, len(stats_config), 3):
            row_frame = ttk_bs.Frame(stats_frame)
            row_frame.pack(fill=X, pady=5)
            
            for j in range(3):
                if i + j < len(stats_config):
                    key, title, value, style, icon = stats_config[i + j]
                    card = self.create_stat_card(row_frame, title, value, style, icon)
                    card.pack(side=LEFT, fill=BOTH, expand=True, padx=5)
                    self.stats_cards[key] = card
    
    def create_stat_card(self, parent, title, value, style, icon):
        """创建单个统计卡片"""
        card = ttk_bs.Frame(parent, bootstyle=f"{style}")
        
        # 内容框架
        content_frame = ttk_bs.Frame(card)
        content_frame.pack(fill=BOTH, expand=True, padx=15, pady=15)
        
        # 图标和标题行
        header_frame = ttk_bs.Frame(content_frame)
        header_frame.pack(fill=X)
        
        ttk_bs.Label(
            header_frame,
            text=icon,
            font=("Arial", 20)
        ).pack(side=LEFT)
        
        ttk_bs.Label(
            header_frame,
            text=title,
            font=("Arial", 10),
            bootstyle="light"
        ).pack(side=RIGHT)
        
        # 数值
        value_label = ttk_bs.Label(
            content_frame,
            text=value,
            font=("Arial", 24, "bold"),
            bootstyle="light"
        )
        value_label.pack(anchor=W, pady=(10, 0))
        
        # 保存值标签引用以便更新
        card.value_label = value_label
        
        return card
    
    def create_charts_area(self):
        """创建图表区域"""
        charts_frame = ttk_bs.LabelFrame(
            self.scrollable_frame,
            text="数据趋势",
            padding=15
        )
        charts_frame.pack(fill=X, padx=20, pady=10)
        
        # 创建图表容器
        self.chart_frame = ttk_bs.Frame(charts_frame)
        self.chart_frame.pack(fill=BOTH, expand=True)
        
        # 初始化图表
        self.init_charts()
    
    def init_charts(self):
        """初始化图表"""
        # 创建matplotlib图形
        self.fig, (self.ax1, self.ax2) = plt.subplots(1, 2, figsize=(12, 4))
        self.fig.patch.set_facecolor('#2b3e50')  # 深色背景
        
        # 设置图表样式
        for ax in [self.ax1, self.ax2]:
            ax.set_facecolor('#34495e')
            ax.tick_params(colors='white')
            ax.spines['bottom'].set_color('white')
            ax.spines['top'].set_color('white')
            ax.spines['right'].set_color('white')
            ax.spines['left'].set_color('white')
        
        # 创建画布
        self.canvas_chart = FigureCanvasTkAgg(self.fig, self.chart_frame)
        self.canvas_chart.get_tk_widget().pack(fill=BOTH, expand=True)
    
    def create_recent_activity(self):
        """创建最新活动区域"""
        activity_frame = ttk_bs.LabelFrame(
            self.scrollable_frame,
            text="最新活动",
            padding=15
        )
        activity_frame.pack(fill=X, padx=20, pady=10)
        
        # 创建活动列表
        self.activity_tree = ttk_bs.Treeview(
            activity_frame,
            columns=("time", "action", "details"),
            show="headings",
            height=8
        )
        
        # 设置列标题
        self.activity_tree.heading("time", text="时间")
        self.activity_tree.heading("action", text="操作")
        self.activity_tree.heading("details", text="详情")
        
        # 设置列宽
        self.activity_tree.column("time", width=150)
        self.activity_tree.column("action", width=100)
        self.activity_tree.column("details", width=300)
        
        # 添加滚动条
        activity_scroll = ttk_bs.Scrollbar(activity_frame, orient="vertical", command=self.activity_tree.yview)
        self.activity_tree.configure(yscrollcommand=activity_scroll.set)
        
        self.activity_tree.pack(side=LEFT, fill=BOTH, expand=True)
        activity_scroll.pack(side=RIGHT, fill=Y)
    
    def show(self):
        """显示框架"""
        if not self.is_visible:
            self.frame.pack(fill=BOTH, expand=True)
            self.is_visible = True
            self.refresh()
    
    def hide(self):
        """隐藏框架"""
        if self.is_visible:
            self.frame.pack_forget()
            self.is_visible = False
    
    def refresh(self):
        """刷新数据"""
        if not self.is_visible:
            return
        
        def fetch_data():
            try:
                result = api_client.get_dashboard()
                if result.get("success"):
                    self.dashboard_data = result.get("data", {})
                    self.parent.after(0, self.update_interface)
                else:
                    self.main_app.set_status(f"获取仪表盘数据失败: {result.get('message', '未知错误')}")
            except Exception as e:
                self.main_app.set_status(f"获取仪表盘数据失败: {str(e)}")
        
        threading.Thread(target=fetch_data, daemon=True).start()
    
    def update_interface(self):
        """更新界面数据"""
        if not self.dashboard_data:
            return
        
        # 更新统计卡片
        self.update_stats_cards()
        
        # 更新图表
        self.update_charts()
        
        # 更新活动列表
        self.update_activity_list()
    
    def update_stats_cards(self):
        """更新统计卡片"""
        stats = self.dashboard_data.get("stats", {})
        
        # 更新各个卡片的值
        updates = {
            "total_videos": f"{stats.get('total_videos', 0):,}",
            "total_categories": f"{stats.get('total_categories', 0):,}",
            "total_views": f"{stats.get('total_views', 0):,}",
            "total_admins": f"{stats.get('total_admins', 0):,}",
            "api_requests": f"{stats.get('api_requests_today', 0):,}",
            "storage_used": f"{stats.get('storage_used_gb', 0):.1f} GB"
        }
        
        for key, value in updates.items():
            if key in self.stats_cards:
                self.stats_cards[key].value_label.config(text=value)
    
    def update_charts(self):
        """更新图表"""
        # 清除现有图表
        self.ax1.clear()
        self.ax2.clear()
        
        # 获取图表数据
        charts_data = self.dashboard_data.get("charts", {})
        
        # 视频上传趋势图
        video_data = charts_data.get("video_uploads", [])
        if video_data:
            dates = [item["date"] for item in video_data]
            counts = [item["count"] for item in video_data]
            
            self.ax1.plot(dates, counts, color='#3498db', linewidth=2, marker='o')
            self.ax1.set_title("视频上传趋势", color='white')
            self.ax1.set_ylabel("上传数量", color='white')
            
        # 观看次数统计图
        views_data = charts_data.get("daily_views", [])
        if views_data:
            dates = [item["date"] for item in views_data]
            views = [item["views"] for item in views_data]
            
            self.ax2.bar(dates, views, color='#2ecc71', alpha=0.7)
            self.ax2.set_title("每日观看次数", color='white')
            self.ax2.set_ylabel("观看次数", color='white')
        
        # 刷新画布
        self.canvas_chart.draw()
    
    def update_activity_list(self):
        """更新活动列表"""
        # 清除现有数据
        for item in self.activity_tree.get_children():
            self.activity_tree.delete(item)
        
        # 添加新数据
        activities = self.dashboard_data.get("recent_activities", [])
        for activity in activities:
            self.activity_tree.insert("", "end", values=(
                activity.get("time", ""),
                activity.get("action", ""),
                activity.get("details", "")
            ))

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
91JSPG.COM 桌面管理应用安装脚本
"""

import sys
import os
import subprocess
import platform

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print(f"[错误] Python版本过低: {version.major}.{version.minor}")
        print("请安装Python 3.7或更高版本")
        return False
    
    print(f"[信息] Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def install_dependencies():
    """安装依赖包"""
    print("[信息] 开始安装依赖包...")
    
    try:
        # 升级pip
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # 安装依赖
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("[成功] 依赖包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"[错误] 依赖包安装失败: {e}")
        return False

def create_virtual_environment():
    """创建虚拟环境（可选）"""
    if input("[询问] 是否创建虚拟环境？(y/N): ").lower() in ['y', 'yes']:
        try:
            print("[信息] 创建虚拟环境...")
            subprocess.check_call([sys.executable, "-m", "venv", "venv"])
            
            # 激活虚拟环境并安装依赖
            if platform.system() == "Windows":
                pip_path = os.path.join("venv", "Scripts", "pip.exe")
            else:
                pip_path = os.path.join("venv", "bin", "pip")
            
            subprocess.check_call([pip_path, "install", "--upgrade", "pip"])
            subprocess.check_call([pip_path, "install", "-r", "requirements.txt"])
            
            print("[成功] 虚拟环境创建完成")
            print("[提示] 使用启动脚本会自动激活虚拟环境")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"[错误] 虚拟环境创建失败: {e}")
            return False
    
    return True

def set_script_permissions():
    """设置脚本执行权限（Linux/macOS）"""
    if platform.system() != "Windows":
        try:
            os.chmod("start.sh", 0o755)
            print("[信息] 已设置start.sh执行权限")
        except Exception as e:
            print(f"[警告] 设置执行权限失败: {e}")

def test_installation():
    """测试安装"""
    print("[信息] 测试安装...")
    
    try:
        # 测试导入主要模块
        import tkinter
        import ttkbootstrap
        import requests
        import matplotlib
        import pandas
        from PIL import Image
        
        print("[成功] 所有依赖包测试通过")
        return True
        
    except ImportError as e:
        print(f"[错误] 模块导入失败: {e}")
        return False

def main():
    """主安装流程"""
    print("=" * 50)
    print("  91JSPG.COM 桌面管理应用安装程序")
    print("=" * 50)
    print()
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 创建虚拟环境（可选）
    if not create_virtual_environment():
        print("[警告] 虚拟环境创建失败，将使用系统Python环境")
    
    # 安装依赖包
    if not install_dependencies():
        print("[错误] 安装失败")
        sys.exit(1)
    
    # 设置脚本权限
    set_script_permissions()
    
    # 测试安装
    if not test_installation():
        print("[错误] 安装测试失败")
        sys.exit(1)
    
    print()
    print("=" * 50)
    print("  安装完成！")
    print("=" * 50)
    print()
    print("启动方式：")
    
    if platform.system() == "Windows":
        print("  双击 start.bat 或运行: python app.py")
    else:
        print("  运行: ./start.sh 或 python3 app.py")
    
    print()
    print("首次使用请配置服务器连接地址")
    print("默认地址: http://localhost:3001")
    print()

if __name__ == "__main__":
    main()

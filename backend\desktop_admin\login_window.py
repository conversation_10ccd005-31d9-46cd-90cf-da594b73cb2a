# 登录窗口
import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import threading

from config import config
from api_client import api_client

class LoginWindow:
    def __init__(self, parent, success_callback):
        self.parent = parent
        self.success_callback = success_callback
        
        # 创建登录窗口
        self.window = ttk_bs.Toplevel(parent)
        self.window.title("登录 - 91JSPG.COM 管理系统")
        self.window.geometry("400x500")
        self.window.resizable(False, False)
        
        # 设置窗口为模态
        self.window.transient(parent)
        self.window.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.create_interface()
        
        # 加载保存的配置
        self.load_saved_config()
        
        # 绑定回车键
        self.window.bind('<Return>', lambda e: self.login())
    
    def center_window(self):
        """居中显示窗口"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_interface(self):
        """创建登录界面"""
        # 主框架
        main_frame = ttk_bs.Frame(self.window)
        main_frame.pack(fill=BOTH, expand=True, padx=30, pady=30)
        
        # 标题
        title_label = ttk_bs.Label(
            main_frame,
            text="91JSPG.COM",
            font=("Arial", 24, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 10))
        
        subtitle_label = ttk_bs.Label(
            main_frame,
            text="桌面管理系统",
            font=("Arial", 12),
            bootstyle="secondary"
        )
        subtitle_label.pack(pady=(0, 30))
        
        # 服务器地址配置
        server_frame = ttk_bs.LabelFrame(main_frame, text="服务器配置", padding=15)
        server_frame.pack(fill=X, pady=(0, 20))
        
        ttk_bs.Label(server_frame, text="服务器地址:").pack(anchor=W)
        self.server_url_var = tk.StringVar(value=config.get("server_url", "http://localhost:3001"))
        self.server_url_entry = ttk_bs.Entry(
            server_frame,
            textvariable=self.server_url_var,
            font=("Arial", 10),
            width=40
        )
        self.server_url_entry.pack(fill=X, pady=(5, 0))
        
        # 测试连接按钮
        test_btn = ttk_bs.Button(
            server_frame,
            text="测试连接",
            bootstyle="outline-info",
            command=self.test_connection
        )
        test_btn.pack(pady=(10, 0))
        
        # 登录表单
        login_frame = ttk_bs.LabelFrame(main_frame, text="管理员登录", padding=15)
        login_frame.pack(fill=X, pady=(0, 20))
        
        # 用户名
        ttk_bs.Label(login_frame, text="用户名:").pack(anchor=W)
        self.username_var = tk.StringVar()
        self.username_entry = ttk_bs.Entry(
            login_frame,
            textvariable=self.username_var,
            font=("Arial", 10),
            width=40
        )
        self.username_entry.pack(fill=X, pady=(5, 10))
        
        # 密码
        ttk_bs.Label(login_frame, text="密码:").pack(anchor=W)
        self.password_var = tk.StringVar()
        self.password_entry = ttk_bs.Entry(
            login_frame,
            textvariable=self.password_var,
            show="*",
            font=("Arial", 10),
            width=40
        )
        self.password_entry.pack(fill=X, pady=(5, 10))
        
        # 记住登录
        self.remember_var = tk.BooleanVar(value=config.get("remember_login", False))
        remember_check = ttk_bs.Checkbutton(
            login_frame,
            text="记住登录信息",
            variable=self.remember_var,
            bootstyle="primary"
        )
        remember_check.pack(anchor=W, pady=(0, 10))
        
        # 登录按钮
        self.login_btn = ttk_bs.Button(
            login_frame,
            text="登录",
            bootstyle="primary",
            command=self.login,
            width=20
        )
        self.login_btn.pack(pady=10)
        
        # 状态标签
        self.status_label = ttk_bs.Label(
            main_frame,
            text="",
            font=("Arial", 9),
            bootstyle="secondary"
        )
        self.status_label.pack(pady=(10, 0))
        
        # 进度条
        self.progress = ttk_bs.Progressbar(
            main_frame,
            mode='indeterminate',
            bootstyle="primary"
        )
        
        # 设置焦点
        self.username_entry.focus()
    
    def load_saved_config(self):
        """加载保存的配置"""
        if config.get("remember_login"):
            self.username_var.set(config.get("admin_username", ""))
            if self.username_var.get():
                self.password_entry.focus()
    
    def test_connection(self):
        """测试服务器连接"""
        def test():
            self.set_status("正在测试连接...")
            self.progress.pack(fill=X, pady=(10, 0))
            self.progress.start()
            
            try:
                # 更新API客户端的服务器地址
                api_client.set_base_url(self.server_url_var.get())
                
                # 进行健康检查
                result = api_client.health_check()
                
                self.window.after(0, lambda: self.on_test_result(result))
            except Exception as e:
                self.window.after(0, lambda: self.on_test_result({
                    "success": False,
                    "message": f"连接失败: {str(e)}"
                }))
        
        threading.Thread(target=test, daemon=True).start()
    
    def on_test_result(self, result):
        """处理连接测试结果"""
        self.progress.stop()
        self.progress.pack_forget()
        
        if result.get("success"):
            self.set_status("✓ 服务器连接正常", "success")
            messagebox.showinfo("成功", "服务器连接正常！")
        else:
            self.set_status(f"✗ {result.get('message', '连接失败')}", "danger")
            messagebox.showerror("错误", f"服务器连接失败：\n{result.get('message', '未知错误')}")
    
    def login(self):
        """执行登录"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()
        
        if not username or not password:
            messagebox.showwarning("警告", "请输入用户名和密码")
            return
        
        def do_login():
            self.set_status("正在登录...")
            self.login_btn.config(state="disabled")
            self.progress.pack(fill=X, pady=(10, 0))
            self.progress.start()
            
            try:
                # 更新API客户端的服务器地址
                api_client.set_base_url(self.server_url_var.get())
                
                # 执行登录
                result = api_client.login(username, password)
                
                self.window.after(0, lambda: self.on_login_result(result, username, password))
            except Exception as e:
                self.window.after(0, lambda: self.on_login_result({
                    "success": False,
                    "message": f"登录失败: {str(e)}"
                }, username, password))
        
        threading.Thread(target=do_login, daemon=True).start()
    
    def on_login_result(self, result, username, password):
        """处理登录结果"""
        self.progress.stop()
        self.progress.pack_forget()
        self.login_btn.config(state="normal")
        
        if result.get("success"):
            # 保存配置
            config.update({
                "server_url": self.server_url_var.get(),
                "remember_login": self.remember_var.get(),
                "admin_username": username if self.remember_var.get() else ""
            })
            
            self.set_status("✓ 登录成功", "success")
            
            # 获取管理员信息
            admin_info = result.get("data", {}).get("admin", {})
            
            # 调用成功回调
            self.success_callback(admin_info)
            
            # 关闭登录窗口
            self.window.destroy()
        else:
            self.set_status(f"✗ {result.get('message', '登录失败')}", "danger")
            messagebox.showerror("登录失败", result.get('message', '未知错误'))
    
    def set_status(self, message, style="secondary"):
        """设置状态消息"""
        self.status_label.config(text=message, bootstyle=style)

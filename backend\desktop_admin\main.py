# 主应用程序
import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import threading
import time
from datetime import datetime

from config import config
from api_client import api_client
from login_window import LoginWindow
from dashboard_frame import DashboardFrame
from videos_frame import VideosFrame
from categories_frame import CategoriesFrame
from api_keys_frame import ApiKeysFrame
from admins_frame import AdminsFrame
from settings_frame import SettingsFrame

class MainApplication:
    def __init__(self):
        self.root = ttk_bs.Window(
            title="91JSPG.COM 桌面管理系统",
            themename=config.get("theme", "darkly"),
            size=tuple(map(int, config.get("window_size", "1200x800").split('x')))
        )
        
        # 设置窗口图标和属性
        self.root.resizable(True, True)
        self.root.minsize(1000, 600)
        
        # 居中显示窗口
        self.center_window()
        
        # 当前用户信息
        self.current_admin = None
        self.is_logged_in = False
        
        # 创建主界面
        self.create_main_interface()
        
        # 检查登录状态
        self.check_login_status()
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 自动刷新定时器
        self.auto_refresh_timer = None
        self.start_auto_refresh()
    
    def center_window(self):
        """居中显示窗口"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_main_interface(self):
        """创建主界面"""
        # 创建主框架
        self.main_frame = ttk_bs.Frame(self.root)
        self.main_frame.pack(fill=BOTH, expand=True)
        
        # 创建顶部工具栏
        self.create_toolbar()
        
        # 创建主内容区域
        self.create_content_area()
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_toolbar(self):
        """创建顶部工具栏"""
        self.toolbar = ttk_bs.Frame(self.main_frame, bootstyle="dark")
        self.toolbar.pack(fill=X, padx=5, pady=5)
        
        # 左侧按钮组
        left_frame = ttk_bs.Frame(self.toolbar)
        left_frame.pack(side=LEFT, fill=Y)
        
        # 导航按钮
        self.nav_buttons = {}
        nav_items = [
            ("仪表盘", "dashboard", "info"),
            ("视频管理", "videos", "primary"),
            ("分类管理", "categories", "secondary"),
            ("API密钥", "api_keys", "warning"),
            ("管理员", "admins", "danger"),
            ("设置", "settings", "dark")
        ]
        
        for text, key, style in nav_items:
            btn = ttk_bs.Button(
                left_frame,
                text=text,
                bootstyle=f"outline-{style}",
                command=lambda k=key: self.switch_frame(k)
            )
            btn.pack(side=LEFT, padx=2)
            self.nav_buttons[key] = btn
        
        # 右侧用户信息
        right_frame = ttk_bs.Frame(self.toolbar)
        right_frame.pack(side=RIGHT, fill=Y)
        
        self.user_label = ttk_bs.Label(right_frame, text="未登录", bootstyle="light")
        self.user_label.pack(side=RIGHT, padx=10)
        
        self.logout_btn = ttk_bs.Button(
            right_frame,
            text="退出登录",
            bootstyle="outline-danger",
            command=self.logout
        )
        self.logout_btn.pack(side=RIGHT, padx=5)
        
        # 刷新按钮
        self.refresh_btn = ttk_bs.Button(
            right_frame,
            text="刷新",
            bootstyle="outline-success",
            command=self.refresh_current_frame
        )
        self.refresh_btn.pack(side=RIGHT, padx=5)
    
    def create_content_area(self):
        """创建主内容区域"""
        self.content_frame = ttk_bs.Frame(self.main_frame)
        self.content_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        # 创建各个功能框架
        self.frames = {}
        self.current_frame_key = None
        
        # 初始化各个框架（但不显示）
        frame_classes = {
            "dashboard": DashboardFrame,
            "videos": VideosFrame,
            "categories": CategoriesFrame,
            "api_keys": ApiKeysFrame,
            "admins": AdminsFrame,
            "settings": SettingsFrame
        }
        
        for key, frame_class in frame_classes.items():
            frame = frame_class(self.content_frame, self)
            self.frames[key] = frame
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk_bs.Frame(self.main_frame, bootstyle="dark")
        self.status_bar.pack(fill=X, side=BOTTOM)
        
        # 状态信息
        self.status_label = ttk_bs.Label(
            self.status_bar,
            text="就绪",
            bootstyle="light"
        )
        self.status_label.pack(side=LEFT, padx=10, pady=5)
        
        # 服务器连接状态
        self.connection_label = ttk_bs.Label(
            self.status_bar,
            text="未连接",
            bootstyle="danger"
        )
        self.connection_label.pack(side=RIGHT, padx=10, pady=5)
        
        # 时间显示
        self.time_label = ttk_bs.Label(
            self.status_bar,
            text="",
            bootstyle="light"
        )
        self.time_label.pack(side=RIGHT, padx=10, pady=5)
        
        # 更新时间显示
        self.update_time()
    
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)
    
    def check_login_status(self):
        """检查登录状态"""
        # 如果已经登录，跳过登录检查
        if self.is_logged_in and self.current_admin:
            self.user_label.config(text=f"欢迎，{self.current_admin.get('username', '管理员')}")
            self.update_connection_status(True)
            self.switch_frame("dashboard")
            return

        if config.get("remember_login") and config.get("admin_username"):
            # 尝试自动登录
            self.show_login_window()
        else:
            self.show_login_window()
    
    def show_login_window(self):
        """显示登录窗口"""
        login_window = LoginWindow(self.root, self.on_login_success)
        self.root.wait_window(login_window.window)
    
    def on_login_success(self, admin_info):
        """登录成功回调"""
        self.current_admin = admin_info
        self.is_logged_in = True
        self.user_label.config(text=f"欢迎，{admin_info.get('username', '管理员')}")
        self.update_connection_status(True)
        self.switch_frame("dashboard")
        self.set_status("登录成功")
    
    def logout(self):
        """退出登录"""
        if messagebox.askyesno("确认", "确定要退出登录吗？"):
            self.is_logged_in = False
            self.current_admin = None
            api_client.token = None
            self.user_label.config(text="未登录")
            self.update_connection_status(False)
            self.hide_all_frames()
            self.show_login_window()
    
    def switch_frame(self, frame_key):
        """切换显示的框架"""
        if not self.is_logged_in and frame_key != "settings":
            messagebox.showwarning("警告", "请先登录")
            return
        
        # 隐藏当前框架
        if self.current_frame_key:
            self.frames[self.current_frame_key].hide()
        
        # 显示新框架
        self.frames[frame_key].show()
        self.current_frame_key = frame_key
        
        # 更新按钮状态
        for key, btn in self.nav_buttons.items():
            if key == frame_key:
                btn.config(bootstyle=btn.cget("bootstyle").replace("outline-", ""))
            else:
                style = btn.cget("bootstyle")
                if not style.startswith("outline-"):
                    btn.config(bootstyle=f"outline-{style}")
    
    def hide_all_frames(self):
        """隐藏所有框架"""
        for frame in self.frames.values():
            frame.hide()
        self.current_frame_key = None
    
    def refresh_current_frame(self):
        """刷新当前框架"""
        if self.current_frame_key and self.current_frame_key in self.frames:
            self.frames[self.current_frame_key].refresh()
            self.set_status("已刷新")
    
    def start_auto_refresh(self):
        """开始自动刷新"""
        if config.get("auto_refresh"):
            interval = config.get("refresh_interval", 30)
            self.auto_refresh_timer = self.root.after(interval * 1000, self.auto_refresh)
    
    def auto_refresh(self):
        """自动刷新"""
        if self.is_logged_in and self.current_frame_key:
            self.refresh_current_frame()
        self.start_auto_refresh()
    
    def update_connection_status(self, connected):
        """更新连接状态"""
        if connected:
            self.connection_label.config(text="已连接", bootstyle="success")
        else:
            self.connection_label.config(text="未连接", bootstyle="danger")
    
    def set_status(self, message):
        """设置状态栏消息"""
        self.status_label.config(text=message)
        # 3秒后恢复默认状态
        self.root.after(3000, lambda: self.status_label.config(text="就绪"))
    
    def on_closing(self):
        """窗口关闭事件"""
        # 保存窗口大小和位置
        geometry = self.root.geometry()
        config.set("window_size", geometry.split('+')[0])
        
        # 取消自动刷新定时器
        if self.auto_refresh_timer:
            self.root.after_cancel(self.auto_refresh_timer)
        
        self.root.destroy()
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()

# 为了兼容app.py的调用，创建一个别名
VideoAdminApp = MainApplication

if __name__ == "__main__":
    app = MainApplication()
    app.run()

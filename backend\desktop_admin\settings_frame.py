# 设置框架
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import threading
import json
import os

from config import Config

class SettingsFrame:
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.frame = None
        self.is_visible = False
        self.config = Config()
        
        self.create_interface()
    
    def create_interface(self):
        """创建设置界面"""
        self.frame = ttk_bs.Frame(self.parent)
        
        # 创建滚动区域
        self.create_scrollable_area()
        
        # 标题
        self.create_title()
        
        # 服务器设置
        self.create_server_settings()
        
        # 界面设置
        self.create_ui_settings()
        
        # 数据设置
        self.create_data_settings()
        
        # 高级设置
        self.create_advanced_settings()
        
        # 操作按钮
        self.create_action_buttons()
    
    def create_scrollable_area(self):
        """创建可滚动区域"""
        # 创建Canvas和Scrollbar
        self.canvas = tk.Canvas(self.frame)
        self.scrollbar = tk.Scrollbar(self.frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk_bs.Frame(self.canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        # 布局
        self.canvas.pack(side="left", fill="both", expand=True, padx=20, pady=20)
        self.scrollbar.pack(side="right", fill="y", pady=20)
        
        # 绑定鼠标滚轮
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
    
    def _on_mousewheel(self, event):
        """鼠标滚轮事件"""
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    
    def create_title(self):
        """创建标题"""
        title_frame = ttk_bs.Frame(self.scrollable_frame)
        title_frame.pack(fill=X, pady=(0, 30))
        
        ttk_bs.Label(
            title_frame,
            text="应用设置",
            font=("Arial", 18, "bold"),
            bootstyle="primary"
        ).pack(side=LEFT)
    
    def create_server_settings(self):
        """创建服务器设置"""
        server_frame = ttk_bs.LabelFrame(self.scrollable_frame, text="服务器设置", padding=20)
        server_frame.pack(fill=X, pady=(0, 20))
        
        # 服务器URL
        url_frame = ttk_bs.Frame(server_frame)
        url_frame.pack(fill=X, pady=(0, 15))
        
        ttk_bs.Label(url_frame, text="服务器地址:", width=15).pack(side=LEFT)
        self.server_url_var = tk.StringVar(value=self.config.get("server_url", ""))
        url_entry = ttk_bs.Entry(url_frame, textvariable=self.server_url_var, width=40)
        url_entry.pack(side=LEFT, padx=(10, 0))
        
        # 测试连接按钮
        test_btn = ttk_bs.Button(
            url_frame,
            text="测试连接",
            bootstyle="outline-primary",
            command=self.test_connection
        )
        test_btn.pack(side=RIGHT, padx=(10, 0))
        
        # 连接超时
        timeout_frame = ttk_bs.Frame(server_frame)
        timeout_frame.pack(fill=X, pady=(0, 15))
        
        ttk_bs.Label(timeout_frame, text="连接超时(秒):", width=15).pack(side=LEFT)
        self.timeout_var = tk.StringVar(value=str(self.config.get("timeout", 30)))
        timeout_entry = ttk_bs.Entry(timeout_frame, textvariable=self.timeout_var, width=10)
        timeout_entry.pack(side=LEFT, padx=(10, 0))
        
        # 自动重连
        auto_reconnect_frame = ttk_bs.Frame(server_frame)
        auto_reconnect_frame.pack(fill=X)
        
        self.auto_reconnect_var = tk.BooleanVar(value=self.config.get("auto_reconnect", True))
        auto_reconnect_check = ttk_bs.Checkbutton(
            auto_reconnect_frame,
            text="自动重连",
            variable=self.auto_reconnect_var,
            bootstyle="primary"
        )
        auto_reconnect_check.pack(side=LEFT)
    
    def create_ui_settings(self):
        """创建界面设置"""
        ui_frame = ttk_bs.LabelFrame(self.scrollable_frame, text="界面设置", padding=20)
        ui_frame.pack(fill=X, pady=(0, 20))
        
        # 主题选择
        theme_frame = ttk_bs.Frame(ui_frame)
        theme_frame.pack(fill=X, pady=(0, 15))
        
        ttk_bs.Label(theme_frame, text="主题:", width=15).pack(side=LEFT)
        self.theme_var = tk.StringVar(value=self.config.get("theme", "darkly"))
        theme_combo = ttk_bs.Combobox(
            theme_frame,
            textvariable=self.theme_var,
            values=["darkly", "flatly", "journal", "litera", "lumen", "minty", "pulse", "sandstone", "united", "yeti"],
            state="readonly",
            width=20
        )
        theme_combo.pack(side=LEFT, padx=(10, 0))
        
        # 字体大小
        font_frame = ttk_bs.Frame(ui_frame)
        font_frame.pack(fill=X, pady=(0, 15))
        
        ttk_bs.Label(font_frame, text="字体大小:", width=15).pack(side=LEFT)
        self.font_size_var = tk.StringVar(value=str(self.config.get("font_size", 10)))
        font_size_combo = ttk_bs.Combobox(
            font_frame,
            textvariable=self.font_size_var,
            values=["8", "9", "10", "11", "12", "14", "16"],
            state="readonly",
            width=10
        )
        font_size_combo.pack(side=LEFT, padx=(10, 0))
        
        # 自动刷新
        refresh_frame = ttk_bs.Frame(ui_frame)
        refresh_frame.pack(fill=X, pady=(0, 15))
        
        self.auto_refresh_var = tk.BooleanVar(value=self.config.get("auto_refresh", True))
        auto_refresh_check = ttk_bs.Checkbutton(
            refresh_frame,
            text="自动刷新数据",
            variable=self.auto_refresh_var,
            bootstyle="primary"
        )
        auto_refresh_check.pack(side=LEFT)
        
        # 刷新间隔
        interval_frame = ttk_bs.Frame(ui_frame)
        interval_frame.pack(fill=X)
        
        ttk_bs.Label(interval_frame, text="刷新间隔(秒):", width=15).pack(side=LEFT)
        self.refresh_interval_var = tk.StringVar(value=str(self.config.get("refresh_interval", 30)))
        interval_entry = ttk_bs.Entry(interval_frame, textvariable=self.refresh_interval_var, width=10)
        interval_entry.pack(side=LEFT, padx=(10, 0))
    
    def create_data_settings(self):
        """创建数据设置"""
        data_frame = ttk_bs.LabelFrame(self.scrollable_frame, text="数据设置", padding=20)
        data_frame.pack(fill=X, pady=(0, 20))
        
        # 每页显示数量
        page_size_frame = ttk_bs.Frame(data_frame)
        page_size_frame.pack(fill=X, pady=(0, 15))
        
        ttk_bs.Label(page_size_frame, text="每页显示:", width=15).pack(side=LEFT)
        self.page_size_var = tk.StringVar(value=str(self.config.get("page_size", 20)))
        page_size_combo = ttk_bs.Combobox(
            page_size_frame,
            textvariable=self.page_size_var,
            values=["10", "20", "50", "100"],
            state="readonly",
            width=10
        )
        page_size_combo.pack(side=LEFT, padx=(10, 0))
        
        # 缓存设置
        cache_frame = ttk_bs.Frame(data_frame)
        cache_frame.pack(fill=X, pady=(0, 15))
        
        self.enable_cache_var = tk.BooleanVar(value=self.config.get("enable_cache", True))
        cache_check = ttk_bs.Checkbutton(
            cache_frame,
            text="启用数据缓存",
            variable=self.enable_cache_var,
            bootstyle="primary"
        )
        cache_check.pack(side=LEFT)
        
        # 缓存时间
        cache_time_frame = ttk_bs.Frame(data_frame)
        cache_time_frame.pack(fill=X)
        
        ttk_bs.Label(cache_time_frame, text="缓存时间(分钟):", width=15).pack(side=LEFT)
        self.cache_time_var = tk.StringVar(value=str(self.config.get("cache_time", 5)))
        cache_time_entry = ttk_bs.Entry(cache_time_frame, textvariable=self.cache_time_var, width=10)
        cache_time_entry.pack(side=LEFT, padx=(10, 0))
    
    def create_advanced_settings(self):
        """创建高级设置"""
        advanced_frame = ttk_bs.LabelFrame(self.scrollable_frame, text="高级设置", padding=20)
        advanced_frame.pack(fill=X, pady=(0, 20))
        
        # 调试模式
        debug_frame = ttk_bs.Frame(advanced_frame)
        debug_frame.pack(fill=X, pady=(0, 15))
        
        self.debug_mode_var = tk.BooleanVar(value=self.config.get("debug_mode", False))
        debug_check = ttk_bs.Checkbutton(
            debug_frame,
            text="调试模式",
            variable=self.debug_mode_var,
            bootstyle="primary"
        )
        debug_check.pack(side=LEFT)
        
        # 日志级别
        log_level_frame = ttk_bs.Frame(advanced_frame)
        log_level_frame.pack(fill=X, pady=(0, 15))
        
        ttk_bs.Label(log_level_frame, text="日志级别:", width=15).pack(side=LEFT)
        self.log_level_var = tk.StringVar(value=self.config.get("log_level", "INFO"))
        log_level_combo = ttk_bs.Combobox(
            log_level_frame,
            textvariable=self.log_level_var,
            values=["DEBUG", "INFO", "WARNING", "ERROR"],
            state="readonly",
            width=15
        )
        log_level_combo.pack(side=LEFT, padx=(10, 0))
        
        # 导出/导入配置
        export_frame = ttk_bs.Frame(advanced_frame)
        export_frame.pack(fill=X)
        
        export_btn = ttk_bs.Button(
            export_frame,
            text="导出配置",
            bootstyle="outline-info",
            command=self.export_config
        )
        export_btn.pack(side=LEFT, padx=(0, 10))
        
        import_btn = ttk_bs.Button(
            export_frame,
            text="导入配置",
            bootstyle="outline-info",
            command=self.import_config
        )
        import_btn.pack(side=LEFT)
    
    def create_action_buttons(self):
        """创建操作按钮"""
        action_frame = ttk_bs.Frame(self.scrollable_frame)
        action_frame.pack(fill=X, pady=(20, 0))
        
        # 保存按钮
        save_btn = ttk_bs.Button(
            action_frame,
            text="保存设置",
            bootstyle="success",
            command=self.save_settings
        )
        save_btn.pack(side=LEFT, padx=(0, 10))
        
        # 重置按钮
        reset_btn = ttk_bs.Button(
            action_frame,
            text="重置默认",
            bootstyle="warning",
            command=self.reset_settings
        )
        reset_btn.pack(side=LEFT, padx=(0, 10))
        
        # 应用按钮
        apply_btn = ttk_bs.Button(
            action_frame,
            text="应用",
            bootstyle="primary",
            command=self.apply_settings
        )
        apply_btn.pack(side=LEFT)
    
    def show(self):
        """显示框架"""
        if not self.is_visible:
            self.frame.pack(fill=BOTH, expand=True)
            self.is_visible = True
            self.load_settings()
    
    def hide(self):
        """隐藏框架"""
        if self.is_visible:
            self.frame.pack_forget()
            self.is_visible = False
    
    def load_settings(self):
        """加载设置"""
        config_data = self.config.load()
        
        # 更新界面控件的值
        self.server_url_var.set(config_data.get("server_url", ""))
        self.timeout_var.set(str(config_data.get("timeout", 30)))
        self.auto_reconnect_var.set(config_data.get("auto_reconnect", True))
        self.theme_var.set(config_data.get("theme", "darkly"))
        self.font_size_var.set(str(config_data.get("font_size", 10)))
        self.auto_refresh_var.set(config_data.get("auto_refresh", True))
        self.refresh_interval_var.set(str(config_data.get("refresh_interval", 30)))
        self.page_size_var.set(str(config_data.get("page_size", 20)))
        self.enable_cache_var.set(config_data.get("enable_cache", True))
        self.cache_time_var.set(str(config_data.get("cache_time", 5)))
        self.debug_mode_var.set(config_data.get("debug_mode", False))
        self.log_level_var.set(config_data.get("log_level", "INFO"))
    
    def save_settings(self):
        """保存设置"""
        try:
            # 收集所有设置
            settings = {
                "server_url": self.server_url_var.get().strip(),
                "timeout": int(self.timeout_var.get() or 30),
                "auto_reconnect": self.auto_reconnect_var.get(),
                "theme": self.theme_var.get(),
                "font_size": int(self.font_size_var.get() or 10),
                "auto_refresh": self.auto_refresh_var.get(),
                "refresh_interval": int(self.refresh_interval_var.get() or 30),
                "page_size": int(self.page_size_var.get() or 20),
                "enable_cache": self.enable_cache_var.get(),
                "cache_time": int(self.cache_time_var.get() or 5),
                "debug_mode": self.debug_mode_var.get(),
                "log_level": self.log_level_var.get()
            }
            
            # 保存配置
            self.config.update(settings)
            self.config.save()
            
            messagebox.showinfo("成功", "设置已保存")
            self.main_app.set_status("设置已保存")
            
        except ValueError as e:
            messagebox.showerror("错误", f"设置值无效: {str(e)}")
        except Exception as e:
            messagebox.showerror("错误", f"保存设置失败: {str(e)}")
    
    def reset_settings(self):
        """重置设置"""
        if messagebox.askyesno("确认重置", "确定要重置所有设置为默认值吗？"):
            self.config.reset()
            self.load_settings()
            messagebox.showinfo("成功", "设置已重置为默认值")
    
    def apply_settings(self):
        """应用设置"""
        self.save_settings()
        
        # 应用主题
        theme = self.theme_var.get()
        if hasattr(self.main_app, 'style'):
            self.main_app.style.theme_use(theme)
        
        messagebox.showinfo("成功", "设置已应用，部分设置需要重启应用后生效")
    
    def test_connection(self):
        """测试连接"""
        server_url = self.server_url_var.get().strip()
        if not server_url:
            messagebox.showwarning("警告", "请输入服务器地址")
            return
        
        def do_test():
            try:
                import requests
                response = requests.get(f"{server_url}/api/health", timeout=10)
                if response.status_code == 200:
                    self.parent.after(0, lambda: messagebox.showinfo("成功", "连接测试成功"))
                else:
                    self.parent.after(0, lambda: messagebox.showerror("失败", f"连接失败: HTTP {response.status_code}"))
            except Exception as e:
                self.parent.after(0, lambda: messagebox.showerror("失败", f"连接失败: {str(e)}"))
        
        threading.Thread(target=do_test, daemon=True).start()
    
    def export_config(self):
        """导出配置"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="导出配置",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if file_path:
                config_data = self.config.load()
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, indent=2, ensure_ascii=False)
                
                messagebox.showinfo("成功", f"配置已导出到: {file_path}")
        except Exception as e:
            messagebox.showerror("错误", f"导出配置失败: {str(e)}")
    
    def import_config(self):
        """导入配置"""
        try:
            file_path = filedialog.askopenfilename(
                title="导入配置",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 验证配置数据
                if isinstance(config_data, dict):
                    self.config.update(config_data)
                    self.config.save()
                    self.load_settings()
                    messagebox.showinfo("成功", "配置已导入")
                else:
                    messagebox.showerror("错误", "无效的配置文件格式")
        except Exception as e:
            messagebox.showerror("错误", f"导入配置失败: {str(e)}")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
91JSPG.COM 桌面管理应用 - 主启动脚本
"""

import sys
import os

def main():
    """主函数"""
    print("=" * 50)
    print("91JSPG.COM 桌面管理应用")
    print("=" * 50)
    
    try:
        # 添加当前目录到路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)
        
        # 设置环境变量，禁用DPI相关功能
        os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '0'
        os.environ['QT_SCALE_FACTOR'] = '1'
        
        print("正在启动应用...")
        
        # 导入并启动修复版应用
        from start_fixed import main as start_fixed_main
        start_fixed_main()
        
    except KeyboardInterrupt:
        print("\n用户取消启动")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版启动脚本 - 完全独立的应用启动
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
from datetime import datetime

def main():
    """主函数"""
    print("=" * 50)
    print("91JSPG.COM 桌面管理应用 - 修复版启动")
    print("=" * 50)
    
    # 设置环境变量，完全禁用DPI相关功能
    os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '0'
    os.environ['QT_SCALE_FACTOR'] = '1'
    os.environ['QT_DEVICE_PIXEL_RATIO'] = '1'
    os.environ['QT_SCREEN_SCALE_FACTORS'] = '1'
    
    try:
        # 添加当前目录到路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)
        
        print("1. 检查基本模块...")
        
        # 检查tkinter
        import tkinter as tk
        print("✓ tkinter")
        
        # 检查ttkbootstrap
        import ttkbootstrap as ttk_bs
        print("✓ ttkbootstrap")
        
        # 检查其他依赖
        import requests
        print("✓ requests")
        
        print("\n2. 检查应用模块...")
        
        # 检查配置
        from config import config
        print("✓ config")
        
        # 检查API客户端
        from api_client import api_client
        print("✓ api_client")
        
        print("\n3. 创建独立应用...")
        
        # 创建独立的应用类
        class FixedVideoAdminApp:
            def __init__(self):
                # 创建主窗口
                self.root = ttk_bs.Window(
                    title="91JSPG.COM 桌面管理系统",
                    themename="darkly",
                    size=(1200, 800)
                )
                
                # 设置窗口属性
                self.root.resizable(True, True)
                self.root.minsize(1000, 600)
                
                # 居中显示
                self.center_window()
                
                # 当前用户信息
                self.current_admin = None
                self.is_logged_in = False
                
                # 创建界面
                self.create_interface()
                
                # 绑定窗口关闭事件
                self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
            def center_window(self):
                """居中显示窗口"""
                self.root.update_idletasks()
                width = self.root.winfo_width()
                height = self.root.winfo_height()
                x = (self.root.winfo_screenwidth() // 2) - (width // 2)
                y = (self.root.winfo_screenheight() // 2) - (height // 2)
                self.root.geometry(f"{width}x{height}+{x}+{y}")
            
            def create_interface(self):
                """创建界面"""
                # 主框架
                main_frame = ttk_bs.Frame(self.root)
                main_frame.pack(fill='both', expand=True, padx=20, pady=20)
                
                # 标题
                title_label = ttk_bs.Label(
                    main_frame,
                    text="91JSPG.COM 桌面管理系统",
                    font=("Arial", 20, "bold")
                )
                title_label.pack(pady=30)
                
                # 状态信息
                status_frame = ttk_bs.LabelFrame(main_frame, text="系统状态", padding=20)
                status_frame.pack(fill='x', pady=20)
                
                ttk_bs.Label(status_frame, text="✓ 应用启动成功", font=("Arial", 12)).pack(anchor='w', pady=5)
                ttk_bs.Label(status_frame, text="✓ 所有依赖模块正常", font=("Arial", 12)).pack(anchor='w', pady=5)
                ttk_bs.Label(status_frame, text="✓ GUI组件工作正常", font=("Arial", 12)).pack(anchor='w', pady=5)
                
                # 功能区域
                function_frame = ttk_bs.LabelFrame(main_frame, text="管理功能", padding=20)
                function_frame.pack(fill='both', expand=True, pady=20)
                
                # 服务器配置
                server_frame = ttk_bs.Frame(function_frame)
                server_frame.pack(fill='x', pady=10)
                
                ttk_bs.Label(server_frame, text="服务器地址:", font=("Arial", 12)).pack(side='left')
                self.server_var = tk.StringVar(value="http://localhost:3001")
                server_entry = ttk_bs.Entry(server_frame, textvariable=self.server_var, width=40)
                server_entry.pack(side='left', padx=10)
                
                ttk_bs.Button(
                    server_frame,
                    text="测试连接",
                    command=self.test_connection,
                    bootstyle="info"
                ).pack(side='left', padx=10)
                
                # 登录区域
                login_frame = ttk_bs.Frame(function_frame)
                login_frame.pack(fill='x', pady=20)
                
                # 用户名
                user_frame = ttk_bs.Frame(login_frame)
                user_frame.pack(fill='x', pady=5)
                ttk_bs.Label(user_frame, text="用户名:", width=10).pack(side='left')
                self.username_var = tk.StringVar()
                ttk_bs.Entry(user_frame, textvariable=self.username_var, width=30).pack(side='left', padx=10)
                
                # 密码
                pass_frame = ttk_bs.Frame(login_frame)
                pass_frame.pack(fill='x', pady=5)
                ttk_bs.Label(pass_frame, text="密码:", width=10).pack(side='left')
                self.password_var = tk.StringVar()
                ttk_bs.Entry(pass_frame, textvariable=self.password_var, show="*", width=30).pack(side='left', padx=10)
                
                # 按钮区域
                button_frame = ttk_bs.Frame(function_frame)
                button_frame.pack(pady=30)
                
                ttk_bs.Button(
                    button_frame,
                    text="登录",
                    command=self.login,
                    bootstyle="success",
                    width=15
                ).pack(side='left', padx=10)
                
                ttk_bs.Button(
                    button_frame,
                    text="设置",
                    command=self.show_settings,
                    bootstyle="warning",
                    width=15
                ).pack(side='left', padx=10)
                
                ttk_bs.Button(
                    button_frame,
                    text="关于",
                    command=self.show_about,
                    bootstyle="info",
                    width=15
                ).pack(side='left', padx=10)
                
                # 状态栏
                self.status_var = tk.StringVar(value="就绪")
                status_bar = ttk_bs.Label(
                    main_frame,
                    textvariable=self.status_var,
                    relief='sunken',
                    anchor='w'
                )
                status_bar.pack(fill='x', side='bottom')
            
            def test_connection(self):
                """测试服务器连接"""
                try:
                    import requests
                    server_url = self.server_var.get().rstrip('/')
                    self.status_var.set("正在测试连接...")
                    self.root.update()

                    # 尝试多个可能的路径来测试连接
                    test_paths = [
                        "/health",  # 健康检查 (优先)
                        "",  # 根路径
                        "/api",  # API根路径
                        "/api/admin",  # 管理API路径
                    ]

                    success = False
                    last_error = None

                    for path in test_paths:
                        try:
                            test_url = f"{server_url}{path}"
                            response = requests.get(test_url, timeout=5)

                            # 任何非5xx错误都认为是连接成功
                            if response.status_code < 500:
                                success = True
                                self.status_var.set("✓ 服务器连接成功")
                                messagebox.showinfo("成功",
                                    f"服务器连接正常！\n"
                                    f"测试URL: {test_url}\n"
                                    f"响应状态: {response.status_code}")
                                break

                        except requests.exceptions.RequestException:
                            continue

                    if not success:
                        self.status_var.set("❌ 服务器无响应")
                        messagebox.showerror("连接失败",
                            f"无法连接到服务器: {server_url}\n\n"
                            f"请检查:\n"
                            f"1. 服务器地址是否正确\n"
                            f"2. 服务器是否正在运行\n"
                            f"3. 端口是否正确\n"
                            f"4. 防火墙设置")

                except Exception as e:
                    self.status_var.set("❌ 测试失败")
                    messagebox.showerror("错误", f"测试连接时发生错误:\n{str(e)}")
            
            def login(self):
                """登录"""
                username = self.username_var.get()
                password = self.password_var.get()
                
                if not username or not password:
                    messagebox.showerror("错误", "请输入用户名和密码")
                    return

                try:
                    self.status_var.set("正在登录...")
                    self.root.update()

                    # 这里可以调用实际的登录API
                    result = api_client.login(username, password)

                    if result.get('success'):
                        self.status_var.set("✓ 登录成功")
                        self.current_admin = result.get('admin')
                        self.is_logged_in = True
                        messagebox.showinfo("成功", "登录成功！即将切换到管理界面...")
                        # 切换到完整的管理界面
                        self.switch_to_main_app()
                    else:
                        self.status_var.set("❌ 登录失败")
                        messagebox.showerror("错误", result.get('message', '登录失败'))

                except Exception as e:
                    self.status_var.set("❌ 登录异常")
                    messagebox.showerror("错误", f"登录时发生错误:\n{str(e)}")

            def switch_to_main_app(self):
                """切换到完整的管理应用"""
                try:
                    # 保存登录信息到配置文件
                    from config import config
                    config.set("current_admin", self.current_admin)
                    config.set("is_logged_in", True)
                    config.set("login_time", str(datetime.now()))

                    # 显示切换提示
                    self.status_var.set("正在切换到管理界面...")
                    self.root.update()

                    # 关闭当前窗口
                    self.root.destroy()

                    # 启动新进程运行完整应用
                    import subprocess
                    import sys

                    # 使用subprocess启动主应用
                    subprocess.Popen([sys.executable, "app.py"],
                                   cwd=os.path.dirname(os.path.abspath(__file__)))

                except Exception as e:
                    # 如果切换失败，恢复当前窗口
                    try:
                        self.root.deiconify()
                        messagebox.showerror("错误", f"切换到管理界面失败:\n{str(e)}")
                        self.status_var.set("❌ 界面切换失败")
                    except:
                        # 如果窗口已经被销毁，创建新的错误对话框
                        import tkinter as tk
                        root = tk.Tk()
                        root.withdraw()
                        messagebox.showerror("错误", f"切换到管理界面失败:\n{str(e)}")
                        root.destroy()

            def show_settings(self):
                """显示设置"""
                messagebox.showinfo("设置", "设置功能开发中...")

            def show_about(self):
                """显示关于"""
                about_text = """
91JSPG.COM 桌面管理系统

版本: 1.0.0
开发: Python + ttkbootstrap
功能: 视频CMS后台管理

这是一个用于管理91JSPG.COM视频网站的桌面应用程序。
通过连接后端API来实现各种管理功能。
                """
                messagebox.showinfo("关于", about_text)

            def on_closing(self):
                """窗口关闭事件"""
                if messagebox.askokcancel("退出", "确定要退出应用程序吗？"):
                    self.root.destroy()
            
            def run(self):
                """运行应用"""
                self.root.mainloop()
        
        print("✓ 应用类创建成功")
        
        print("\n4. 启动应用...")
        app = FixedVideoAdminApp()
        app.run()
        
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()

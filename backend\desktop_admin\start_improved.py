#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
91JSPG.COM 桌面管理应用 - 改进版
基于简化版的成功经验，创建完整的管理界面
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
from datetime import datetime

def main():
    """主函数"""
    print("=" * 50)
    print("91JSPG.COM 桌面管理应用 - 改进版")
    print("=" * 50)
    
    try:
        # 添加当前目录到路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)
        
        print("1. 检查基本模块...")
        
        # 检查ttkbootstrap
        import ttkbootstrap as ttk_bs
        print("✓ ttkbootstrap")
        
        # 检查配置和API客户端
        from config import config
        from api_client import api_client
        print("✓ config & api_client")
        
        print("\n2. 创建改进应用...")
        
        # 创建改进的应用类
        class ImprovedVideoAdminApp:
            def __init__(self):
                # 创建主窗口
                self.root = ttk_bs.Window(
                    title="91JSPG.COM 桌面管理系统",
                    themename="darkly",
                    size=(1200, 800)
                )
                
                # 设置窗口属性
                self.root.resizable(True, True)
                self.root.minsize(1000, 600)
                
                # 居中显示
                self.center_window()
                
                # 当前用户信息
                self.current_admin = None
                self.is_logged_in = False
                self.current_page = "dashboard"

                # 视频管理相关变量
                self.current_video_page = 1
                self.total_video_pages = 1

                # 分类数据缓存
                self.categories_cache = []
                self.categories_map = {}  # 分类名称到ID的映射
                
                # 创建登录界面
                self.create_login_interface()
                
                # 绑定窗口关闭事件
                self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
            def center_window(self):
                """居中显示窗口"""
                self.root.update_idletasks()
                width = self.root.winfo_width()
                height = self.root.winfo_height()
                x = (self.root.winfo_screenwidth() // 2) - (width // 2)
                y = (self.root.winfo_screenheight() // 2) - (height // 2)
                self.root.geometry(f"{width}x{height}+{x}+{y}")
            
            def create_login_interface(self):
                """创建登录界面"""
                # 清除现有内容
                for widget in self.root.winfo_children():
                    widget.destroy()
                
                # 主框架
                main_frame = ttk_bs.Frame(self.root)
                main_frame.pack(fill='both', expand=True, padx=20, pady=20)
                
                # 标题
                title_label = ttk_bs.Label(
                    main_frame,
                    text="91JSPG.COM 桌面管理系统",
                    font=("Arial", 24, "bold")
                )
                title_label.pack(pady=50)
                
                # 登录框架
                login_frame = ttk_bs.LabelFrame(main_frame, text="管理员登录", padding=30)
                login_frame.pack(pady=30)
                
                # 服务器配置
                server_frame = ttk_bs.Frame(login_frame)
                server_frame.pack(fill='x', pady=10)
                
                ttk_bs.Label(server_frame, text="服务器地址:", font=("Arial", 12)).pack(side='left')
                self.server_var = tk.StringVar(value="http://localhost:3001")
                server_entry = ttk_bs.Entry(server_frame, textvariable=self.server_var, width=40)
                server_entry.pack(side='left', padx=10)
                
                ttk_bs.Button(
                    server_frame,
                    text="测试连接",
                    command=self.test_connection,
                    bootstyle="info"
                ).pack(side='left', padx=10)
                
                # 用户名
                user_frame = ttk_bs.Frame(login_frame)
                user_frame.pack(fill='x', pady=10)
                ttk_bs.Label(user_frame, text="用户名:", width=10, font=("Arial", 12)).pack(side='left')
                self.username_var = tk.StringVar()
                ttk_bs.Entry(user_frame, textvariable=self.username_var, width=30, font=("Arial", 12)).pack(side='left', padx=10)
                
                # 密码
                pass_frame = ttk_bs.Frame(login_frame)
                pass_frame.pack(fill='x', pady=10)
                ttk_bs.Label(pass_frame, text="密码:", width=10, font=("Arial", 12)).pack(side='left')
                self.password_var = tk.StringVar()
                password_entry = ttk_bs.Entry(pass_frame, textvariable=self.password_var, show="*", width=30, font=("Arial", 12))
                password_entry.pack(side='left', padx=10)
                
                # 绑定回车键登录
                password_entry.bind('<Return>', lambda e: self.login())
                
                # 登录按钮
                button_frame = ttk_bs.Frame(login_frame)
                button_frame.pack(pady=30)
                
                ttk_bs.Button(
                    button_frame,
                    text="登录",
                    command=self.login,
                    bootstyle="success",
                    width=20
                ).pack()
                
                # 状态栏
                self.status_var = tk.StringVar(value="请输入用户名和密码登录")
                status_bar = ttk_bs.Label(
                    main_frame,
                    textvariable=self.status_var,
                    relief='sunken',
                    anchor='w'
                )
                status_bar.pack(fill='x', side='bottom', pady=(20, 0))
            
            def test_connection(self):
                """测试服务器连接"""
                try:
                    import requests
                    server_url = self.server_var.get().rstrip('/')
                    self.status_var.set("正在测试连接...")
                    self.root.update()
                    
                    # 测试健康检查接口
                    response = requests.get(f"{server_url}/health", timeout=5)
                    
                    if response.status_code == 200:
                        self.status_var.set("✓ 服务器连接成功")
                        messagebox.showinfo("成功", "服务器连接正常！")
                    else:
                        # 尝试根路径
                        response = requests.get(server_url, timeout=5)
                        if response.status_code < 500:
                            self.status_var.set("✓ 服务器连接成功")
                            messagebox.showinfo("成功", "服务器连接正常！")
                        else:
                            self.status_var.set("❌ 服务器响应异常")
                            messagebox.showerror("错误", f"服务器响应异常: {response.status_code}")
                        
                except requests.exceptions.RequestException as e:
                    self.status_var.set("❌ 连接失败")
                    messagebox.showerror("连接错误", f"无法连接到服务器:\n{str(e)}")
                except Exception as e:
                    self.status_var.set("❌ 测试失败")
                    messagebox.showerror("错误", f"测试连接时发生错误:\n{str(e)}")
            
            def login(self):
                """登录"""
                username = self.username_var.get()
                password = self.password_var.get()
                
                if not username or not password:
                    messagebox.showerror("错误", "请输入用户名和密码")
                    return
                
                try:
                    self.status_var.set("正在登录...")
                    self.root.update()
                    
                    # 更新API客户端的服务器地址
                    server_url = self.server_var.get().rstrip('/')
                    api_client.set_base_url(server_url)
                    
                    # 调用登录API
                    print(f"尝试登录到: {server_url}")
                    result = api_client.login(username, password)
                    print(f"登录结果: {result}")
                    
                    # 检查结果是否为None
                    if result is None:
                        self.status_var.set("❌ 登录失败")
                        messagebox.showerror("错误", "服务器无响应或返回无效数据")
                        return
                    
                    if result.get('success'):
                        self.status_var.set("✓ 登录成功")
                        # 从正确的路径获取管理员信息
                        data = result.get('data', {})
                        self.current_admin = data.get('admin', {})
                        self.is_logged_in = True
                        
                        print(f"管理员信息: {self.current_admin}")
                        
                        # 切换到管理界面
                        self.create_admin_interface()
                    else:
                        self.status_var.set("❌ 登录失败")
                        messagebox.showerror("错误", result.get('message', '登录失败'))
                        
                except Exception as e:
                    self.status_var.set("❌ 登录异常")
                    print(f"登录异常详细信息: {e}")
                    import traceback
                    traceback.print_exc()
                    messagebox.showerror("错误", f"登录时发生错误:\n{str(e)}")
            
            def create_admin_interface(self):
                """创建管理界面"""
                try:
                    # 清除现有内容
                    for widget in self.root.winfo_children():
                        widget.destroy()
                    
                    # 主框架
                    main_frame = ttk_bs.Frame(self.root)
                    main_frame.pack(fill='both', expand=True)
                    
                    # 创建顶部工具栏
                    self.create_toolbar(main_frame)
                    
                    # 创建主要内容区域
                    content_container = ttk_bs.Frame(main_frame)
                    content_container.pack(fill='both', expand=True, padx=5, pady=5)
                    
                    # 左侧导航
                    self.create_navigation(content_container)
                    
                    # 右侧内容区域
                    self.content_frame = ttk_bs.Frame(content_container)
                    self.content_frame.pack(side='right', fill='both', expand=True)
                    
                    # 底部状态栏
                    self.create_status_bar(main_frame)
                    
                    # 默认显示仪表盘
                    self.switch_page("dashboard")
                    
                except Exception as e:
                    print(f"创建管理界面时出错: {e}")
                    import traceback
                    traceback.print_exc()
                    messagebox.showerror("界面错误", f"创建管理界面时发生错误:\n{str(e)}")
            
            def create_toolbar(self, parent):
                """创建顶部工具栏"""
                toolbar = ttk_bs.Frame(parent)
                toolbar.pack(fill='x', padx=5, pady=5)
                
                # 左侧 - 系统标题
                ttk_bs.Label(
                    toolbar,
                    text="91JSPG.COM 管理系统",
                    font=("Arial", 16, "bold")
                ).pack(side='left', padx=10, pady=5)
                
                # 右侧 - 用户信息和操作
                user_frame = ttk_bs.Frame(toolbar)
                user_frame.pack(side='right', fill='y')
                
                # 用户信息
                username = "管理员"
                if self.current_admin and isinstance(self.current_admin, dict):
                    username = self.current_admin.get('username', '管理员')
                
                ttk_bs.Label(
                    user_frame,
                    text=f"欢迎，{username}",
                    font=("Arial", 12)
                ).pack(side='left', padx=10, pady=5)
                
                # 退出按钮
                ttk_bs.Button(
                    user_frame,
                    text="退出登录",
                    command=self.logout,
                    width=10
                ).pack(side='right', padx=10, pady=5)
            
            def create_navigation(self, parent):
                """创建左侧导航菜单"""
                nav_frame = ttk_bs.LabelFrame(parent, text="导航菜单", padding=10)
                nav_frame.pack(side='left', fill='y', padx=(0, 5))
                
                # 导航按钮配置
                nav_buttons = [
                    ("dashboard", "🏠 仪表盘"),
                    ("videos", "🎬 视频管理"),
                    ("categories", "📁 分类管理"),
                    ("admins", "👥 管理员"),
                    ("settings", "⚙️ 系统设置")
                ]
                
                self.nav_buttons = {}
                
                for page_id, title in nav_buttons:
                    btn = ttk_bs.Button(
                        nav_frame,
                        text=title,
                        width=15,
                        command=lambda p=page_id: self.switch_page(p)
                    )
                    btn.pack(fill='x', pady=5)
                    self.nav_buttons[page_id] = btn
            
            def create_status_bar(self, parent):
                """创建底部状态栏"""
                self.status_var.set(f"管理界面已加载 - 登录时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                status_bar = ttk_bs.Label(
                    parent,
                    textvariable=self.status_var,
                    relief='sunken',
                    anchor='w'
                )
                status_bar.pack(fill='x', side='bottom', padx=5, pady=5)
            
            def switch_page(self, page_id):
                """切换页面"""
                try:
                    # 清除当前内容
                    for widget in self.content_frame.winfo_children():
                        widget.destroy()
                    
                    self.current_page = page_id
                    
                    # 根据页面ID显示对应内容
                    if page_id == "dashboard":
                        self.show_dashboard_page()
                    elif page_id == "videos":
                        self.show_videos_page()
                    elif page_id == "categories":
                        self.show_categories_page()
                    elif page_id == "admins":
                        self.show_admins_page()
                    elif page_id == "settings":
                        self.show_settings_page()
                    
                    self.status_var.set(f"当前页面: {page_id} - {datetime.now().strftime('%H:%M:%S')}")
                    
                except Exception as e:
                    print(f"切换页面时出错: {e}")
                    import traceback
                    traceback.print_exc()
                    messagebox.showerror("页面错误", f"切换页面时发生错误:\n{str(e)}")

            def show_dashboard_page(self):
                """显示仪表盘页面"""
                # 页面标题
                title_frame = ttk_bs.Frame(self.content_frame)
                title_frame.pack(fill='x', pady=(0, 20))

                ttk_bs.Label(
                    title_frame,
                    text="🏠 系统仪表盘",
                    font=("Arial", 20, "bold")
                ).pack(side='left')

                ttk_bs.Button(
                    title_frame,
                    text="🔄 刷新数据",
                    command=self.refresh_dashboard,
                    bootstyle="info"
                ).pack(side='right')

                # 统计卡片区域
                stats_frame = ttk_bs.LabelFrame(self.content_frame, text="系统统计", padding=15)
                stats_frame.pack(fill='x', pady=(0, 20))

                # 创建统计卡片容器
                self.stats_cards_frame = ttk_bs.Frame(stats_frame)
                self.stats_cards_frame.pack(fill='x')

                # 快速操作区域
                actions_frame = ttk_bs.LabelFrame(self.content_frame, text="快速操作", padding=15)
                actions_frame.pack(fill='both', expand=True)

                # 操作按钮
                actions_data = [
                    ("添加视频", "上传新的视频内容", self.quick_add_video),
                    ("管理分类", "查看和编辑分类", lambda: self.switch_page("categories")),
                    ("系统设置", "配置系统参数", lambda: self.switch_page("settings")),
                    ("查看日志", "查看系统日志", self.view_logs)
                ]

                for title, desc, command in actions_data:
                    action_frame = ttk_bs.Frame(actions_frame)
                    action_frame.pack(fill='x', pady=5)

                    ttk_bs.Button(
                        action_frame,
                        text=title,
                        command=command,
                        width=15
                    ).pack(side='left', padx=10)

                    ttk_bs.Label(action_frame, text=desc, font=("Arial", 10)).pack(side='left', padx=10)

                # 初始加载仪表盘数据
                self.refresh_dashboard()

            def show_videos_page(self):
                """显示视频管理页面"""
                # 页面标题
                title_frame = ttk_bs.Frame(self.content_frame)
                title_frame.pack(fill='x', pady=(0, 20))

                ttk_bs.Label(
                    title_frame,
                    text="🎬 视频管理",
                    font=("Arial", 20, "bold")
                ).pack(side='left')

                # 操作按钮
                btn_frame = ttk_bs.Frame(title_frame)
                btn_frame.pack(side='right')

                ttk_bs.Button(
                    btn_frame,
                    text="➕ 添加视频",
                    command=self.add_video,
                    bootstyle="success"
                ).pack(side='left', padx=5)

                ttk_bs.Button(
                    btn_frame,
                    text="📊 统计",
                    command=self.show_video_stats,
                    bootstyle="warning"
                ).pack(side='left', padx=5)

                ttk_bs.Button(
                    btn_frame,
                    text="📤 批量操作",
                    command=self.show_batch_operations,
                    bootstyle="secondary"
                ).pack(side='left', padx=5)

                ttk_bs.Button(
                    btn_frame,
                    text="🔄 刷新",
                    command=self.refresh_videos,
                    bootstyle="info"
                ).pack(side='left', padx=5)

                # 搜索和筛选区域
                search_frame = ttk_bs.LabelFrame(self.content_frame, text="搜索和筛选", padding=10)
                search_frame.pack(fill='x', pady=(0, 20))

                # 第一行：搜索
                search_row1 = ttk_bs.Frame(search_frame)
                search_row1.pack(fill='x', pady=(0, 10))

                ttk_bs.Label(search_row1, text="搜索:").pack(side='left', padx=(0, 5))
                self.video_search_var = tk.StringVar()
                search_entry = ttk_bs.Entry(search_row1, textvariable=self.video_search_var, width=30)
                search_entry.pack(side='left', padx=5)

                # 绑定回车键搜索
                search_entry.bind('<Return>', lambda e: self.search_videos())

                ttk_bs.Button(
                    search_row1,
                    text="🔍 搜索",
                    command=self.search_videos,
                    bootstyle="primary"
                ).pack(side='left', padx=5)

                ttk_bs.Button(
                    search_row1,
                    text="🔄 重置",
                    command=self.reset_video_search,
                    bootstyle="secondary"
                ).pack(side='left', padx=5)

                # 第二行：筛选
                search_row2 = ttk_bs.Frame(search_frame)
                search_row2.pack(fill='x')

                ttk_bs.Label(search_row2, text="分类:").pack(side='left', padx=(0, 5))
                self.video_category_var = tk.StringVar(value="全部")
                category_combo = ttk_bs.Combobox(
                    search_row2,
                    textvariable=self.video_category_var,
                    width=15,
                    state="readonly"
                )
                category_combo['values'] = ["全部", "电影", "电视剧", "综艺", "动漫", "纪录片"]
                category_combo.pack(side='left', padx=5)
                category_combo.bind('<<ComboboxSelected>>', lambda e: self.filter_videos())

                ttk_bs.Label(search_row2, text="状态:").pack(side='left', padx=(10, 5))
                self.video_status_var = tk.StringVar(value="全部")
                status_combo = ttk_bs.Combobox(
                    search_row2,
                    textvariable=self.video_status_var,
                    width=10,
                    state="readonly"
                )
                status_combo['values'] = ["全部", "已发布", "草稿", "审核中", "已下架"]
                status_combo.pack(side='left', padx=5)
                status_combo.bind('<<ComboboxSelected>>', lambda e: self.filter_videos())

                # 分页信息
                self.video_page_info_var = tk.StringVar(value="第 1 页，共 0 条记录")
                ttk_bs.Label(search_row2, textvariable=self.video_page_info_var).pack(side='right', padx=10)

                # 视频列表区域
                list_frame = ttk_bs.LabelFrame(self.content_frame, text="视频列表", padding=10)
                list_frame.pack(fill='both', expand=True)

                # 创建增强的视频表格
                self.create_enhanced_videos_table(list_frame)

                # 分页控制
                self.create_video_pagination(list_frame)

                # 初始化分页变量
                self.current_video_page = 1
                self.video_page_size = 20
                self.total_video_pages = 1

                # 加载视频数据
                self.refresh_videos()

            def show_categories_page(self):
                """显示分类管理页面"""
                # 页面标题
                title_frame = ttk_bs.Frame(self.content_frame)
                title_frame.pack(fill='x', pady=(0, 20))

                ttk_bs.Label(
                    title_frame,
                    text="📁 分类管理",
                    font=("Arial", 20, "bold")
                ).pack(side='left')

                ttk_bs.Button(
                    title_frame,
                    text="➕ 添加分类",
                    command=self.add_category,
                    bootstyle="success"
                ).pack(side='right')

                # 分类列表区域
                list_frame = ttk_bs.LabelFrame(self.content_frame, text="分类列表", padding=20)
                list_frame.pack(fill='both', expand=True)

                # 创建简化的分类表格
                self.create_simple_categories_table(list_frame)

                # 加载分类数据
                self.refresh_categories()

            def show_admins_page(self):
                """显示管理员管理页面"""
                # 页面标题
                title_frame = ttk_bs.Frame(self.content_frame)
                title_frame.pack(fill='x', pady=(0, 20))

                ttk_bs.Label(
                    title_frame,
                    text="👥 管理员管理",
                    font=("Arial", 20, "bold")
                ).pack(side='left')

                ttk_bs.Button(
                    title_frame,
                    text="➕ 添加管理员",
                    command=self.add_admin,
                    bootstyle="success"
                ).pack(side='right')

                # 内容区域
                content = ttk_bs.LabelFrame(self.content_frame, text="管理员列表", padding=20)
                content.pack(fill='both', expand=True)

                ttk_bs.Label(
                    content,
                    text="管理员管理功能开发中...\n\n将包含:\n• 查看管理员列表\n• 创建新管理员\n• 编辑管理员信息\n• 删除管理员\n• 权限管理",
                    font=("Arial", 12),
                    justify='center'
                ).pack(expand=True)

            def show_settings_page(self):
                """显示系统设置页面"""
                # 页面标题
                title_frame = ttk_bs.Frame(self.content_frame)
                title_frame.pack(fill='x', pady=(0, 20))

                ttk_bs.Label(
                    title_frame,
                    text="⚙️ 系统设置",
                    font=("Arial", 20, "bold")
                ).pack(side='left')

                # 设置内容
                settings_frame = ttk_bs.LabelFrame(self.content_frame, text="系统配置", padding=20)
                settings_frame.pack(fill='both', expand=True)

                # 服务器设置
                server_frame = ttk_bs.LabelFrame(settings_frame, text="服务器设置", padding=15)
                server_frame.pack(fill='x', pady=(0, 20))

                ttk_bs.Label(server_frame, text="服务器地址:").pack(anchor='w')
                ttk_bs.Label(server_frame, text=self.server_var.get(), font=("Arial", 10, "bold")).pack(anchor='w', padx=20)

                # 用户信息
                user_frame = ttk_bs.LabelFrame(settings_frame, text="当前用户", padding=15)
                user_frame.pack(fill='x', pady=(0, 20))

                if self.current_admin:
                    ttk_bs.Label(user_frame, text=f"用户名: {self.current_admin.get('username', 'N/A')}").pack(anchor='w')
                    ttk_bs.Label(user_frame, text=f"邮箱: {self.current_admin.get('email', 'N/A')}").pack(anchor='w')
                    ttk_bs.Label(user_frame, text=f"角色: {self.current_admin.get('name', 'N/A')}").pack(anchor='w')

                # 系统信息
                system_frame = ttk_bs.LabelFrame(settings_frame, text="系统信息", padding=15)
                system_frame.pack(fill='x')

                ttk_bs.Label(system_frame, text="应用版本: v1.0.0").pack(anchor='w')
                ttk_bs.Label(system_frame, text="主题: darkly").pack(anchor='w')
                ttk_bs.Label(system_frame, text=f"窗口大小: {self.root.winfo_width()}x{self.root.winfo_height()}").pack(anchor='w')

            def create_enhanced_videos_table(self, parent):
                """创建增强的视频表格"""
                # 表格容器框架
                table_container = ttk_bs.Frame(parent)
                table_container.pack(fill='both', expand=True, pady=(0, 10))

                # 表格框架（用于滚动条布局）
                table_frame = ttk_bs.Frame(table_container)
                table_frame.pack(fill='both', expand=True)

                # 表格列定义
                columns = ("ID", "标题", "分类", "状态", "观看数", "时长", "大小", "创建时间")

                # 创建Treeview - 支持多选
                self.videos_tree = ttk_bs.Treeview(
                    table_frame,
                    columns=columns,
                    show='headings',
                    height=12,  # 减少高度，避免过多空白
                    selectmode='extended'  # 支持多选
                )

                # 设置列标题和宽度 - 优化列宽分配
                column_configs = [
                    ("ID", 60, "center"),
                    ("标题", 300, "w"),  # 增加标题列宽度
                    ("分类", 100, "center"),
                    ("状态", 80, "center"),
                    ("观看数", 100, "e"),  # 数字右对齐
                    ("时长", 80, "center"),
                    ("大小", 80, "e"),  # 数字右对齐
                    ("创建时间", 120, "center")
                ]

                for i, (col, width, anchor) in enumerate(column_configs):
                    self.videos_tree.heading(col, text=col, anchor='center')
                    self.videos_tree.column(col, width=width, minwidth=60, anchor=anchor)

                # 创建滚动条框架
                scrollbar_frame = ttk_bs.Frame(table_frame)

                # 垂直滚动条
                v_scrollbar = tk.Scrollbar(scrollbar_frame, orient='vertical', command=self.videos_tree.yview)
                self.videos_tree.configure(yscrollcommand=v_scrollbar.set)

                # 水平滚动条
                h_scrollbar = tk.Scrollbar(table_frame, orient='horizontal', command=self.videos_tree.xview)
                self.videos_tree.configure(xscrollcommand=h_scrollbar.set)

                # 布局 - 改进滚动条布局
                self.videos_tree.grid(row=0, column=0, sticky='nsew')
                v_scrollbar.grid(row=0, column=1, sticky='ns')
                h_scrollbar.grid(row=1, column=0, sticky='ew')

                # 配置网格权重
                table_frame.grid_rowconfigure(0, weight=1)
                table_frame.grid_columnconfigure(0, weight=1)

                # 绑定事件
                self.videos_tree.bind('<Double-1>', self.edit_video)
                self.videos_tree.bind('<Button-3>', self.show_video_context_menu)

                # 设置表格样式
                self.setup_table_style()

                # 创建右键菜单
                self.create_video_context_menu()

            def setup_table_style(self):
                """设置表格样式"""
                try:
                    # 配置行高和字体
                    style = ttk_bs.Style()
                    style.configure("Treeview", rowheight=25, font=("Arial", 9))
                    style.configure("Treeview.Heading", font=("Arial", 9, "bold"))

                    # 设置交替行颜色
                    self.videos_tree.tag_configure('oddrow', background='#f8f9fa')
                    self.videos_tree.tag_configure('evenrow', background='#ffffff')

                    # 设置状态颜色
                    self.videos_tree.tag_configure('active', foreground='#28a745')
                    self.videos_tree.tag_configure('inactive', foreground='#6c757d')
                    self.videos_tree.tag_configure('draft', foreground='#ffc107')

                except Exception as e:
                    print(f"设置表格样式失败: {e}")

            def create_video_pagination(self, parent):
                """创建视频分页控制"""
                # 分页控制容器
                pagination_container = ttk_bs.Frame(parent)
                pagination_container.pack(fill='x', pady=(15, 0))

                # 分页控制框架 - 使用LabelFrame增加视觉层次
                pagination_frame = ttk_bs.LabelFrame(pagination_container, text="分页控制", padding=10)
                pagination_frame.pack(fill='x')

                # 左侧：每页显示数量和记录信息
                left_frame = ttk_bs.Frame(pagination_frame)
                left_frame.pack(side='left')

                ttk_bs.Label(left_frame, text="每页显示:").pack(side='left', padx=(0, 5))
                self.page_size_var = tk.StringVar(value="20")
                page_size_combo = ttk_bs.Combobox(
                    left_frame,
                    textvariable=self.page_size_var,
                    width=6,
                    state="readonly"
                )
                page_size_combo['values'] = ["10", "20", "50", "100"]
                page_size_combo.pack(side='left', padx=(0, 15))
                page_size_combo.bind('<<ComboboxSelected>>', lambda e: self.change_page_size())

                # 记录信息
                self.record_info_label = ttk_bs.Label(left_frame, text="")
                self.record_info_label.pack(side='left', padx=(0, 10))

                # 右侧：分页按钮
                page_btn_frame = ttk_bs.Frame(pagination_frame)
                page_btn_frame.pack(side='right')

                # 分页按钮样式优化
                btn_style = {"width": 4, "bootstyle": "outline-secondary"}

                self.first_page_btn = ttk_bs.Button(
                    page_btn_frame, text="⏮",
                    command=lambda: self.goto_video_page(1),
                    **btn_style
                )
                self.first_page_btn.pack(side='left', padx=1)

                self.prev_page_btn = ttk_bs.Button(
                    page_btn_frame, text="◀",
                    command=self.prev_video_page,
                    **btn_style
                )
                self.prev_page_btn.pack(side='left', padx=1)

                # 页面信息标签 - 增加样式
                self.page_info_label = ttk_bs.Label(
                    page_btn_frame,
                    text="1/1",
                    font=("Arial", 10, "bold"),
                    foreground="#495057"
                )
                self.page_info_label.pack(side='left', padx=15)

                self.next_page_btn = ttk_bs.Button(
                    page_btn_frame, text="▶",
                    command=self.next_video_page,
                    **btn_style
                )
                self.next_page_btn.pack(side='left', padx=1)

                self.last_page_btn = ttk_bs.Button(
                    page_btn_frame, text="⏭",
                    command=lambda: self.goto_video_page(self.total_video_pages),
                    **btn_style
                )
                self.last_page_btn.pack(side='left', padx=1)

                # 操作提示
                tips_frame = ttk_bs.Frame(parent)
                tips_frame.pack(fill='x', pady=(10, 0))

                ttk_bs.Label(
                    tips_frame,
                    text="💡 操作提示: Ctrl+点击多选视频，Shift+点击范围选择，双击编辑视频，右键显示菜单",
                    font=("Arial", 8),
                    foreground="#6c757d"
                ).pack()

            def create_video_context_menu(self):
                """创建视频右键菜单"""
                self.video_context_menu = tk.Menu(self.root, tearoff=0)
                self.video_context_menu.add_command(label="📝 编辑", command=self.edit_video)
                self.video_context_menu.add_command(label="👁️ 查看详情", command=self.view_video_details)
                self.video_context_menu.add_command(label="📊 查看统计", command=self.view_video_stats)
                self.video_context_menu.add_separator()
                self.video_context_menu.add_command(label="📤 发布", command=self.publish_video)
                self.video_context_menu.add_command(label="📥 下架", command=self.unpublish_video)
                self.video_context_menu.add_separator()
                self.video_context_menu.add_command(label="🗑️ 删除", command=self.delete_video)

            def show_video_context_menu(self, event):
                """显示视频右键菜单"""
                # 选择点击的项目
                item = self.videos_tree.identify_row(event.y)
                if item:
                    self.videos_tree.selection_set(item)
                    self.video_context_menu.post(event.x_root, event.y_root)

            def create_simple_categories_table(self, parent):
                """创建简化的分类表格"""
                # 表格框架
                table_frame = ttk_bs.Frame(parent)
                table_frame.pack(fill='both', expand=True)

                # 表格列定义
                columns = ("ID", "分类名称", "描述", "视频数量", "状态")

                # 创建Treeview
                self.categories_tree = ttk_bs.Treeview(
                    table_frame,
                    columns=columns,
                    show='headings',
                    height=15
                )

                # 设置列标题和宽度
                column_widths = [50, 150, 250, 100, 80]
                for i, (col, width) in enumerate(zip(columns, column_widths)):
                    self.categories_tree.heading(col, text=col)
                    self.categories_tree.column(col, width=width, minwidth=50)

                # 滚动条
                v_scrollbar = tk.Scrollbar(table_frame, orient='vertical', command=self.categories_tree.yview)
                self.categories_tree.configure(yscrollcommand=v_scrollbar.set)

                # 布局
                self.categories_tree.pack(side='left', fill='both', expand=True)
                v_scrollbar.pack(side='right', fill='y')

                # 绑定双击事件
                self.categories_tree.bind('<Double-1>', self.edit_category)

            # 功能方法
            def refresh_dashboard(self):
                """刷新仪表盘数据"""
                try:
                    self.status_var.set("正在刷新仪表盘数据...")
                    self.root.update()

                    # 清除现有的统计卡片
                    for widget in self.stats_cards_frame.winfo_children():
                        widget.destroy()

                    # 调用API获取仪表盘数据
                    result = api_client.get_dashboard()

                    if result and result.get('success'):
                        # 使用API返回的真实数据
                        data = result.get('data', {})
                        stats = data.get('stats', {})

                        # 格式化数字显示
                        total_videos = stats.get('totalVideos', 0)
                        total_categories = stats.get('totalCategories', 0)
                        total_admins = stats.get('totalAdmins', 0)
                        today_views = stats.get('todayViews', 0)

                        stats_data = [
                            ("视频总数", f"{total_videos:,}", "🎬"),
                            ("分类数量", f"{total_categories:,}", "📁"),
                            ("管理员数", f"{total_admins:,}", "👥"),
                            ("今日观看", f"{today_views:,}", "👁️")
                        ]
                        self.status_var.set("✓ 仪表盘数据已刷新（来自API）")
                    else:
                        # 如果API失败，使用示例数据
                        stats_data = [
                            ("视频总数", "1,234", "🎬"),
                            ("分类数量", "56", "📁"),
                            ("管理员数", "8", "👥"),
                            ("今日观看", "2,567", "👁️")
                        ]
                        self.status_var.set("✓ 仪表盘数据已刷新（示例数据）")

                    # 创建统计卡片
                    for i, (title, value, icon) in enumerate(stats_data):
                        card = ttk_bs.LabelFrame(self.stats_cards_frame, text=f"{icon} {title}", padding=10)
                        card.pack(side='left', fill='both', expand=True, padx=5)

                        # 数值标签
                        value_label = ttk_bs.Label(
                            card,
                            text=value,
                            font=("Arial", 24, "bold")
                        )
                        value_label.pack()

                        # 如果有API数据，可以添加更多信息
                        if result and result.get('success'):
                            data = result.get('data', {})
                            stats = data.get('stats', {})

                            # 添加额外的统计信息
                            if title == "视频总数":
                                active_videos = stats.get('activeVideos', 0)
                                if active_videos > 0:
                                    ttk_bs.Label(card, text=f"已发布: {active_videos}", font=("Arial", 8)).pack()
                            elif title == "今日观看":
                                total_views = stats.get('totalViews', 0)
                                if total_views > 0:
                                    ttk_bs.Label(card, text=f"总观看: {total_views:,}", font=("Arial", 8)).pack()

                except Exception as e:
                    self.status_var.set(f"❌ 刷新失败: {str(e)}")
                    print(f"仪表盘刷新错误: {e}")
                    import traceback
                    traceback.print_exc()

            def quick_add_video(self):
                """快速添加视频"""
                messagebox.showinfo("添加视频", "添加视频功能开发中...\n\n将包含:\n• 视频文件上传\n• 基本信息填写\n• 分类选择\n• 封面设置")

            def view_logs(self):
                """查看系统日志"""
                messagebox.showinfo("系统日志", "系统日志功能开发中...\n\n将包含:\n• 操作日志\n• 错误日志\n• 访问日志\n• 系统日志")

            def refresh_videos(self):
                """刷新视频列表"""
                try:
                    self.status_var.set("正在加载视频列表...")
                    self.root.update()

                    # 清空现有数据
                    for item in self.videos_tree.get_children():
                        self.videos_tree.delete(item)

                    # 获取搜索和筛选条件
                    search_term = self.video_search_var.get() if hasattr(self, 'video_search_var') else ""
                    category = self.video_category_var.get() if hasattr(self, 'video_category_var') and self.video_category_var.get() != "全部" else ""
                    page_size = int(self.page_size_var.get()) if hasattr(self, 'page_size_var') else 20

                    # 调用API获取视频列表
                    result = api_client.get_videos(
                        page=self.current_video_page,
                        limit=page_size,
                        search=search_term,
                        category=category
                    )

                    if result and result.get('success'):
                        data = result.get('data', {})
                        videos = data.get('videos', [])
                        total = data.get('total', 0)

                        # 更新分页信息
                        self.total_video_pages = max(1, (total + page_size - 1) // page_size)
                        self.update_video_pagination_info(total)

                        # 填充表格数据
                        for i, video in enumerate(videos):
                            # 格式化数据
                            title = video.get('title', '')
                            if len(title) > 40:  # 增加标题显示长度
                                title = title[:40] + '...'

                            category_name = ""
                            if isinstance(video.get('category'), dict):
                                category_name = video.get('category', {}).get('name', '')
                            elif isinstance(video.get('category'), str):
                                category_name = video.get('category', '')

                            # 格式化文件大小
                            file_size = video.get('fileSize', 0)
                            try:
                                file_size = int(file_size) if file_size else 0
                                if file_size > 0:
                                    if file_size > 1024 * 1024 * 1024:  # GB
                                        size_str = f"{file_size / (1024 * 1024 * 1024):.1f}GB"
                                    elif file_size > 1024 * 1024:  # MB
                                        size_str = f"{file_size / (1024 * 1024):.1f}MB"
                                    else:
                                        size_str = f"{file_size / 1024:.1f}KB"
                                else:
                                    size_str = "-"
                            except (ValueError, TypeError):
                                size_str = "-"

                            # 格式化时长
                            duration = video.get('duration', 0)
                            try:
                                # 确保duration是数字类型
                                if isinstance(duration, str):
                                    # 如果是字符串，尝试转换为整数
                                    duration = int(duration) if duration.isdigit() else 0
                                else:
                                    duration = int(duration) if duration else 0

                                if duration > 0:
                                    hours = duration // 3600
                                    minutes = (duration % 3600) // 60
                                    seconds = duration % 60
                                    if hours > 0:
                                        duration_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                                    else:
                                        duration_str = f"{minutes:02d}:{seconds:02d}"
                                else:
                                    duration_str = "-"
                            except (ValueError, TypeError):
                                duration_str = "-"

                            # 格式化观看数
                            view_count = video.get('viewCount', 0) or video.get('views', 0)
                            try:
                                view_count = int(view_count) if view_count else 0
                                view_count_str = f"{view_count:,}"
                            except (ValueError, TypeError):
                                view_count_str = "0"

                            # 格式化状态显示
                            status = video.get('status', '')
                            status_display = {
                                'active': '已发布',
                                'inactive': '已下架',
                                'draft': '草稿',
                                'pending': '审核中'
                            }.get(status, status)

                            # 插入数据并设置样式
                            item_id = self.videos_tree.insert('', 'end', values=(
                                video.get('id', ''),
                                title,
                                category_name,
                                status_display,
                                view_count_str,
                                duration_str,
                                size_str,
                                video.get('createdAt', '')[:10] if video.get('createdAt') else ''
                            ))

                            # 设置行样式
                            row_tag = 'evenrow' if i % 2 == 0 else 'oddrow'
                            status_tag = None

                            # 根据状态设置颜色标签
                            if status == 'active':
                                status_tag = 'active'
                            elif status == 'inactive':
                                status_tag = 'inactive'
                            elif status == 'draft':
                                status_tag = 'draft'

                            # 应用标签
                            if status_tag:
                                self.videos_tree.item(item_id, tags=(row_tag, status_tag))
                            else:
                                self.videos_tree.item(item_id, tags=(row_tag,))

                        self.status_var.set(f"✓ 已加载 {len(videos)} 个视频，共 {total} 条记录")
                    else:
                        # 添加示例数据
                        sample_videos = [
                            ("1", "示例视频1 - 精彩动作大片", "电影", "已发布", "1,234", "02:15:30", "2.5GB", "2024-01-01"),
                            ("2", "示例视频2 - 热门电视剧第一集", "电视剧", "已发布", "5,678", "45:20", "1.2GB", "2024-01-02"),
                            ("3", "示例视频3 - 综艺节目精彩片段", "综艺", "草稿", "0", "30:45", "800MB", "2024-01-03"),
                            ("4", "示例视频4 - 动漫第一季", "动漫", "审核中", "2,345", "24:00", "600MB", "2024-01-04"),
                            ("5", "示例视频5 - 纪录片系列", "纪录片", "已发布", "3,456", "58:12", "1.8GB", "2024-01-05"),
                            ("6", "示例视频6 - 搞笑短片合集", "综艺", "已发布", "8,901", "15:30", "450MB", "2024-01-06"),
                        ]

                        for i, video in enumerate(sample_videos):
                            item_id = self.videos_tree.insert('', 'end', values=video)

                            # 设置行样式
                            row_tag = 'evenrow' if i % 2 == 0 else 'oddrow'
                            status_tag = None

                            # 根据状态设置颜色标签
                            status = video[3]  # 状态列
                            if status == "已发布":
                                status_tag = 'active'
                            elif status == "草稿":
                                status_tag = 'draft'
                            elif status == "审核中":
                                status_tag = 'inactive'

                            # 应用标签
                            if status_tag:
                                self.videos_tree.item(item_id, tags=(row_tag, status_tag))
                            else:
                                self.videos_tree.item(item_id, tags=(row_tag,))

                        # 更新分页信息（示例数据）
                        self.total_video_pages = 1
                        self.update_video_pagination_info(len(sample_videos))
                        self.status_var.set("✓ 已加载示例视频数据")

                    # 更新分页按钮状态
                    self.update_video_pagination_buttons()

                except Exception as e:
                    self.status_var.set(f"❌ 视频列表加载异常: {str(e)}")
                    print(f"视频列表加载错误: {e}")
                    import traceback
                    traceback.print_exc()

            def refresh_categories(self):
                """刷新分类列表"""
                try:
                    self.status_var.set("正在加载分类列表...")

                    # 清空现有数据
                    for item in self.categories_tree.get_children():
                        self.categories_tree.delete(item)

                    # 调用API获取分类列表
                    result = api_client.get_categories(page=1, limit=100)

                    if result and result.get('success'):
                        data = result.get('data', {})
                        categories = data.get('categories', [])

                        # 填充表格数据
                        for category in categories:
                            self.categories_tree.insert('', 'end', values=(
                                category.get('id', ''),
                                category.get('name', ''),
                                category.get('description', '')[:40] + ('...' if len(category.get('description', '')) > 40 else ''),
                                category.get('videoCount', 0),
                                category.get('status', '')
                            ))

                        self.status_var.set(f"✓ 已加载 {len(categories)} 个分类")
                    else:
                        # 添加示例数据
                        sample_categories = [
                            ("1", "电影", "各类电影内容", "156", "启用"),
                            ("2", "电视剧", "电视剧集内容", "89", "启用"),
                            ("3", "综艺", "综艺节目内容", "45", "启用"),
                            ("4", "动漫", "动画片内容", "78", "启用"),
                        ]

                        for category in sample_categories:
                            self.categories_tree.insert('', 'end', values=category)

                        self.status_var.set("✓ 已加载示例分类数据")

                except Exception as e:
                    self.status_var.set(f"❌ 分类列表加载异常: {str(e)}")

            def update_video_pagination_info(self, total_records):
                """更新分页信息"""
                page_size = int(self.page_size_var.get()) if hasattr(self, 'page_size_var') else 20
                start_record = (self.current_video_page - 1) * page_size + 1
                end_record = min(self.current_video_page * page_size, total_records)

                # 更新顶部的分页信息
                if hasattr(self, 'video_page_info_var'):
                    self.video_page_info_var.set(f"第 {self.current_video_page} 页，共 {total_records:,} 条记录")

                # 更新底部的页面信息
                if hasattr(self, 'page_info_label'):
                    self.page_info_label.config(text=f"{self.current_video_page}/{self.total_video_pages}")

                # 更新记录范围信息
                if hasattr(self, 'record_info_label'):
                    if total_records > 0:
                        self.record_info_label.config(text=f"显示 {start_record:,}-{end_record:,} 条，共 {total_records:,} 条")
                    else:
                        self.record_info_label.config(text="暂无数据")

            def update_video_pagination_buttons(self):
                """更新分页按钮状态"""
                if hasattr(self, 'first_page_btn'):
                    # 第一页和上一页按钮
                    if self.current_video_page <= 1:
                        self.first_page_btn.config(state='disabled')
                        self.prev_page_btn.config(state='disabled')
                    else:
                        self.first_page_btn.config(state='normal')
                        self.prev_page_btn.config(state='normal')

                    # 下一页和最后一页按钮
                    if self.current_video_page >= self.total_video_pages:
                        self.next_page_btn.config(state='disabled')
                        self.last_page_btn.config(state='disabled')
                    else:
                        self.next_page_btn.config(state='normal')
                        self.last_page_btn.config(state='normal')

            def prev_video_page(self):
                """上一页"""
                if self.current_video_page > 1:
                    self.current_video_page -= 1
                    self.refresh_videos()

            def next_video_page(self):
                """下一页"""
                if self.current_video_page < self.total_video_pages:
                    self.current_video_page += 1
                    self.refresh_videos()

            def goto_video_page(self, page):
                """跳转到指定页"""
                if 1 <= page <= self.total_video_pages:
                    self.current_video_page = page
                    self.refresh_videos()

            def change_page_size(self):
                """改变每页显示数量"""
                self.current_video_page = 1  # 重置到第一页
                self.video_page_size = int(self.page_size_var.get())
                self.refresh_videos()

            def search_videos(self):
                """搜索视频"""
                self.current_video_page = 1  # 重置到第一页
                self.refresh_videos()

            def reset_video_search(self):
                """重置搜索"""
                self.video_search_var.set("")
                self.video_category_var.set("全部")
                self.video_status_var.set("全部")
                self.current_video_page = 1
                self.refresh_videos()

            def filter_videos(self):
                """筛选视频"""
                self.current_video_page = 1  # 重置到第一页
                self.refresh_videos()

            def show_video_stats(self):
                """显示视频统计"""
                try:
                    result = api_client.get_video_stats()
                    if result and result.get('success'):
                        data = result.get('data', {})
                        stats_text = f"""视频统计信息：

总视频数: {data.get('totalVideos', 0)}
已发布: {data.get('publishedVideos', 0)}
草稿: {data.get('draftVideos', 0)}
审核中: {data.get('pendingVideos', 0)}

总观看数: {data.get('totalViews', 0):,}
今日观看: {data.get('todayViews', 0):,}
本月观看: {data.get('monthViews', 0):,}

总存储空间: {data.get('totalStorage', 'N/A')}
平均文件大小: {data.get('avgFileSize', 'N/A')}"""
                    else:
                        stats_text = """视频统计信息：

总视频数: 1,234
已发布: 1,100
草稿: 89
审核中: 45

总观看数: 2,567,890
今日观看: 12,345
本月观看: 456,789

总存储空间: 2.5TB
平均文件大小: 1.2GB

注：这是示例数据"""

                    messagebox.showinfo("视频统计", stats_text)
                except Exception as e:
                    messagebox.showerror("错误", f"获取统计信息失败: {str(e)}")

            def add_video(self):
                """添加视频"""
                self.open_video_form()

            def edit_video(self, event=None):
                """编辑视频"""
                selection = self.videos_tree.selection()
                if selection:
                    item = self.videos_tree.item(selection[0])
                    video_id = item['values'][0]
                    if video_id:
                        self.open_video_form(video_id)
                    else:
                        messagebox.showwarning("提示", "请选择要编辑的视频")

            def view_video_details(self):
                """查看视频详情"""
                selection = self.videos_tree.selection()
                if selection:
                    item = self.videos_tree.item(selection[0])
                    video_id = item['values'][0]
                    if video_id:
                        self.open_video_details(video_id)
                    else:
                        messagebox.showwarning("提示", "请选择要查看的视频")

            def open_video_details(self, video_id):
                """打开视频详情窗口"""
                try:
                    # 获取视频详情
                    result = api_client.get_video_by_id(int(video_id))

                    if result and result.get('success'):
                        video_data = result.get('data', {})
                        self.show_video_details_window(video_data)
                    else:
                        messagebox.showerror("错误", f"获取视频详情失败: {result.get('message', '未知错误')}")

                except Exception as e:
                    messagebox.showerror("错误", f"获取视频详情异常: {str(e)}")

            def show_video_details_window(self, video_data):
                """显示视频详情窗口"""
                # 创建详情窗口
                details_window = tk.Toplevel(self.root)
                details_window.title(f"视频详情 - {video_data.get('title', '未知')}")
                details_window.geometry("600x500")
                details_window.resizable(True, True)

                # 设置窗口图标和属性
                details_window.transient(self.root)
                details_window.grab_set()

                # 窗口居中显示
                details_window.update_idletasks()
                width = 600
                height = 500
                x = (details_window.winfo_screenwidth() // 2) - (width // 2)
                y = (details_window.winfo_screenheight() // 2) - (height // 2)
                details_window.geometry(f"{width}x{height}+{x}+{y}")

                # 创建滚动框架
                canvas = tk.Canvas(details_window)
                scrollbar = tk.Scrollbar(details_window, orient="vertical", command=canvas.yview)
                scrollable_frame = ttk_bs.Frame(canvas)

                scrollable_frame.bind(
                    "<Configure>",
                    lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
                )

                canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
                canvas.configure(yscrollcommand=scrollbar.set)

                # 绑定鼠标滚轮事件
                def _on_mousewheel(event):
                    canvas.yview_scroll(int(-1*(event.delta/120)), "units")

                def _bind_to_mousewheel(event):
                    canvas.bind_all("<MouseWheel>", _on_mousewheel)

                def _unbind_from_mousewheel(event):
                    canvas.unbind_all("<MouseWheel>")

                canvas.bind('<Enter>', _bind_to_mousewheel)
                canvas.bind('<Leave>', _unbind_from_mousewheel)

                # 详情内容
                self.create_video_details_content(scrollable_frame, video_data)

                # 布局
                canvas.pack(side="left", fill="both", expand=True, padx=10, pady=10)
                scrollbar.pack(side="right", fill="y")

                # 关闭按钮
                btn_frame = ttk_bs.Frame(details_window)
                btn_frame.pack(fill='x', padx=10, pady=10)

                ttk_bs.Button(
                    btn_frame,
                    text="关闭",
                    command=details_window.destroy,
                    bootstyle="secondary"
                ).pack(side='right')

                ttk_bs.Button(
                    btn_frame,
                    text="编辑",
                    command=lambda: [details_window.destroy(), self.open_video_form(video_data.get('id'))],
                    bootstyle="primary"
                ).pack(side='right', padx=(0, 10))

            def create_video_details_content(self, parent, video_data):
                """创建视频详情内容"""
                # 标题
                title_frame = ttk_bs.Frame(parent)
                title_frame.pack(fill='x', pady=(0, 20))

                ttk_bs.Label(
                    title_frame,
                    text="📹 视频详细信息",
                    font=("Arial", 16, "bold")
                ).pack()

                # 基本信息
                basic_frame = ttk_bs.LabelFrame(parent, text="基本信息", padding=15)
                basic_frame.pack(fill='x', pady=(0, 15))

                basic_info = [
                    ("ID", video_data.get('id', 'N/A')),
                    ("标题", video_data.get('title', 'N/A')),
                    ("描述", video_data.get('description', 'N/A')),
                    ("分类", video_data.get('category', {}).get('name', 'N/A') if isinstance(video_data.get('category'), dict) else video_data.get('category', 'N/A')),
                    ("状态", {'active': '已发布', 'inactive': '已下架', 'draft': '草稿', 'pending': '审核中'}.get(video_data.get('status'), video_data.get('status', 'N/A'))),
                    ("创建时间", video_data.get('createdAt', 'N/A')),
                    ("更新时间", video_data.get('updatedAt', 'N/A'))
                ]

                for label, value in basic_info:
                    row = ttk_bs.Frame(basic_frame)
                    row.pack(fill='x', pady=2)

                    ttk_bs.Label(row, text=f"{label}:", font=("Arial", 9, "bold"), width=12).pack(side='left')
                    ttk_bs.Label(row, text=str(value), font=("Arial", 9)).pack(side='left', padx=(10, 0))

                # 媒体信息
                media_frame = ttk_bs.LabelFrame(parent, text="媒体信息", padding=15)
                media_frame.pack(fill='x', pady=(0, 15))

                # 格式化时长
                duration = video_data.get('duration', 0)
                try:
                    duration = int(duration) if duration else 0
                    if duration > 0:
                        hours = duration // 3600
                        minutes = (duration % 3600) // 60
                        seconds = duration % 60
                        duration_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}" if hours > 0 else f"{minutes:02d}:{seconds:02d}"
                    else:
                        duration_str = "未知"
                except:
                    duration_str = "未知"

                # 格式化文件大小
                file_size = video_data.get('fileSize', 0)
                try:
                    file_size = int(file_size) if file_size else 0
                    if file_size > 0:
                        if file_size > 1024 * 1024 * 1024:
                            size_str = f"{file_size / (1024 * 1024 * 1024):.2f} GB"
                        elif file_size > 1024 * 1024:
                            size_str = f"{file_size / (1024 * 1024):.2f} MB"
                        else:
                            size_str = f"{file_size / 1024:.2f} KB"
                    else:
                        size_str = "未知"
                except:
                    size_str = "未知"

                media_info = [
                    ("时长", duration_str),
                    ("文件大小", size_str),
                    ("视频URL", video_data.get('videoUrl', 'N/A')),
                    ("封面图", video_data.get('thumbnail', 'N/A')),
                    ("观看次数", f"{video_data.get('viewCount', 0):,}" if video_data.get('viewCount') else "0"),
                    ("评分", f"{video_data.get('rating', 0):.1f}" if video_data.get('rating') else "暂无评分")
                ]

                for label, value in media_info:
                    row = ttk_bs.Frame(media_frame)
                    row.pack(fill='x', pady=2)

                    ttk_bs.Label(row, text=f"{label}:", font=("Arial", 9, "bold"), width=12).pack(side='left')
                    ttk_bs.Label(row, text=str(value), font=("Arial", 9)).pack(side='left', padx=(10, 0))

            def open_video_form(self, video_id=None):
                """打开视频编辑表单"""
                # 创建表单窗口
                form_window = tk.Toplevel(self.root)
                form_window.title("添加视频" if video_id is None else "编辑视频")
                form_window.geometry("700x600")
                form_window.resizable(True, True)

                # 设置窗口属性
                form_window.transient(self.root)
                form_window.grab_set()

                # 窗口居中显示
                form_window.update_idletasks()
                width = 700
                height = 600
                x = (form_window.winfo_screenwidth() // 2) - (width // 2)
                y = (form_window.winfo_screenheight() // 2) - (height // 2)
                form_window.geometry(f"{width}x{height}+{x}+{y}")

                # 创建表单内容
                self.create_video_form_content(form_window, video_id)

            def create_video_form_content(self, parent, video_id=None):
                """创建视频表单内容"""
                # 主框架
                main_frame = ttk_bs.Frame(parent)
                main_frame.pack(fill='both', expand=True, padx=20, pady=20)

                # 标题
                title_text = "添加新视频" if video_id is None else "编辑视频"
                ttk_bs.Label(
                    main_frame,
                    text=f"📹 {title_text}",
                    font=("Arial", 16, "bold")
                ).pack(pady=(0, 20))

                # 创建滚动区域
                canvas = tk.Canvas(main_frame)
                scrollbar = tk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
                scrollable_frame = ttk_bs.Frame(canvas)

                scrollable_frame.bind(
                    "<Configure>",
                    lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
                )

                canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
                canvas.configure(yscrollcommand=scrollbar.set)

                # 绑定鼠标滚轮事件
                def _on_mousewheel(event):
                    canvas.yview_scroll(int(-1*(event.delta/120)), "units")

                def _bind_to_mousewheel(event):
                    canvas.bind_all("<MouseWheel>", _on_mousewheel)

                def _unbind_from_mousewheel(event):
                    canvas.unbind_all("<MouseWheel>")

                canvas.bind('<Enter>', _bind_to_mousewheel)
                canvas.bind('<Leave>', _unbind_from_mousewheel)

                # 表单字段
                self.create_video_form_fields(scrollable_frame, video_id)

                # 布局滚动区域
                canvas.pack(side="left", fill="both", expand=True)
                scrollbar.pack(side="right", fill="y")

                # 按钮区域
                btn_frame = ttk_bs.Frame(parent)
                btn_frame.pack(fill='x', padx=20, pady=(0, 20))

                ttk_bs.Button(
                    btn_frame,
                    text="取消",
                    command=parent.destroy,
                    bootstyle="secondary"
                ).pack(side='right', padx=(10, 0))

                ttk_bs.Button(
                    btn_frame,
                    text="保存" if video_id else "添加",
                    command=lambda: self.save_video_form(parent, video_id),
                    bootstyle="primary"
                ).pack(side='right')

                # 如果是编辑模式，加载数据
                if video_id:
                    self.load_video_data(video_id)

            def create_video_form_fields(self, parent, video_id=None):
                """创建表单字段"""
                # 基本信息
                basic_frame = ttk_bs.LabelFrame(parent, text="基本信息", padding=15)
                basic_frame.pack(fill='x', pady=(0, 15))

                # 标题
                ttk_bs.Label(basic_frame, text="标题 *").pack(anchor='w')
                self.form_title_var = tk.StringVar()
                ttk_bs.Entry(basic_frame, textvariable=self.form_title_var, width=60).pack(fill='x', pady=(5, 15))

                # 描述
                ttk_bs.Label(basic_frame, text="描述").pack(anchor='w')
                self.form_desc_text = tk.Text(basic_frame, height=4, width=60)
                self.form_desc_text.pack(fill='x', pady=(5, 15))

                # 分类
                ttk_bs.Label(basic_frame, text="分类 *").pack(anchor='w')
                self.form_category_var = tk.StringVar()
                self.category_combo = ttk_bs.Combobox(
                    basic_frame,
                    textvariable=self.form_category_var,
                    state="readonly",
                    width=30
                )
                # 加载分类数据
                self.load_categories_for_form()
                self.category_combo.pack(anchor='w', pady=(5, 15))

                # 状态
                ttk_bs.Label(basic_frame, text="状态").pack(anchor='w')
                self.form_status_var = tk.StringVar(value="draft")
                status_frame = ttk_bs.Frame(basic_frame)
                status_frame.pack(anchor='w', pady=(5, 15))

                ttk_bs.Radiobutton(status_frame, text="草稿", variable=self.form_status_var, value="draft").pack(side='left', padx=(0, 15))
                ttk_bs.Radiobutton(status_frame, text="已发布", variable=self.form_status_var, value="active").pack(side='left', padx=(0, 15))
                ttk_bs.Radiobutton(status_frame, text="已下架", variable=self.form_status_var, value="inactive").pack(side='left')

                # 媒体信息
                media_frame = ttk_bs.LabelFrame(parent, text="媒体信息", padding=15)
                media_frame.pack(fill='x', pady=(0, 15))

                # 视频URL
                ttk_bs.Label(media_frame, text="视频URL *").pack(anchor='w')
                self.form_video_url_var = tk.StringVar()
                ttk_bs.Entry(media_frame, textvariable=self.form_video_url_var, width=60).pack(fill='x', pady=(5, 15))

                # 封面图URL
                ttk_bs.Label(media_frame, text="封面图URL").pack(anchor='w')
                self.form_thumbnail_var = tk.StringVar()
                ttk_bs.Entry(media_frame, textvariable=self.form_thumbnail_var, width=60).pack(fill='x', pady=(5, 15))

                # 时长（秒）
                duration_frame = ttk_bs.Frame(media_frame)
                duration_frame.pack(fill='x', pady=(0, 15))

                ttk_bs.Label(duration_frame, text="时长").pack(side='left')
                self.form_duration_var = tk.StringVar()
                ttk_bs.Entry(duration_frame, textvariable=self.form_duration_var, width=10).pack(side='left', padx=(10, 5))
                ttk_bs.Label(duration_frame, text="秒 (或格式: HH:MM:SS)").pack(side='left')

                # 其他信息
                other_frame = ttk_bs.LabelFrame(parent, text="其他信息", padding=15)
                other_frame.pack(fill='x', pady=(0, 15))

                # 标签
                ttk_bs.Label(other_frame, text="标签 (用逗号分隔)").pack(anchor='w')
                self.form_tags_var = tk.StringVar()
                ttk_bs.Entry(other_frame, textvariable=self.form_tags_var, width=60).pack(fill='x', pady=(5, 15))

                # 是否推荐
                self.form_featured_var = tk.BooleanVar()
                ttk_bs.Checkbutton(
                    other_frame,
                    text="推荐视频",
                    variable=self.form_featured_var
                ).pack(anchor='w')

            def load_video_data(self, video_id):
                """加载视频数据到表单"""
                try:
                    # 确保分类数据已加载
                    self.load_categories_for_form()

                    result = api_client.get_video_by_id(int(video_id))

                    if result and result.get('success'):
                        video_data = result.get('data', {})

                        # 调试：打印视频数据结构
                        print(f"加载视频数据: {video_data}")

                        # 填充表单数据
                        self.form_title_var.set(video_data.get('title', ''))

                        # 描述
                        self.form_desc_text.delete('1.0', tk.END)
                        self.form_desc_text.insert('1.0', video_data.get('description', ''))

                        # 分类 - 确保分类值在下拉框选项中
                        category = video_data.get('category', {})
                        category_value = ''
                        if isinstance(category, dict):
                            category_value = category.get('name', '')
                        else:
                            category_value = str(category)

                        print(f"分类数据: {category}, 分类值: {category_value}")

                        # 确保当前分类在下拉框选项中
                        if category_value and hasattr(self, 'category_combo'):
                            current_values = list(self.category_combo['values'])
                            print(f"当前下拉框选项: {current_values}")
                            if category_value not in current_values:
                                current_values.append(category_value)
                                self.category_combo['values'] = current_values
                                print(f"添加分类后的选项: {current_values}")

                        self.form_category_var.set(category_value)
                        print(f"设置分类变量为: {category_value}")

                        # 状态
                        self.form_status_var.set(video_data.get('status', 'draft'))

                        # 媒体信息
                        video_url = video_data.get('videoUrl', '')
                        thumbnail_url = video_data.get('thumbnail', '')
                        duration = video_data.get('duration', '')

                        print(f"视频URL: {video_url}")
                        print(f"封面图URL: {thumbnail_url}")
                        print(f"时长: {duration}")

                        self.form_video_url_var.set(video_url)
                        self.form_thumbnail_var.set(thumbnail_url)
                        self.form_duration_var.set(str(duration))

                        # 标签
                        tags = video_data.get('tags', [])
                        if isinstance(tags, list):
                            self.form_tags_var.set(', '.join(tags))
                        else:
                            self.form_tags_var.set(str(tags))

                        # 推荐
                        self.form_featured_var.set(bool(video_data.get('featured', False)))

                    else:
                        messagebox.showerror("错误", f"加载视频数据失败: {result.get('message', '未知错误')}")

                except Exception as e:
                    messagebox.showerror("错误", f"加载视频数据异常: {str(e)}")

            def load_categories_for_form(self):
                """为表单加载分类数据"""
                try:
                    # 如果缓存为空，从API获取
                    if not self.categories_cache:
                        result = api_client.get_categories(page=1, limit=100)  # 获取所有分类

                        if result and result.get('success'):
                            categories_data = result.get('data', {})
                            categories = categories_data.get('categories', [])

                            # 缓存分类数据 - 保存完整的分类信息
                            self.categories_cache = []
                            self.categories_map = {}  # 名称到ID的映射
                            for category in categories:
                                if isinstance(category, dict):
                                    name = category.get('name', '')
                                    category_id = category.get('id')
                                    if name:
                                        self.categories_cache.append(name)
                                        self.categories_map[name] = category_id
                                else:
                                    self.categories_cache.append(str(category))

                            # 如果没有分类，使用默认分类
                            if not self.categories_cache:
                                self.categories_cache = ["电影", "电视剧", "综艺", "动漫", "纪录片", "其他"]
                        else:
                            # API失败时使用默认分类
                            self.categories_cache = ["电影", "电视剧", "综艺", "动漫", "纪录片", "其他"]

                    # 设置下拉框选项
                    if hasattr(self, 'category_combo'):
                        self.category_combo['values'] = self.categories_cache

                except Exception as e:
                    print(f"加载分类数据异常: {e}")
                    # 异常时使用默认分类
                    self.categories_cache = ["电影", "电视剧", "综艺", "动漫", "纪录片", "其他"]
                    if hasattr(self, 'category_combo'):
                        self.category_combo['values'] = self.categories_cache

            def get_category_id_by_name(self, category_name):
                """根据分类名称获取分类ID"""
                try:
                    if not category_name:
                        return None

                    # 首先尝试从缓存的映射中获取
                    if hasattr(self, 'categories_map') and category_name in self.categories_map:
                        return self.categories_map[category_name]

                    # 如果缓存中没有，从API获取分类列表
                    result = api_client.get_categories(page=1, limit=100)

                    if result and result.get('success'):
                        categories_data = result.get('data', {})
                        categories = categories_data.get('categories', [])

                        # 查找匹配的分类
                        for category in categories:
                            if isinstance(category, dict):
                                if category.get('name') == category_name:
                                    return category.get('id')

                    # 如果没找到，返回None或默认值
                    print(f"未找到分类 '{category_name}' 的ID")
                    return None

                except Exception as e:
                    print(f"获取分类ID异常: {e}")
                    return None

            def view_video_stats(self):
                """查看单个视频统计"""
                selection = self.videos_tree.selection()
                if selection:
                    item = self.videos_tree.item(selection[0])
                    video_id = item['values'][0]
                    video_title = item['values'][1]

                    try:
                        # 这里可以调用视频统计API
                        stats_text = f"""视频统计信息：

视频ID: {video_id}
标题: {video_title}

观看数据：
• 总观看次数: 1,234
• 今日观看: 56
• 本周观看: 345
• 本月观看: 789

用户互动：
• 点赞数: 123
• 收藏数: 45
• 分享数: 67
• 评论数: 89

注：这是示例数据，实际数据需要API支持"""

                        messagebox.showinfo("视频统计", stats_text)
                    except Exception as e:
                        messagebox.showerror("错误", f"获取视频统计失败: {str(e)}")

            def save_video_form(self, form_window, video_id=None):
                """保存视频表单"""
                try:
                    # 验证必填字段
                    if not self.form_title_var.get().strip():
                        messagebox.showerror("错误", "请输入视频标题")
                        return

                    if not self.form_category_var.get():
                        messagebox.showerror("错误", "请选择视频分类")
                        return

                    if not self.form_video_url_var.get().strip():
                        messagebox.showerror("错误", "请输入视频URL")
                        return

                    # 处理时长
                    duration_str = self.form_duration_var.get().strip()
                    duration = 0
                    if duration_str:
                        try:
                            if ':' in duration_str:
                                # 格式: HH:MM:SS 或 MM:SS
                                parts = duration_str.split(':')
                                if len(parts) == 3:  # HH:MM:SS
                                    duration = int(parts[0]) * 3600 + int(parts[1]) * 60 + int(parts[2])
                                elif len(parts) == 2:  # MM:SS
                                    duration = int(parts[0]) * 60 + int(parts[1])
                            else:
                                # 直接是秒数
                                duration = int(duration_str)
                        except ValueError:
                            messagebox.showerror("错误", "时长格式不正确，请输入秒数或 HH:MM:SS 格式")
                            return

                    # 处理标签
                    tags_str = self.form_tags_var.get().strip()
                    tags = [tag.strip() for tag in tags_str.split(',') if tag.strip()] if tags_str else []

                    # 构建视频数据 - 使用后端期望的字段名
                    video_data = {
                        'title': self.form_title_var.get().strip(),
                        'description': self.form_desc_text.get('1.0', tk.END).strip(),
                        'category_id': self.get_category_id_by_name(self.form_category_var.get()),
                        'status': self.form_status_var.get(),
                        'video_url': self.form_video_url_var.get().strip(),
                        'cover_url': self.form_thumbnail_var.get().strip(),
                        'duration': duration,
                        'tags': tags,
                        'featured': self.form_featured_var.get()
                    }

                    print(f"发送的视频数据: {video_data}")

                    # 调用API
                    if video_id:
                        # 更新视频
                        print(f"更新视频 ID: {video_id}")
                        result = api_client.update_video(int(video_id), video_data)
                        action = "更新"
                    else:
                        # 创建视频
                        print(f"创建新视频")
                        result = api_client.create_video(video_data)
                        action = "添加"

                    print(f"API响应: {result}")

                    if result and result.get('success'):
                        messagebox.showinfo("成功", f"视频{action}成功！")
                        form_window.destroy()
                        self.refresh_videos()  # 刷新视频列表
                    else:
                        error_msg = result.get('message', '未知错误') if result else '无响应'
                        print(f"API错误: {error_msg}")
                        if result and result.get('errors'):
                            print(f"详细错误: {result.get('errors')}")
                        messagebox.showerror("错误", f"视频{action}失败: {error_msg}")

                except Exception as e:
                    print(f"保存视频异常: {str(e)}")
                    messagebox.showerror("错误", f"保存视频异常: {str(e)}")

            def publish_video(self):
                """发布视频"""
                selection = self.videos_tree.selection()
                if selection:
                    item = self.videos_tree.item(selection[0])
                    video_id = item['values'][0]
                    video_title = item['values'][1]

                    if messagebox.askyesno("发布视频", f"确定要发布这个视频吗？\n\n{video_title}"):
                        try:
                            result = api_client.update_video(int(video_id), {'status': 'active'})

                            if result and result.get('success'):
                                messagebox.showinfo("成功", "视频发布成功！")
                                self.refresh_videos()
                            else:
                                messagebox.showerror("错误", f"视频发布失败: {result.get('message', '未知错误')}")

                        except Exception as e:
                            messagebox.showerror("错误", f"发布视频异常: {str(e)}")

            def unpublish_video(self):
                """下架视频"""
                selection = self.videos_tree.selection()
                if selection:
                    item = self.videos_tree.item(selection[0])
                    video_id = item['values'][0]
                    video_title = item['values'][1]

                    if messagebox.askyesno("下架视频", f"确定要下架这个视频吗？\n\n{video_title}"):
                        try:
                            result = api_client.update_video(int(video_id), {'status': 'inactive'})

                            if result and result.get('success'):
                                messagebox.showinfo("成功", "视频下架成功！")
                                self.refresh_videos()
                            else:
                                messagebox.showerror("错误", f"视频下架失败: {result.get('message', '未知错误')}")

                        except Exception as e:
                            messagebox.showerror("错误", f"下架视频异常: {str(e)}")

            def delete_video(self):
                """删除视频"""
                selection = self.videos_tree.selection()
                if selection:
                    item = self.videos_tree.item(selection[0])
                    video_id = item['values'][0]
                    video_title = item['values'][1]

                    if messagebox.askyesno("删除视频", f"确定要删除这个视频吗？\n\n{video_title}\n\n⚠️ 注意：此操作不可恢复！"):
                        try:
                            result = api_client.delete_video(int(video_id))

                            if result and result.get('success'):
                                messagebox.showinfo("成功", "视频删除成功！")
                                self.refresh_videos()
                            else:
                                messagebox.showerror("错误", f"视频删除失败: {result.get('message', '未知错误')}")

                        except Exception as e:
                            messagebox.showerror("错误", f"删除视频异常: {str(e)}")

            def show_batch_operations(self):
                """显示批量操作菜单"""
                # 检查是否有选中的项目
                selected_items = self.videos_tree.selection()
                if not selected_items:
                    messagebox.showwarning("提示", "请先选择要操作的视频")
                    return

                # 创建批量操作窗口
                batch_window = tk.Toplevel(self.root)
                batch_window.title("批量操作")
                batch_window.geometry("400x300")
                batch_window.resizable(False, False)

                # 设置窗口属性
                batch_window.transient(self.root)
                batch_window.grab_set()

                # 窗口居中显示
                batch_window.update_idletasks()
                width = 400
                height = 300
                x = (batch_window.winfo_screenwidth() // 2) - (width // 2)
                y = (batch_window.winfo_screenheight() // 2) - (height // 2)
                batch_window.geometry(f"{width}x{height}+{x}+{y}")

                # 主框架
                main_frame = ttk_bs.Frame(batch_window)
                main_frame.pack(fill='both', expand=True, padx=20, pady=20)

                # 标题
                ttk_bs.Label(
                    main_frame,
                    text=f"📤 批量操作 ({len(selected_items)} 个视频)",
                    font=("Arial", 14, "bold")
                ).pack(pady=(0, 20))

                # 操作选项
                operations_frame = ttk_bs.LabelFrame(main_frame, text="选择操作", padding=15)
                operations_frame.pack(fill='x', pady=(0, 20))

                self.batch_operation_var = tk.StringVar(value="publish")

                ttk_bs.Radiobutton(
                    operations_frame,
                    text="📤 批量发布",
                    variable=self.batch_operation_var,
                    value="publish"
                ).pack(anchor='w', pady=5)

                ttk_bs.Radiobutton(
                    operations_frame,
                    text="📥 批量下架",
                    variable=self.batch_operation_var,
                    value="unpublish"
                ).pack(anchor='w', pady=5)

                ttk_bs.Radiobutton(
                    operations_frame,
                    text="📝 批量设为草稿",
                    variable=self.batch_operation_var,
                    value="draft"
                ).pack(anchor='w', pady=5)

                ttk_bs.Radiobutton(
                    operations_frame,
                    text="⭐ 批量设为推荐",
                    variable=self.batch_operation_var,
                    value="feature"
                ).pack(anchor='w', pady=5)

                ttk_bs.Radiobutton(
                    operations_frame,
                    text="🗑️ 批量删除",
                    variable=self.batch_operation_var,
                    value="delete"
                ).pack(anchor='w', pady=5)

                # 按钮区域
                btn_frame = ttk_bs.Frame(main_frame)
                btn_frame.pack(fill='x')

                ttk_bs.Button(
                    btn_frame,
                    text="取消",
                    command=batch_window.destroy,
                    bootstyle="secondary"
                ).pack(side='right', padx=(10, 0))

                ttk_bs.Button(
                    btn_frame,
                    text="执行",
                    command=lambda: self.execute_batch_operation(batch_window, selected_items),
                    bootstyle="primary"
                ).pack(side='right')

            def execute_batch_operation(self, batch_window, selected_items):
                """执行批量操作"""
                operation = self.batch_operation_var.get()

                # 获取选中的视频信息
                video_info = []
                for item in selected_items:
                    values = self.videos_tree.item(item)['values']
                    video_info.append({
                        'id': values[0],
                        'title': values[1]
                    })

                # 确认操作
                operation_names = {
                    'publish': '发布',
                    'unpublish': '下架',
                    'draft': '设为草稿',
                    'feature': '设为推荐',
                    'delete': '删除'
                }

                operation_name = operation_names.get(operation, '操作')

                if operation == 'delete':
                    confirm_msg = f"确定要删除这 {len(video_info)} 个视频吗？\n\n⚠️ 注意：此操作不可恢复！"
                else:
                    confirm_msg = f"确定要{operation_name}这 {len(video_info)} 个视频吗？"

                if not messagebox.askyesno("确认操作", confirm_msg):
                    return

                # 执行操作
                batch_window.destroy()

                try:
                    success_count = 0
                    error_count = 0

                    for video in video_info:
                        try:
                            video_id = int(video['id'])

                            if operation == 'delete':
                                result = api_client.delete_video(video_id)
                            else:
                                # 更新操作
                                update_data = {}
                                if operation == 'publish':
                                    update_data['status'] = 'active'
                                elif operation == 'unpublish':
                                    update_data['status'] = 'inactive'
                                elif operation == 'draft':
                                    update_data['status'] = 'draft'
                                elif operation == 'feature':
                                    update_data['featured'] = True

                                result = api_client.update_video(video_id, update_data)

                            if result and result.get('success'):
                                success_count += 1
                            else:
                                error_count += 1

                        except Exception as e:
                            error_count += 1
                            print(f"批量操作错误 - 视频ID {video['id']}: {e}")

                    # 显示结果
                    if error_count == 0:
                        messagebox.showinfo("操作完成", f"批量{operation_name}成功！\n\n成功: {success_count} 个")
                    else:
                        messagebox.showwarning("操作完成", f"批量{operation_name}完成\n\n成功: {success_count} 个\n失败: {error_count} 个")

                    # 刷新列表
                    self.refresh_videos()

                except Exception as e:
                    messagebox.showerror("错误", f"批量操作异常: {str(e)}")

            def add_category(self):
                """添加分类"""
                messagebox.showinfo("添加分类", "添加分类功能开发中...")

            def edit_category(self, event=None):
                """编辑分类"""
                selection = self.categories_tree.selection()
                if selection:
                    item = self.categories_tree.item(selection[0])
                    category_id = item['values'][0]
                    messagebox.showinfo("编辑分类", f"编辑分类功能开发中...\n\n分类ID: {category_id}")

            def add_admin(self):
                """添加管理员"""
                messagebox.showinfo("添加管理员", "添加管理员功能开发中...")

            def logout(self):
                """退出登录"""
                if messagebox.askokcancel("退出登录", "确定要退出登录吗？"):
                    self.current_admin = None
                    self.is_logged_in = False
                    self.create_login_interface()

            def on_closing(self):
                """窗口关闭事件"""
                if messagebox.askokcancel("退出", "确定要退出应用程序吗？"):
                    self.root.destroy()

            def run(self):
                """运行应用"""
                self.root.mainloop()

        print("✓ 改进应用类创建成功")

        print("\n3. 启动改进应用...")
        app = ImprovedVideoAdminApp()
        app.run()

    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()

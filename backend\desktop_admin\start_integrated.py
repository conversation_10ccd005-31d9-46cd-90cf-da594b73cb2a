#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
91JSPG.COM 桌面管理应用 - 集成版启动脚本
解决窗口路径冲突问题
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
from datetime import datetime

def main():
    """主函数"""
    print("=" * 50)
    print("91JSPG.COM 桌面管理应用 - 集成版启动")
    print("=" * 50)
    
    # 设置环境变量，完全禁用DPI相关功能
    os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '0'
    os.environ['QT_SCALE_FACTOR'] = '1'
    os.environ['QT_DEVICE_PIXEL_RATIO'] = '1'
    os.environ['QT_SCREEN_SCALE_FACTORS'] = '1'
    
    try:
        # 添加当前目录到路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)
        
        print("1. 检查基本模块...")
        
        # 检查tkinter
        import tkinter as tk
        print("✓ tkinter")
        
        # 检查ttkbootstrap
        import ttkbootstrap as ttk_bs
        print("✓ ttkbootstrap")
        
        # 检查其他依赖
        import requests
        print("✓ requests")
        
        print("\n2. 检查应用模块...")
        
        # 检查配置
        from config import config
        print("✓ config")
        
        # 检查API客户端
        from api_client import api_client
        print("✓ api_client")
        
        print("\n3. 创建集成应用...")
        
        # 创建集成的应用类
        class IntegratedVideoAdminApp:
            def __init__(self):
                # 创建主窗口
                self.root = ttk_bs.Window(
                    title="91JSPG.COM 桌面管理系统",
                    themename="darkly",
                    size=(1200, 800)
                )
                
                # 设置窗口属性
                self.root.resizable(True, True)
                self.root.minsize(1000, 600)
                
                # 居中显示
                self.center_window()
                
                # 当前用户信息
                self.current_admin = None
                self.is_logged_in = False
                
                # 当前显示的界面
                self.current_interface = None
                
                # 创建登录界面
                self.create_login_interface()
                
                # 绑定窗口关闭事件
                self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
            def center_window(self):
                """居中显示窗口"""
                self.root.update_idletasks()
                width = self.root.winfo_width()
                height = self.root.winfo_height()
                x = (self.root.winfo_screenwidth() // 2) - (width // 2)
                y = (self.root.winfo_screenheight() // 2) - (height // 2)
                self.root.geometry(f"{width}x{height}+{x}+{y}")
            
            def create_login_interface(self):
                """创建登录界面"""
                # 清除现有内容
                for widget in self.root.winfo_children():
                    widget.destroy()
                
                self.current_interface = "login"
                
                # 主框架
                main_frame = ttk_bs.Frame(self.root)
                main_frame.pack(fill='both', expand=True, padx=20, pady=20)
                
                # 标题
                title_label = ttk_bs.Label(
                    main_frame,
                    text="91JSPG.COM 桌面管理系统",
                    font=("Arial", 24, "bold")
                )
                title_label.pack(pady=50)
                
                # 登录框架
                login_frame = ttk_bs.LabelFrame(main_frame, text="管理员登录", padding=30)
                login_frame.pack(pady=30)
                
                # 服务器配置
                server_frame = ttk_bs.Frame(login_frame)
                server_frame.pack(fill='x', pady=10)
                
                ttk_bs.Label(server_frame, text="服务器地址:", font=("Arial", 12)).pack(side='left')
                self.server_var = tk.StringVar(value="http://localhost:3001")
                server_entry = ttk_bs.Entry(server_frame, textvariable=self.server_var, width=40)
                server_entry.pack(side='left', padx=10)
                
                ttk_bs.Button(
                    server_frame,
                    text="测试连接",
                    command=self.test_connection,
                    bootstyle="info"
                ).pack(side='left', padx=10)
                
                # 用户名
                user_frame = ttk_bs.Frame(login_frame)
                user_frame.pack(fill='x', pady=10)
                ttk_bs.Label(user_frame, text="用户名:", width=10, font=("Arial", 12)).pack(side='left')
                self.username_var = tk.StringVar()
                ttk_bs.Entry(user_frame, textvariable=self.username_var, width=30, font=("Arial", 12)).pack(side='left', padx=10)
                
                # 密码
                pass_frame = ttk_bs.Frame(login_frame)
                pass_frame.pack(fill='x', pady=10)
                ttk_bs.Label(pass_frame, text="密码:", width=10, font=("Arial", 12)).pack(side='left')
                self.password_var = tk.StringVar()
                password_entry = ttk_bs.Entry(pass_frame, textvariable=self.password_var, show="*", width=30, font=("Arial", 12))
                password_entry.pack(side='left', padx=10)
                
                # 绑定回车键登录
                password_entry.bind('<Return>', lambda e: self.login())
                
                # 登录按钮
                button_frame = ttk_bs.Frame(login_frame)
                button_frame.pack(pady=30)
                
                ttk_bs.Button(
                    button_frame,
                    text="登录",
                    command=self.login,
                    bootstyle="success",
                    width=20
                ).pack()
                
                # 状态栏
                self.status_var = tk.StringVar(value="请输入用户名和密码登录")
                status_bar = ttk_bs.Label(
                    main_frame,
                    textvariable=self.status_var,
                    relief='sunken',
                    anchor='w'
                )
                status_bar.pack(fill='x', side='bottom', pady=(20, 0))
            
            def test_connection(self):
                """测试服务器连接"""
                try:
                    import requests
                    server_url = self.server_var.get().rstrip('/')
                    self.status_var.set("正在测试连接...")
                    self.root.update()
                    
                    # 测试健康检查接口
                    response = requests.get(f"{server_url}/health", timeout=5)
                    
                    if response.status_code == 200:
                        self.status_var.set("✓ 服务器连接成功")
                        messagebox.showinfo("成功", "服务器连接正常！")
                    else:
                        # 尝试根路径
                        response = requests.get(server_url, timeout=5)
                        if response.status_code < 500:
                            self.status_var.set("✓ 服务器连接成功")
                            messagebox.showinfo("成功", "服务器连接正常！")
                        else:
                            self.status_var.set("❌ 服务器响应异常")
                            messagebox.showerror("错误", f"服务器响应异常: {response.status_code}")
                        
                except requests.exceptions.RequestException as e:
                    self.status_var.set("❌ 连接失败")
                    messagebox.showerror("连接错误", f"无法连接到服务器:\n{str(e)}")
                except Exception as e:
                    self.status_var.set("❌ 测试失败")
                    messagebox.showerror("错误", f"测试连接时发生错误:\n{str(e)}")
            
            def login(self):
                """登录"""
                username = self.username_var.get()
                password = self.password_var.get()
                
                if not username or not password:
                    messagebox.showerror("错误", "请输入用户名和密码")
                    return
                
                try:
                    self.status_var.set("正在登录...")
                    self.root.update()

                    # 更新API客户端的服务器地址
                    server_url = self.server_var.get().rstrip('/')
                    api_client.set_base_url(server_url)

                    # 调用登录API
                    print(f"尝试登录到: {server_url}")
                    result = api_client.login(username, password)
                    print(f"登录结果: {result}")

                    # 检查结果是否为None
                    if result is None:
                        self.status_var.set("❌ 登录失败")
                        messagebox.showerror("错误", "服务器无响应或返回无效数据")
                        return

                    if result.get('success'):
                        self.status_var.set("✓ 登录成功")
                        # 从正确的路径获取管理员信息
                        data = result.get('data', {})
                        self.current_admin = data.get('admin', {})
                        self.is_logged_in = True

                        print(f"管理员信息: {self.current_admin}")

                        # 切换到管理界面
                        try:
                            self.create_admin_interface()
                        except Exception as ui_error:
                            print(f"创建管理界面时出错: {ui_error}")
                            import traceback
                            traceback.print_exc()
                            messagebox.showerror("界面错误", f"创建管理界面时发生错误:\n{str(ui_error)}")
                            return
                    else:
                        self.status_var.set("❌ 登录失败")
                        messagebox.showerror("错误", result.get('message', '登录失败'))
                        
                except Exception as e:
                    self.status_var.set("❌ 登录异常")
                    print(f"登录异常详细信息: {e}")
                    import traceback
                    traceback.print_exc()
                    messagebox.showerror("错误", f"登录时发生错误:\n{str(e)}")
            
            def create_admin_interface(self):
                """创建管理界面"""
                # 清除现有内容
                for widget in self.root.winfo_children():
                    widget.destroy()

                self.current_interface = "admin"
                self.current_page = "dashboard"  # 默认显示仪表盘

                # 主框架
                main_frame = ttk_bs.Frame(self.root)
                main_frame.pack(fill='both', expand=True)

                # 顶部工具栏
                self.create_toolbar(main_frame)

                # 主要内容区域 - 使用水平布局
                content_container = ttk_bs.Frame(main_frame)
                content_container.pack(fill='both', expand=True, padx=5, pady=5)

                # 左侧导航菜单
                self.create_navigation(content_container)

                # 右侧内容区域
                self.create_content_area(content_container)

                # 底部状态栏
                self.create_status_bar(main_frame)

                # 默认显示仪表盘
                self.switch_page("dashboard")

            def create_toolbar(self, parent):
                """创建顶部工具栏"""
                toolbar = ttk_bs.Frame(parent, bootstyle="dark")
                toolbar.pack(fill='x', padx=5, pady=5)

                # 左侧 - 系统标题
                title_frame = ttk_bs.Frame(toolbar)
                title_frame.pack(side='left', fill='y')

                ttk_bs.Label(
                    title_frame,
                    text="91JSPG.COM 管理系统",
                    font=("Arial", 16, "bold"),
                    bootstyle="inverse-dark"
                ).pack(side='left', padx=10, pady=5)

                # 右侧 - 用户信息和操作
                user_frame = ttk_bs.Frame(toolbar)
                user_frame.pack(side='right', fill='y')

                # 用户信息
                username = "管理员"
                if self.current_admin and isinstance(self.current_admin, dict):
                    username = self.current_admin.get('username', '管理员')

                ttk_bs.Label(
                    user_frame,
                    text=f"欢迎，{username}",
                    font=("Arial", 12),
                    bootstyle="inverse-dark"
                ).pack(side='left', padx=10, pady=5)

                # 退出按钮
                ttk_bs.Button(
                    user_frame,
                    text="退出登录",
                    command=self.logout,
                    bootstyle="danger-outline",
                    width=10
                ).pack(side='right', padx=10, pady=5)

            def create_navigation(self, parent):
                """创建左侧导航菜单"""
                nav_frame = ttk_bs.LabelFrame(parent, text="导航菜单", padding=10)
                nav_frame.pack(side='left', fill='y', padx=(0, 5))

                # 导航按钮配置
                nav_buttons = [
                    ("dashboard", "🏠 仪表盘", "info"),
                    ("videos", "🎬 视频管理", "primary"),
                    ("categories", "📁 分类管理", "secondary"),
                    ("admins", "👥 管理员", "warning"),
                    ("api_keys", "🔑 API密钥", "success"),
                    ("settings", "⚙️ 系统设置", "dark")
                ]

                self.nav_buttons = {}

                for page_id, title, style in nav_buttons:
                    btn = ttk_bs.Button(
                        nav_frame,
                        text=title,
                        bootstyle=f"{style}-outline",
                        width=15,
                        command=lambda p=page_id: self.switch_page(p)
                    )
                    btn.pack(fill='x', pady=5)
                    self.nav_buttons[page_id] = btn

            def create_content_area(self, parent):
                """创建右侧内容区域"""
                self.content_frame = ttk_bs.Frame(parent)
                self.content_frame.pack(side='right', fill='both', expand=True)

            def create_status_bar(self, parent):
                """创建底部状态栏"""
                self.status_var.set(f"管理界面已加载 - 登录时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                status_bar = ttk_bs.Label(
                    parent,
                    textvariable=self.status_var,
                    relief='sunken',
                    anchor='w'
                )
                status_bar.pack(fill='x', side='bottom', padx=5, pady=5)

            def switch_page(self, page_id):
                """切换页面"""
                # 清除当前内容
                for widget in self.content_frame.winfo_children():
                    widget.destroy()

                # 更新导航按钮状态
                nav_styles = {
                    "dashboard": "info",
                    "videos": "primary",
                    "categories": "secondary",
                    "admins": "warning",
                    "api_keys": "success",
                    "settings": "dark"
                }

                for btn_id, btn in self.nav_buttons.items():
                    base_style = nav_styles.get(btn_id, "secondary")
                    if btn_id == page_id:
                        # 当前页面按钮高亮
                        try:
                            btn.configure(bootstyle=base_style)
                        except:
                            pass  # 忽略配置错误
                    else:
                        # 其他按钮恢复outline样式
                        try:
                            btn.configure(bootstyle=f"{base_style}-outline")
                        except:
                            pass  # 忽略配置错误

                self.current_page = page_id

                # 根据页面ID显示对应内容
                if page_id == "dashboard":
                    self.show_dashboard_page()
                elif page_id == "videos":
                    self.show_videos_page()
                elif page_id == "categories":
                    self.show_categories_page()
                elif page_id == "admins":
                    self.show_admins_page()
                elif page_id == "api_keys":
                    self.show_api_keys_page()
                elif page_id == "settings":
                    self.show_settings_page()

                self.status_var.set(f"当前页面: {page_id} - {datetime.now().strftime('%H:%M:%S')}")
            
            def show_dashboard_page(self):
                """显示仪表盘页面"""
                # 页面标题
                title_frame = ttk_bs.Frame(self.content_frame)
                title_frame.pack(fill='x', pady=(0, 20))

                ttk_bs.Label(
                    title_frame,
                    text="📊 系统仪表盘",
                    font=("Arial", 20, "bold")
                ).pack(side='left')

                ttk_bs.Button(
                    title_frame,
                    text="🔄 刷新数据",
                    command=self.refresh_dashboard,
                    bootstyle="info-outline"
                ).pack(side='right')

                # 统计卡片区域
                stats_frame = ttk_bs.Frame(self.content_frame)
                stats_frame.pack(fill='x', pady=(0, 20))

                # 创建统计卡片
                self.create_stat_cards(stats_frame)

                # 图表区域（占位）
                chart_frame = ttk_bs.LabelFrame(self.content_frame, text="📈 数据图表", padding=20)
                chart_frame.pack(fill='both', expand=True)

                ttk_bs.Label(
                    chart_frame,
                    text="图表功能开发中...\n\n将显示:\n• 访问量趋势\n• 视频观看统计\n• 用户活跃度",
                    font=("Arial", 12),
                    justify='center'
                ).pack(expand=True)

                # 加载数据
                self.refresh_dashboard()

            def create_stat_cards(self, parent):
                """创建统计卡片"""
                # 创建卡片容器
                cards_container = ttk_bs.Frame(parent)
                cards_container.pack(fill='x')

                # 统计卡片配置
                self.stat_cards = {}
                card_configs = [
                    ("videos", "🎬", "视频总数", "0", "primary"),
                    ("categories", "📁", "分类总数", "0", "secondary"),
                    ("admins", "👥", "管理员", "0", "warning"),
                    ("views", "👁️", "总观看", "0", "success")
                ]

                for i, (key, icon, title, value, style) in enumerate(card_configs):
                    card = ttk_bs.LabelFrame(cards_container, text="", padding=15, bootstyle=style)
                    card.pack(side='left', fill='both', expand=True, padx=5)

                    # 图标和数值
                    ttk_bs.Label(
                        card,
                        text=icon,
                        font=("Arial", 24)
                    ).pack()

                    value_label = ttk_bs.Label(
                        card,
                        text=value,
                        font=("Arial", 18, "bold")
                    )
                    value_label.pack()

                    ttk_bs.Label(
                        card,
                        text=title,
                        font=("Arial", 10)
                    ).pack()

                    self.stat_cards[key] = value_label

            def refresh_dashboard(self):
                """刷新仪表盘数据"""
                try:
                    self.status_var.set("正在加载仪表盘数据...")

                    # 调用仪表盘API
                    result = api_client.get_dashboard()

                    if result.get('success'):
                        data = result.get('data', {})

                        # 更新统计卡片
                        self.stat_cards['videos'].config(text=str(data.get('videoCount', 0)))
                        self.stat_cards['categories'].config(text=str(data.get('categoryCount', 0)))
                        self.stat_cards['admins'].config(text=str(data.get('adminCount', 0)))
                        self.stat_cards['views'].config(text=str(data.get('totalViews', 0)))

                        self.status_var.set("✓ 仪表盘数据已更新")
                    else:
                        self.status_var.set("❌ 仪表盘数据加载失败")

                except Exception as e:
                    self.status_var.set(f"❌ 仪表盘加载异常: {str(e)}")

            def show_dashboard(self):
                """显示仪表盘"""
                try:
                    self.status_var.set("正在加载仪表盘数据...")
                    self.root.update()

                    # 调用仪表盘API
                    result = api_client.get_dashboard()

                    if result.get('success'):
                        data = result.get('data', {})
                        info_text = f"""
仪表盘数据：

视频总数: {data.get('videoCount', 'N/A')}
分类总数: {data.get('categoryCount', 'N/A')}
管理员总数: {data.get('adminCount', 'N/A')}
今日观看: {data.get('todayViews', 'N/A')}
总观看数: {data.get('totalViews', 'N/A')}

系统状态: 正常运行
最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                        """
                        messagebox.showinfo("仪表盘", info_text)
                        self.status_var.set("✓ 仪表盘数据加载完成")
                    else:
                        messagebox.showerror("错误", result.get('message', '获取仪表盘数据失败'))
                        self.status_var.set("❌ 仪表盘数据加载失败")

                except Exception as e:
                    messagebox.showerror("错误", f"加载仪表盘时发生错误:\n{str(e)}")
                    self.status_var.set("❌ 仪表盘加载异常")

            def show_videos_page(self):
                """显示视频管理页面"""
                # 页面标题和操作按钮
                title_frame = ttk_bs.Frame(self.content_frame)
                title_frame.pack(fill='x', pady=(0, 20))

                ttk_bs.Label(
                    title_frame,
                    text="🎬 视频管理",
                    font=("Arial", 20, "bold")
                ).pack(side='left')

                # 操作按钮
                btn_frame = ttk_bs.Frame(title_frame)
                btn_frame.pack(side='right')

                ttk_bs.Button(
                    btn_frame,
                    text="➕ 添加视频",
                    command=self.add_video,
                    bootstyle="success"
                ).pack(side='left', padx=5)

                ttk_bs.Button(
                    btn_frame,
                    text="🔄 刷新",
                    command=self.refresh_videos,
                    bootstyle="info-outline"
                ).pack(side='left', padx=5)

                # 搜索和筛选区域
                search_frame = ttk_bs.LabelFrame(self.content_frame, text="搜索和筛选", padding=10)
                search_frame.pack(fill='x', pady=(0, 20))

                search_row = ttk_bs.Frame(search_frame)
                search_row.pack(fill='x')

                ttk_bs.Label(search_row, text="搜索:").pack(side='left', padx=(0, 5))
                self.video_search_var = tk.StringVar()
                ttk_bs.Entry(search_row, textvariable=self.video_search_var, width=30).pack(side='left', padx=5)

                ttk_bs.Button(
                    search_row,
                    text="🔍 搜索",
                    command=self.search_videos,
                    bootstyle="primary-outline"
                ).pack(side='left', padx=5)

                # 视频列表区域
                list_frame = ttk_bs.LabelFrame(self.content_frame, text="视频列表", padding=10)
                list_frame.pack(fill='both', expand=True)

                # 创建表格
                self.create_videos_table(list_frame)

                # 加载视频数据
                self.refresh_videos()

            def create_videos_table(self, parent):
                """创建视频表格"""
                # 表格框架
                table_frame = ttk_bs.Frame(parent)
                table_frame.pack(fill='both', expand=True)

                # 表格列定义
                columns = ("ID", "标题", "分类", "时长", "观看数", "状态", "创建时间", "操作")

                # 创建Treeview
                self.videos_tree = ttk_bs.Treeview(
                    table_frame,
                    columns=columns,
                    show='headings',
                    height=15
                )

                # 设置列标题和宽度
                column_widths = [50, 200, 100, 80, 80, 80, 120, 100]
                for i, (col, width) in enumerate(zip(columns, column_widths)):
                    self.videos_tree.heading(col, text=col)
                    self.videos_tree.column(col, width=width, minwidth=50)

                # 滚动条
                v_scrollbar = tk.Scrollbar(table_frame, orient='vertical', command=self.videos_tree.yview)
                h_scrollbar = tk.Scrollbar(table_frame, orient='horizontal', command=self.videos_tree.xview)
                self.videos_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

                # 布局
                self.videos_tree.pack(side='left', fill='both', expand=True)
                v_scrollbar.pack(side='right', fill='y')
                h_scrollbar.pack(side='bottom', fill='x')

                # 绑定双击事件
                self.videos_tree.bind('<Double-1>', self.edit_video)

                # 右键菜单
                self.create_video_context_menu()

            def create_video_context_menu(self):
                """创建视频右键菜单"""
                self.video_context_menu = tk.Menu(self.root, tearoff=0)
                self.video_context_menu.add_command(label="📝 编辑", command=self.edit_video)
                self.video_context_menu.add_command(label="👁️ 查看详情", command=self.view_video)
                self.video_context_menu.add_separator()
                self.video_context_menu.add_command(label="🗑️ 删除", command=self.delete_video)

                # 绑定右键事件
                self.videos_tree.bind('<Button-3>', self.show_video_context_menu)

            def show_video_context_menu(self, event):
                """显示视频右键菜单"""
                # 选择点击的项目
                item = self.videos_tree.identify_row(event.y)
                if item:
                    self.videos_tree.selection_set(item)
                    self.video_context_menu.post(event.x_root, event.y_root)

            def refresh_videos(self):
                """刷新视频列表"""
                try:
                    self.status_var.set("正在加载视频列表...")

                    # 清空现有数据
                    for item in self.videos_tree.get_children():
                        self.videos_tree.delete(item)

                    # 调用API获取视频列表
                    result = api_client.get_videos(page=1, limit=100)

                    if result.get('success'):
                        data = result.get('data', {})
                        videos = data.get('videos', [])

                        # 填充表格数据
                        for video in videos:
                            self.videos_tree.insert('', 'end', values=(
                                video.get('id', ''),
                                video.get('title', '')[:30] + ('...' if len(video.get('title', '')) > 30 else ''),
                                video.get('category', {}).get('name', '未分类'),
                                video.get('duration', ''),
                                video.get('viewCount', 0),
                                video.get('status', ''),
                                video.get('createdAt', '')[:10] if video.get('createdAt') else '',
                                "双击编辑"
                            ))

                        self.status_var.set(f"✓ 已加载 {len(videos)} 个视频")
                    else:
                        self.status_var.set("❌ 视频列表加载失败")

                except Exception as e:
                    self.status_var.set(f"❌ 视频列表加载异常: {str(e)}")

            def search_videos(self):
                """搜索视频"""
                search_term = self.video_search_var.get()
                if search_term:
                    self.status_var.set(f"搜索: {search_term}")
                    # 这里可以调用搜索API
                else:
                    self.refresh_videos()

            def add_video(self):
                """添加视频"""
                messagebox.showinfo("添加视频", "添加视频功能开发中...\n\n将包含:\n• 视频信息编辑\n• 文件上传\n• 分类选择\n• 标签设置")

            def edit_video(self, event=None):
                """编辑视频"""
                selection = self.videos_tree.selection()
                if selection:
                    item = self.videos_tree.item(selection[0])
                    video_id = item['values'][0]
                    messagebox.showinfo("编辑视频", f"编辑视频功能开发中...\n\n视频ID: {video_id}\n\n将包含:\n• 视频信息编辑\n• 状态管理\n• 分类修改")

            def view_video(self):
                """查看视频详情"""
                selection = self.videos_tree.selection()
                if selection:
                    item = self.videos_tree.item(selection[0])
                    video_id = item['values'][0]
                    messagebox.showinfo("视频详情", f"视频详情功能开发中...\n\n视频ID: {video_id}")

            def delete_video(self):
                """删除视频"""
                selection = self.videos_tree.selection()
                if selection:
                    item = self.videos_tree.item(selection[0])
                    video_id = item['values'][0]
                    video_title = item['values'][1]

                    if messagebox.askyesno("确认删除", f"确定要删除视频吗？\n\nID: {video_id}\n标题: {video_title}"):
                        messagebox.showinfo("删除视频", "删除功能开发中...")

            def show_videos(self):
                """显示视频管理"""
                try:
                    self.status_var.set("正在加载视频列表...")
                    self.root.update()

                    # 调用视频列表API
                    result = api_client.get_videos(page=1, limit=10)

                    if result.get('success'):
                        data = result.get('data', {})
                        videos = data.get('videos', [])
                        total = data.get('total', 0)

                        video_list = "\n".join([
                            f"• {video.get('title', 'N/A')} (ID: {video.get('id', 'N/A')})"
                            for video in videos[:5]  # 只显示前5个
                        ])

                        info_text = f"""
视频管理：

总视频数: {total}
当前页显示: {len(videos)}

最近视频:
{video_list}
{'...' if len(videos) > 5 else ''}

提示: 完整的视频管理功能正在开发中
                        """
                        messagebox.showinfo("视频管理", info_text)
                        self.status_var.set("✓ 视频列表加载完成")
                    else:
                        messagebox.showerror("错误", result.get('message', '获取视频列表失败'))
                        self.status_var.set("❌ 视频列表加载失败")

                except Exception as e:
                    messagebox.showerror("错误", f"加载视频列表时发生错误:\n{str(e)}")
                    self.status_var.set("❌ 视频管理加载异常")

            def show_categories_page(self):
                """显示分类管理页面"""
                # 页面标题和操作按钮
                title_frame = ttk_bs.Frame(self.content_frame)
                title_frame.pack(fill='x', pady=(0, 20))

                ttk_bs.Label(
                    title_frame,
                    text="📁 分类管理",
                    font=("Arial", 20, "bold")
                ).pack(side='left')

                # 操作按钮
                btn_frame = ttk_bs.Frame(title_frame)
                btn_frame.pack(side='right')

                ttk_bs.Button(
                    btn_frame,
                    text="➕ 添加分类",
                    command=self.add_category,
                    bootstyle="success"
                ).pack(side='left', padx=5)

                ttk_bs.Button(
                    btn_frame,
                    text="🔄 刷新",
                    command=self.refresh_categories,
                    bootstyle="info-outline"
                ).pack(side='left', padx=5)

                # 分类列表区域
                list_frame = ttk_bs.LabelFrame(self.content_frame, text="分类列表", padding=10)
                list_frame.pack(fill='both', expand=True)

                # 创建分类表格
                self.create_categories_table(list_frame)

                # 加载分类数据
                self.refresh_categories()

            def create_categories_table(self, parent):
                """创建分类表格"""
                # 表格框架
                table_frame = ttk_bs.Frame(parent)
                table_frame.pack(fill='both', expand=True)

                # 表格列定义
                columns = ("ID", "分类名称", "描述", "视频数量", "状态", "创建时间", "操作")

                # 创建Treeview
                self.categories_tree = ttk_bs.Treeview(
                    table_frame,
                    columns=columns,
                    show='headings',
                    height=15
                )

                # 设置列标题和宽度
                column_widths = [50, 150, 250, 100, 80, 120, 100]
                for i, (col, width) in enumerate(zip(columns, column_widths)):
                    self.categories_tree.heading(col, text=col)
                    self.categories_tree.column(col, width=width, minwidth=50)

                # 滚动条
                v_scrollbar = tk.Scrollbar(table_frame, orient='vertical', command=self.categories_tree.yview)
                h_scrollbar = tk.Scrollbar(table_frame, orient='horizontal', command=self.categories_tree.xview)
                self.categories_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

                # 布局
                self.categories_tree.pack(side='left', fill='both', expand=True)
                v_scrollbar.pack(side='right', fill='y')
                h_scrollbar.pack(side='bottom', fill='x')

                # 绑定双击事件
                self.categories_tree.bind('<Double-1>', self.edit_category)

                # 右键菜单
                self.create_category_context_menu()

            def create_category_context_menu(self):
                """创建分类右键菜单"""
                self.category_context_menu = tk.Menu(self.root, tearoff=0)
                self.category_context_menu.add_command(label="📝 编辑", command=self.edit_category)
                self.category_context_menu.add_command(label="👁️ 查看视频", command=self.view_category_videos)
                self.category_context_menu.add_separator()
                self.category_context_menu.add_command(label="🗑️ 删除", command=self.delete_category)

                # 绑定右键事件
                self.categories_tree.bind('<Button-3>', self.show_category_context_menu)

            def show_category_context_menu(self, event):
                """显示分类右键菜单"""
                # 选择点击的项目
                item = self.categories_tree.identify_row(event.y)
                if item:
                    self.categories_tree.selection_set(item)
                    self.category_context_menu.post(event.x_root, event.y_root)

            def refresh_categories(self):
                """刷新分类列表"""
                try:
                    self.status_var.set("正在加载分类列表...")

                    # 清空现有数据
                    for item in self.categories_tree.get_children():
                        self.categories_tree.delete(item)

                    # 调用API获取分类列表
                    result = api_client.get_categories(page=1, limit=100)

                    if result.get('success'):
                        data = result.get('data', {})
                        categories = data.get('categories', [])

                        # 填充表格数据
                        for category in categories:
                            self.categories_tree.insert('', 'end', values=(
                                category.get('id', ''),
                                category.get('name', ''),
                                category.get('description', '')[:40] + ('...' if len(category.get('description', '')) > 40 else ''),
                                category.get('videoCount', 0),
                                category.get('status', ''),
                                category.get('createdAt', '')[:10] if category.get('createdAt') else '',
                                "双击编辑"
                            ))

                        self.status_var.set(f"✓ 已加载 {len(categories)} 个分类")
                    else:
                        self.status_var.set("❌ 分类列表加载失败")

                except Exception as e:
                    self.status_var.set(f"❌ 分类列表加载异常: {str(e)}")

            def add_category(self):
                """添加分类"""
                messagebox.showinfo("添加分类", "添加分类功能开发中...\n\n将包含:\n• 分类名称\n• 分类描述\n• 状态设置\n• 排序设置")

            def edit_category(self, event=None):
                """编辑分类"""
                selection = self.categories_tree.selection()
                if selection:
                    item = self.categories_tree.item(selection[0])
                    category_id = item['values'][0]
                    messagebox.showinfo("编辑分类", f"编辑分类功能开发中...\n\n分类ID: {category_id}")

            def view_category_videos(self):
                """查看分类下的视频"""
                selection = self.categories_tree.selection()
                if selection:
                    item = self.categories_tree.item(selection[0])
                    category_id = item['values'][0]
                    category_name = item['values'][1]
                    messagebox.showinfo("分类视频", f"查看分类视频功能开发中...\n\n分类: {category_name}\nID: {category_id}")

            def delete_category(self):
                """删除分类"""
                selection = self.categories_tree.selection()
                if selection:
                    item = self.categories_tree.item(selection[0])
                    category_id = item['values'][0]
                    category_name = item['values'][1]

                    if messagebox.askyesno("确认删除", f"确定要删除分类吗？\n\n分类: {category_name}\nID: {category_id}"):
                        messagebox.showinfo("删除分类", "删除功能开发中...")

            def show_categories(self):
                """显示分类管理"""
                try:
                    self.status_var.set("正在加载分类列表...")
                    self.root.update()

                    # 调用分类列表API
                    result = api_client.get_categories(page=1, limit=10)

                    if result.get('success'):
                        data = result.get('data', {})
                        categories = data.get('categories', [])
                        total = data.get('total', 0)

                        category_list = "\n".join([
                            f"• {cat.get('name', 'N/A')} (ID: {cat.get('id', 'N/A')})"
                            for cat in categories
                        ])

                        info_text = f"""
分类管理：

总分类数: {total}
当前页显示: {len(categories)}

分类列表:
{category_list}

提示: 完整的分类管理功能正在开发中
                        """
                        messagebox.showinfo("分类管理", info_text)
                        self.status_var.set("✓ 分类列表加载完成")
                    else:
                        messagebox.showerror("错误", result.get('message', '获取分类列表失败'))
                        self.status_var.set("❌ 分类列表加载失败")

                except Exception as e:
                    messagebox.showerror("错误", f"加载分类列表时发生错误:\n{str(e)}")
                    self.status_var.set("❌ 分类管理加载异常")

            def show_admins_page(self):
                """显示管理员管理页面"""
                # 页面标题
                title_frame = ttk_bs.Frame(self.content_frame)
                title_frame.pack(fill='x', pady=(0, 20))

                ttk_bs.Label(
                    title_frame,
                    text="👥 管理员管理",
                    font=("Arial", 20, "bold")
                ).pack(side='left')

                ttk_bs.Button(
                    title_frame,
                    text="➕ 添加管理员",
                    command=lambda: messagebox.showinfo("添加管理员", "添加管理员功能开发中..."),
                    bootstyle="success"
                ).pack(side='right')

                # 内容区域
                content = ttk_bs.LabelFrame(self.content_frame, text="管理员列表", padding=20)
                content.pack(fill='both', expand=True)

                ttk_bs.Label(
                    content,
                    text="管理员管理功能开发中...\n\n将包含:\n• 查看管理员列表\n• 创建新管理员\n• 编辑管理员信息\n• 删除管理员\n• 权限管理",
                    font=("Arial", 12),
                    justify='center'
                ).pack(expand=True)

            def show_api_keys_page(self):
                """显示API密钥管理页面"""
                # 页面标题
                title_frame = ttk_bs.Frame(self.content_frame)
                title_frame.pack(fill='x', pady=(0, 20))

                ttk_bs.Label(
                    title_frame,
                    text="🔑 API密钥管理",
                    font=("Arial", 20, "bold")
                ).pack(side='left')

                ttk_bs.Button(
                    title_frame,
                    text="➕ 生成密钥",
                    command=lambda: messagebox.showinfo("生成密钥", "生成API密钥功能开发中..."),
                    bootstyle="success"
                ).pack(side='right')

                # 内容区域
                content = ttk_bs.LabelFrame(self.content_frame, text="API密钥列表", padding=20)
                content.pack(fill='both', expand=True)

                ttk_bs.Label(
                    content,
                    text="API密钥管理功能开发中...\n\n将包含:\n• 查看API密钥列表\n• 创建新API密钥\n• 重新生成密钥\n• 删除API密钥\n• 权限设置",
                    font=("Arial", 12),
                    justify='center'
                ).pack(expand=True)

            def show_settings_page(self):
                """显示系统设置页面"""
                # 页面标题
                title_frame = ttk_bs.Frame(self.content_frame)
                title_frame.pack(fill='x', pady=(0, 20))

                ttk_bs.Label(
                    title_frame,
                    text="⚙️ 系统设置",
                    font=("Arial", 20, "bold")
                ).pack(side='left')

                # 设置内容
                settings_frame = ttk_bs.LabelFrame(self.content_frame, text="系统配置", padding=20)
                settings_frame.pack(fill='both', expand=True)

                # 服务器设置
                server_frame = ttk_bs.LabelFrame(settings_frame, text="服务器设置", padding=15)
                server_frame.pack(fill='x', pady=(0, 20))

                ttk_bs.Label(server_frame, text="服务器地址:").pack(anchor='w')
                ttk_bs.Label(server_frame, text=self.server_var.get(), font=("Arial", 10, "bold")).pack(anchor='w', padx=20)

                # 用户信息
                user_frame = ttk_bs.LabelFrame(settings_frame, text="当前用户", padding=15)
                user_frame.pack(fill='x', pady=(0, 20))

                if self.current_admin:
                    ttk_bs.Label(user_frame, text=f"用户名: {self.current_admin.get('username', 'N/A')}").pack(anchor='w')
                    ttk_bs.Label(user_frame, text=f"邮箱: {self.current_admin.get('email', 'N/A')}").pack(anchor='w')
                    ttk_bs.Label(user_frame, text=f"角色: {self.current_admin.get('name', 'N/A')}").pack(anchor='w')

                # 系统信息
                system_frame = ttk_bs.LabelFrame(settings_frame, text="系统信息", padding=15)
                system_frame.pack(fill='x')

                ttk_bs.Label(system_frame, text="应用版本: v1.0.0").pack(anchor='w')
                ttk_bs.Label(system_frame, text="主题: darkly").pack(anchor='w')
                ttk_bs.Label(system_frame, text=f"窗口大小: {self.root.winfo_width()}x{self.root.winfo_height()}").pack(anchor='w')

            def show_admins(self):
                """显示管理员管理"""
                messagebox.showinfo("管理员管理", "管理员管理功能正在开发中...\n\n当前功能:\n• 查看管理员列表\n• 创建新管理员\n• 编辑管理员信息\n• 删除管理员")
                self.status_var.set("管理员管理功能开发中")

            def show_api_keys(self):
                """显示API密钥管理"""
                messagebox.showinfo("API密钥管理", "API密钥管理功能正在开发中...\n\n当前功能:\n• 查看API密钥列表\n• 创建新API密钥\n• 重新生成密钥\n• 删除API密钥")
                self.status_var.set("API密钥管理功能开发中")

            def show_settings(self):
                """显示系统设置"""
                settings_text = f"""
系统设置：

服务器地址: {self.server_var.get()}
当前主题: darkly
窗口大小: 1200x800
登录用户: {self.current_admin.get('username', 'N/A') if self.current_admin else 'N/A'}

提示: 完整的设置功能正在开发中
                """
                messagebox.showinfo("系统设置", settings_text)
                self.status_var.set("系统设置功能开发中")

            def logout(self):
                """退出登录"""
                if messagebox.askokcancel("退出登录", "确定要退出登录吗？"):
                    self.current_admin = None
                    self.is_logged_in = False
                    self.create_login_interface()

            def on_closing(self):
                """窗口关闭事件"""
                if messagebox.askokcancel("退出", "确定要退出应用程序吗？"):
                    self.root.destroy()

            def run(self):
                """运行应用"""
                self.root.mainloop()
        
        print("✓ 应用类创建成功")
        
        print("\n4. 启动应用...")
        app = IntegratedVideoAdminApp()
        app.run()
        
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()

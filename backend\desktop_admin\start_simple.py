#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
91JSPG.COM 桌面管理应用 - 简化版测试
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
from datetime import datetime

def main():
    """主函数"""
    print("=" * 50)
    print("91JSPG.COM 桌面管理应用 - 简化版测试")
    print("=" * 50)
    
    try:
        # 添加当前目录到路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)
        
        print("1. 检查基本模块...")
        
        # 检查ttkbootstrap
        import ttkbootstrap as ttk_bs
        print("✓ ttkbootstrap")
        
        # 检查配置和API客户端
        from config import config
        from api_client import api_client
        print("✓ config & api_client")
        
        print("\n2. 创建简化应用...")
        
        # 创建简化的应用类
        class SimpleVideoAdminApp:
            def __init__(self):
                # 创建主窗口
                self.root = ttk_bs.Window(
                    title="91JSPG.COM 管理系统 - 简化版",
                    themename="darkly",
                    size=(1000, 700)
                )
                
                # 设置窗口属性
                self.root.resizable(True, True)
                self.root.minsize(800, 600)
                
                # 居中显示
                self.center_window()
                
                # 当前用户信息
                self.current_admin = None
                self.is_logged_in = False
                
                # 创建登录界面
                self.create_login_interface()
                
                # 绑定窗口关闭事件
                self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
            def center_window(self):
                """居中显示窗口"""
                self.root.update_idletasks()
                width = self.root.winfo_width()
                height = self.root.winfo_height()
                x = (self.root.winfo_screenwidth() // 2) - (width // 2)
                y = (self.root.winfo_screenheight() // 2) - (height // 2)
                self.root.geometry(f"{width}x{height}+{x}+{y}")
            
            def create_login_interface(self):
                """创建登录界面"""
                # 清除现有内容
                for widget in self.root.winfo_children():
                    widget.destroy()
                
                # 主框架
                main_frame = ttk_bs.Frame(self.root)
                main_frame.pack(fill='both', expand=True, padx=20, pady=20)
                
                # 标题
                title_label = ttk_bs.Label(
                    main_frame,
                    text="91JSPG.COM 管理系统",
                    font=("Arial", 24, "bold")
                )
                title_label.pack(pady=50)
                
                # 登录框架
                login_frame = ttk_bs.LabelFrame(main_frame, text="管理员登录", padding=30)
                login_frame.pack(pady=30)
                
                # 服务器配置
                server_frame = ttk_bs.Frame(login_frame)
                server_frame.pack(fill='x', pady=10)
                
                ttk_bs.Label(server_frame, text="服务器地址:", font=("Arial", 12)).pack(side='left')
                self.server_var = tk.StringVar(value="http://localhost:3001")
                server_entry = ttk_bs.Entry(server_frame, textvariable=self.server_var, width=40)
                server_entry.pack(side='left', padx=10)
                
                # 用户名
                user_frame = ttk_bs.Frame(login_frame)
                user_frame.pack(fill='x', pady=10)
                ttk_bs.Label(user_frame, text="用户名:", width=10, font=("Arial", 12)).pack(side='left')
                self.username_var = tk.StringVar()
                ttk_bs.Entry(user_frame, textvariable=self.username_var, width=30, font=("Arial", 12)).pack(side='left', padx=10)
                
                # 密码
                pass_frame = ttk_bs.Frame(login_frame)
                pass_frame.pack(fill='x', pady=10)
                ttk_bs.Label(pass_frame, text="密码:", width=10, font=("Arial", 12)).pack(side='left')
                self.password_var = tk.StringVar()
                password_entry = ttk_bs.Entry(pass_frame, textvariable=self.password_var, show="*", width=30, font=("Arial", 12))
                password_entry.pack(side='left', padx=10)
                
                # 绑定回车键登录
                password_entry.bind('<Return>', lambda e: self.login())
                
                # 登录按钮
                button_frame = ttk_bs.Frame(login_frame)
                button_frame.pack(pady=30)
                
                ttk_bs.Button(
                    button_frame,
                    text="登录",
                    command=self.login,
                    bootstyle="success",
                    width=20
                ).pack()
                
                # 状态栏
                self.status_var = tk.StringVar(value="请输入用户名和密码登录")
                status_bar = ttk_bs.Label(
                    main_frame,
                    textvariable=self.status_var,
                    relief='sunken',
                    anchor='w'
                )
                status_bar.pack(fill='x', side='bottom', pady=(20, 0))
            
            def login(self):
                """登录"""
                username = self.username_var.get()
                password = self.password_var.get()
                
                if not username or not password:
                    messagebox.showerror("错误", "请输入用户名和密码")
                    return
                
                try:
                    self.status_var.set("正在登录...")
                    self.root.update()
                    
                    # 更新API客户端的服务器地址
                    server_url = self.server_var.get().rstrip('/')
                    api_client.set_base_url(server_url)
                    
                    # 调用登录API
                    print(f"尝试登录到: {server_url}")
                    result = api_client.login(username, password)
                    print(f"登录结果: {result}")
                    
                    # 检查结果是否为None
                    if result is None:
                        self.status_var.set("❌ 登录失败")
                        messagebox.showerror("错误", "服务器无响应或返回无效数据")
                        return
                    
                    if result.get('success'):
                        self.status_var.set("✓ 登录成功")
                        # 从正确的路径获取管理员信息
                        data = result.get('data', {})
                        self.current_admin = data.get('admin', {})
                        self.is_logged_in = True
                        
                        print(f"管理员信息: {self.current_admin}")
                        
                        # 切换到管理界面
                        self.create_simple_admin_interface()
                    else:
                        self.status_var.set("❌ 登录失败")
                        messagebox.showerror("错误", result.get('message', '登录失败'))
                        
                except Exception as e:
                    self.status_var.set("❌ 登录异常")
                    print(f"登录异常详细信息: {e}")
                    import traceback
                    traceback.print_exc()
                    messagebox.showerror("错误", f"登录时发生错误:\n{str(e)}")
            
            def create_simple_admin_interface(self):
                """创建简化的管理界面"""
                try:
                    # 清除现有内容
                    for widget in self.root.winfo_children():
                        widget.destroy()
                    
                    # 主框架
                    main_frame = ttk_bs.Frame(self.root)
                    main_frame.pack(fill='both', expand=True, padx=10, pady=10)
                    
                    # 顶部工具栏
                    toolbar = ttk_bs.Frame(main_frame)
                    toolbar.pack(fill='x', pady=(0, 20))
                    
                    # 标题
                    ttk_bs.Label(
                        toolbar,
                        text="91JSPG.COM 管理系统",
                        font=("Arial", 18, "bold")
                    ).pack(side='left')
                    
                    # 用户信息
                    username = "管理员"
                    if self.current_admin and isinstance(self.current_admin, dict):
                        username = self.current_admin.get('username', '管理员')
                    
                    ttk_bs.Label(
                        toolbar,
                        text=f"欢迎，{username}",
                        font=("Arial", 12)
                    ).pack(side='left', padx=50)
                    
                    # 退出按钮
                    ttk_bs.Button(
                        toolbar,
                        text="退出登录",
                        command=self.logout
                    ).pack(side='right')
                    
                    # 功能区域
                    content_frame = ttk_bs.LabelFrame(main_frame, text="管理功能", padding=20)
                    content_frame.pack(fill='both', expand=True)
                    
                    # 功能按钮
                    buttons_info = [
                        ("仪表盘", "查看系统概览", self.show_dashboard),
                        ("视频管理", "管理视频内容", self.show_videos),
                        ("分类管理", "管理视频分类", self.show_categories),
                        ("管理员管理", "管理系统用户", self.show_admins),
                    ]
                    
                    for title, desc, command in buttons_info:
                        btn_frame = ttk_bs.Frame(content_frame)
                        btn_frame.pack(fill='x', pady=10)
                        
                        ttk_bs.Button(
                            btn_frame,
                            text=title,
                            command=command,
                            width=15
                        ).pack(side='left', padx=10)
                        
                        ttk_bs.Label(btn_frame, text=desc, font=("Arial", 10)).pack(side='left', padx=10)
                    
                    # 状态栏
                    self.status_var.set(f"管理界面已加载 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    status_bar = ttk_bs.Label(
                        main_frame,
                        textvariable=self.status_var,
                        relief='sunken',
                        anchor='w'
                    )
                    status_bar.pack(fill='x', side='bottom', pady=(20, 0))
                    
                except Exception as e:
                    print(f"创建管理界面时出错: {e}")
                    import traceback
                    traceback.print_exc()
                    messagebox.showerror("界面错误", f"创建管理界面时发生错误:\n{str(e)}")
            
            def show_dashboard(self):
                """显示仪表盘"""
                messagebox.showinfo("仪表盘", "仪表盘功能正常！\n\n这是简化版测试。")
                self.status_var.set("仪表盘功能测试完成")
            
            def show_videos(self):
                """显示视频管理"""
                messagebox.showinfo("视频管理", "视频管理功能正常！\n\n这是简化版测试。")
                self.status_var.set("视频管理功能测试完成")
            
            def show_categories(self):
                """显示分类管理"""
                messagebox.showinfo("分类管理", "分类管理功能正常！\n\n这是简化版测试。")
                self.status_var.set("分类管理功能测试完成")
            
            def show_admins(self):
                """显示管理员管理"""
                messagebox.showinfo("管理员管理", "管理员管理功能正常！\n\n这是简化版测试。")
                self.status_var.set("管理员管理功能测试完成")
            
            def logout(self):
                """退出登录"""
                if messagebox.askokcancel("退出登录", "确定要退出登录吗？"):
                    self.current_admin = None
                    self.is_logged_in = False
                    self.create_login_interface()
            
            def on_closing(self):
                """窗口关闭事件"""
                if messagebox.askokcancel("退出", "确定要退出应用程序吗？"):
                    self.root.destroy()
            
            def run(self):
                """运行应用"""
                self.root.mainloop()
        
        print("✓ 简化应用类创建成功")
        
        print("\n3. 启动简化应用...")
        app = SimpleVideoAdminApp()
        app.run()
        
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()

# 视频管理框架
import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import threading
from datetime import datetime

from api_client import api_client

class VideosFrame:
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.frame = None
        self.is_visible = False
        self.videos_data = []
        self.current_page = 1
        self.total_pages = 1
        self.page_size = 20
        
        self.create_interface()
    
    def create_interface(self):
        """创建视频管理界面"""
        self.frame = ttk_bs.Frame(self.parent)
        
        # 标题和工具栏
        self.create_toolbar()
        
        # 搜索和筛选区域
        self.create_search_area()
        
        # 视频列表
        self.create_video_list()
        
        # 分页控件
        self.create_pagination()
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar_frame = ttk_bs.Frame(self.frame)
        toolbar_frame.pack(fill=X, padx=20, pady=20)
        
        # 标题
        ttk_bs.Label(
            toolbar_frame,
            text="视频管理",
            font=("Arial", 18, "bold"),
            bootstyle="primary"
        ).pack(side=LEFT)
        
        # 右侧按钮组
        btn_frame = ttk_bs.Frame(toolbar_frame)
        btn_frame.pack(side=RIGHT)
        
        # 添加视频按钮
        add_btn = ttk_bs.Button(
            btn_frame,
            text="+ 添加视频",
            bootstyle="success",
            command=self.add_video
        )
        add_btn.pack(side=RIGHT, padx=5)
        
        # 批量操作按钮
        batch_btn = ttk_bs.Button(
            btn_frame,
            text="批量操作",
            bootstyle="warning",
            command=self.batch_operations
        )
        batch_btn.pack(side=RIGHT, padx=5)
        
        # 刷新按钮
        refresh_btn = ttk_bs.Button(
            btn_frame,
            text="刷新",
            bootstyle="outline-primary",
            command=self.refresh
        )
        refresh_btn.pack(side=RIGHT, padx=5)
    
    def create_search_area(self):
        """创建搜索区域"""
        search_frame = ttk_bs.LabelFrame(self.frame, text="搜索和筛选", padding=15)
        search_frame.pack(fill=X, padx=20, pady=(0, 20))
        
        # 第一行：搜索框和分类筛选
        row1 = ttk_bs.Frame(search_frame)
        row1.pack(fill=X, pady=(0, 10))
        
        # 搜索框
        ttk_bs.Label(row1, text="搜索:").pack(side=LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk_bs.Entry(row1, textvariable=self.search_var, width=30)
        search_entry.pack(side=LEFT, padx=(0, 20))
        
        # 分类筛选
        ttk_bs.Label(row1, text="分类:").pack(side=LEFT, padx=(0, 5))
        self.category_var = tk.StringVar()
        self.category_combo = ttk_bs.Combobox(row1, textvariable=self.category_var, width=20, state="readonly")
        self.category_combo.pack(side=LEFT, padx=(0, 20))
        
        # 搜索按钮
        search_btn = ttk_bs.Button(
            row1,
            text="搜索",
            bootstyle="primary",
            command=self.search_videos
        )
        search_btn.pack(side=LEFT, padx=5)
        
        # 清除按钮
        clear_btn = ttk_bs.Button(
            row1,
            text="清除",
            bootstyle="outline-secondary",
            command=self.clear_search
        )
        clear_btn.pack(side=LEFT, padx=5)
        
        # 第二行：状态筛选和排序
        row2 = ttk_bs.Frame(search_frame)
        row2.pack(fill=X)
        
        # 状态筛选
        ttk_bs.Label(row2, text="状态:").pack(side=LEFT, padx=(0, 5))
        self.status_var = tk.StringVar(value="all")
        status_combo = ttk_bs.Combobox(
            row2,
            textvariable=self.status_var,
            values=["all", "active", "inactive", "pending"],
            width=15,
            state="readonly"
        )
        status_combo.pack(side=LEFT, padx=(0, 20))
        
        # 排序方式
        ttk_bs.Label(row2, text="排序:").pack(side=LEFT, padx=(0, 5))
        self.sort_var = tk.StringVar(value="latest")
        sort_combo = ttk_bs.Combobox(
            row2,
            textvariable=self.sort_var,
            values=["latest", "oldest", "title", "views", "duration"],
            width=15,
            state="readonly"
        )
        sort_combo.pack(side=LEFT, padx=(0, 20))
        
        # 绑定回车键搜索
        search_entry.bind('<Return>', lambda e: self.search_videos())
    
    def create_video_list(self):
        """创建视频列表"""
        list_frame = ttk_bs.Frame(self.frame)
        list_frame.pack(fill=BOTH, expand=True, padx=20, pady=(0, 20))
        
        # 创建Treeview
        columns = ("id", "title", "category", "duration", "views", "status", "created_at")
        self.video_tree = ttk_bs.Treeview(
            list_frame,
            columns=columns,
            show="headings",
            height=15
        )
        
        # 设置列标题和宽度
        column_config = {
            "id": ("ID", 60),
            "title": ("标题", 300),
            "category": ("分类", 100),
            "duration": ("时长", 80),
            "views": ("观看次数", 100),
            "status": ("状态", 80),
            "created_at": ("创建时间", 150)
        }
        
        for col, (text, width) in column_config.items():
            self.video_tree.heading(col, text=text)
            self.video_tree.column(col, width=width)
        
        # 添加滚动条
        v_scrollbar = tk.Scrollbar(list_frame, orient="vertical", command=self.video_tree.yview)
        h_scrollbar = tk.Scrollbar(list_frame, orient="horizontal", command=self.video_tree.xview)
        self.video_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.video_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定双击事件
        self.video_tree.bind("<Double-1>", self.on_video_double_click)
        
        # 绑定右键菜单
        self.video_tree.bind("<Button-3>", self.show_context_menu)
        
        # 创建右键菜单
        self.context_menu = tk.Menu(self.video_tree, tearoff=0)
        self.context_menu.add_command(label="查看详情", command=self.view_video_details)
        self.context_menu.add_command(label="编辑", command=self.edit_video)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="删除", command=self.delete_video)
    
    def create_pagination(self):
        """创建分页控件"""
        pagination_frame = ttk_bs.Frame(self.frame)
        pagination_frame.pack(fill=X, padx=20, pady=(0, 20))
        
        # 左侧：显示信息
        info_frame = ttk_bs.Frame(pagination_frame)
        info_frame.pack(side=LEFT)
        
        self.info_label = ttk_bs.Label(info_frame, text="")
        self.info_label.pack()
        
        # 右侧：分页按钮
        nav_frame = ttk_bs.Frame(pagination_frame)
        nav_frame.pack(side=RIGHT)
        
        # 首页
        first_btn = ttk_bs.Button(
            nav_frame,
            text="首页",
            bootstyle="outline-primary",
            command=lambda: self.go_to_page(1)
        )
        first_btn.pack(side=LEFT, padx=2)
        
        # 上一页
        self.prev_btn = ttk_bs.Button(
            nav_frame,
            text="上一页",
            bootstyle="outline-primary",
            command=self.prev_page
        )
        self.prev_btn.pack(side=LEFT, padx=2)
        
        # 页码输入
        ttk_bs.Label(nav_frame, text="第").pack(side=LEFT, padx=(10, 2))
        self.page_var = tk.StringVar(value="1")
        page_entry = ttk_bs.Entry(nav_frame, textvariable=self.page_var, width=5)
        page_entry.pack(side=LEFT, padx=2)
        page_entry.bind('<Return>', lambda e: self.go_to_page(int(self.page_var.get() or 1)))
        
        self.total_pages_label = ttk_bs.Label(nav_frame, text="/ 1 页")
        self.total_pages_label.pack(side=LEFT, padx=2)
        
        # 下一页
        self.next_btn = ttk_bs.Button(
            nav_frame,
            text="下一页",
            bootstyle="outline-primary",
            command=self.next_page
        )
        self.next_btn.pack(side=LEFT, padx=2)
        
        # 末页
        last_btn = ttk_bs.Button(
            nav_frame,
            text="末页",
            bootstyle="outline-primary",
            command=lambda: self.go_to_page(self.total_pages)
        )
        last_btn.pack(side=LEFT, padx=2)
    
    def show(self):
        """显示框架"""
        if not self.is_visible:
            self.frame.pack(fill=BOTH, expand=True)
            self.is_visible = True
            self.load_categories()
            self.refresh()
    
    def hide(self):
        """隐藏框架"""
        if self.is_visible:
            self.frame.pack_forget()
            self.is_visible = False
    
    def load_categories(self):
        """加载分类列表"""
        def fetch_categories():
            try:
                result = api_client.get_categories(limit=100)
                if result.get("success"):
                    categories = result.get("data", {}).get("categories", [])
                    category_values = ["全部"] + [cat.get("name", "") for cat in categories]
                    self.parent.after(0, lambda: self.update_category_combo(category_values))
            except Exception as e:
                print(f"加载分类失败: {e}")
        
        threading.Thread(target=fetch_categories, daemon=True).start()
    
    def update_category_combo(self, categories):
        """更新分类下拉框"""
        self.category_combo['values'] = categories
        if categories:
            self.category_combo.set(categories[0])
    
    def refresh(self):
        """刷新视频列表"""
        if not self.is_visible:
            return
        
        self.load_videos()
    
    def load_videos(self):
        """加载视频数据"""
        def fetch_videos():
            try:
                # 构建查询参数
                search = self.search_var.get().strip()
                category = self.category_var.get()
                if category == "全部":
                    category = ""
                
                result = api_client.get_videos(
                    page=self.current_page,
                    limit=self.page_size,
                    search=search,
                    category=category
                )
                
                if result.get("success"):
                    data = result.get("data", {})
                    self.videos_data = data.get("videos", [])
                    pagination = data.get("pagination", {})
                    self.total_pages = pagination.get("total_pages", 1)
                    
                    self.parent.after(0, self.update_video_list)
                else:
                    self.main_app.set_status(f"加载视频失败: {result.get('message', '未知错误')}")
            except Exception as e:
                self.main_app.set_status(f"加载视频失败: {str(e)}")
        
        threading.Thread(target=fetch_videos, daemon=True).start()
    
    def update_video_list(self):
        """更新视频列表显示"""
        # 清除现有数据
        for item in self.video_tree.get_children():
            self.video_tree.delete(item)
        
        # 添加新数据
        for video in self.videos_data:
            # 格式化数据
            duration = self.format_duration(video.get("duration", 0))
            views = f"{video.get('views', 0):,}"
            created_at = self.format_datetime(video.get("created_at", ""))
            
            self.video_tree.insert("", "end", values=(
                video.get("id", ""),
                video.get("title", ""),
                video.get("category_name", ""),
                duration,
                views,
                video.get("status", ""),
                created_at
            ))
        
        # 更新分页信息
        self.update_pagination_info()
    
    def format_duration(self, seconds):
        """格式化时长"""
        if not seconds:
            return "未知"
        
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
    
    def format_datetime(self, dt_str):
        """格式化日期时间"""
        if not dt_str:
            return ""
        try:
            dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
            return dt.strftime("%Y-%m-%d %H:%M")
        except:
            return dt_str
    
    def update_pagination_info(self):
        """更新分页信息"""
        total_items = len(self.videos_data)
        start_item = (self.current_page - 1) * self.page_size + 1
        end_item = min(start_item + total_items - 1, self.current_page * self.page_size)
        
        self.info_label.config(text=f"显示 {start_item}-{end_item} 项")
        self.page_var.set(str(self.current_page))
        self.total_pages_label.config(text=f"/ {self.total_pages} 页")
        
        # 更新按钮状态
        self.prev_btn.config(state="normal" if self.current_page > 1 else "disabled")
        self.next_btn.config(state="normal" if self.current_page < self.total_pages else "disabled")
    
    def search_videos(self):
        """搜索视频"""
        self.current_page = 1
        self.load_videos()
    
    def clear_search(self):
        """清除搜索条件"""
        self.search_var.set("")
        self.category_var.set("全部")
        self.status_var.set("all")
        self.sort_var.set("latest")
        self.search_videos()
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.load_videos()
    
    def next_page(self):
        """下一页"""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.load_videos()
    
    def go_to_page(self, page):
        """跳转到指定页"""
        if 1 <= page <= self.total_pages:
            self.current_page = page
            self.load_videos()
    
    def on_video_double_click(self, event):
        """双击视频事件"""
        self.view_video_details()
    
    def show_context_menu(self, event):
        """显示右键菜单"""
        item = self.video_tree.selection()
        if item:
            self.context_menu.post(event.x_root, event.y_root)
    
    def view_video_details(self):
        """查看视频详情"""
        selection = self.video_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个视频")
            return
        
        item = self.video_tree.item(selection[0])
        video_id = item['values'][0]
        
        # TODO: 实现视频详情窗口
        messagebox.showinfo("提示", f"查看视频详情 ID: {video_id}")
    
    def edit_video(self):
        """编辑视频"""
        selection = self.video_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个视频")
            return
        
        item = self.video_tree.item(selection[0])
        video_id = item['values'][0]
        
        # TODO: 实现视频编辑窗口
        messagebox.showinfo("提示", f"编辑视频 ID: {video_id}")
    
    def delete_video(self):
        """删除视频"""
        selection = self.video_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个视频")
            return
        
        item = self.video_tree.item(selection[0])
        video_id = item['values'][0]
        video_title = item['values'][1]
        
        if messagebox.askyesno("确认删除", f"确定要删除视频 '{video_title}' 吗？\n此操作不可撤销！"):
            def do_delete():
                try:
                    result = api_client.delete_video(video_id)
                    if result.get("success"):
                        self.parent.after(0, lambda: [
                            self.main_app.set_status("视频删除成功"),
                            self.refresh()
                        ])
                    else:
                        self.parent.after(0, lambda: messagebox.showerror(
                            "删除失败", 
                            result.get('message', '未知错误')
                        ))
                except Exception as e:
                    self.parent.after(0, lambda: messagebox.showerror(
                        "删除失败", 
                        f"删除视频时发生错误: {str(e)}"
                    ))
            
            threading.Thread(target=do_delete, daemon=True).start()
    
    def add_video(self):
        """添加视频"""
        # TODO: 实现添加视频窗口
        messagebox.showinfo("提示", "添加视频功能开发中...")
    
    def batch_operations(self):
        """批量操作"""
        # TODO: 实现批量操作窗口
        messagebox.showinfo("提示", "批量操作功能开发中...")

# 采集源管理 API 文档

## 概述

采集源管理系统用于存储和管理视频采集接口配置，支持苹果CMS等多种采集源类型。

## 数据表结构

### collect_sources (采集接口配置表)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int(11) | 主键ID |
| name | varchar(100) | 资源库名称 |
| url | varchar(500) | 采集接口URL |
| type | enum | 接口类型：maccms/feifei/maxcms/other |
| description | text | 接口描述 |
| status | enum | 状态：active/inactive/testing/error |
| collect_config | json | 采集配置JSON |
| auto_collect | tinyint(1) | 是否自动采集 |
| collect_interval | int(11) | 采集间隔(分钟) |
| collect_images | tinyint(1) | 是否采集图片 |
| collect_categories | varchar(500) | 采集分类ID列表 |
| total_videos | int(11) | 总视频数量 |
| collected_videos | int(11) | 已采集视频数量 |
| last_collect_time | datetime | 最后采集时间 |
| last_check_time | datetime | 最后检测时间 |
| response_time | int(11) | 响应时间(毫秒) |
| success_rate | decimal(5,2) | 成功率(%) |
| last_error | text | 最后错误信息 |
| error_count | int(11) | 错误次数 |
| last_error_time | datetime | 最后错误时间 |

## API 接口

### 1. 获取采集源列表

```
GET /api/collect/sources
```

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)
- `status`: 状态筛选
- `type`: 类型筛选
- `search`: 搜索关键词
- `sortField`: 排序字段 (默认: created_at)
- `sortDirection`: 排序方向 (默认: DESC)

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "FQ资源库",
      "url": "https://fqzy.me/api.php/provide/vod/",
      "type": "maccms",
      "status": "active",
      "totalVideos": 0,
      "collectedVideos": 0
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 2,
    "totalPages": 1
  }
}
```

### 2. 获取单个采集源

```
GET /api/collect/sources/:id
```

### 3. 创建采集源

```
POST /api/collect/sources
```

**请求体:**
```json
{
  "name": "新资源库",
  "url": "https://example.com/api.php/provide/vod/",
  "type": "maccms",
  "description": "资源库描述",
  "status": "active",
  "collectConfig": {
    "timeout": 30,
    "retryCount": 3,
    "pageSize": 20
  },
  "autoCollect": 0,
  "collectInterval": 60,
  "collectImages": 1
}
```

### 4. 更新采集源

```
PUT /api/collect/sources/:id
```

### 5. 删除采集源

```
DELETE /api/collect/sources/:id
```

### 6. 测试采集源连接

```
POST /api/collect/sources/:id/test
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "status": "online",
    "responseTime": 1250,
    "dataFormat": "valid",
    "totalVideos": 15420,
    "message": "连接测试成功"
  }
}
```

### 7. 获取采集源统计

```
GET /api/collect/sources/:id/stats
```

## 使用步骤

### 1. 执行数据库迁移

```bash
cd backend
node scripts/migrate-collect-sources.js
```

### 2. 启动后端服务

```bash
npm start
```

### 3. 测试API

```bash
# 获取采集源列表
curl http://localhost:3001/api/collect/sources

# 测试连接
curl -X POST http://localhost:3001/api/collect/sources/1/test
```

## 采集配置说明

`collect_config` 字段存储JSON格式的采集配置：

```json
{
  "timeout": 30,           // 请求超时时间(秒)
  "retryCount": 3,         // 重试次数
  "pageSize": 20,          // 每页数量
  "maxPages": 100,         // 最大页数
  "collectDescription": true,  // 是否采集描述
  "collectActors": true,   // 是否采集演员
  "collectDirector": true, // 是否采集导演
  "skipExisting": true     // 是否跳过已存在的视频
}
```

## 状态说明

- `active`: 正常可用
- `inactive`: 已禁用
- `testing`: 测试中
- `error`: 连接错误

## 注意事项

1. 采集源URL必须是有效的HTTP/HTTPS地址
2. 采集源名称必须唯一
3. 删除采集源会同时删除相关的采集日志和任务
4. 自动采集功能需要配合定时任务使用
5. 建议定期测试采集源连接状态

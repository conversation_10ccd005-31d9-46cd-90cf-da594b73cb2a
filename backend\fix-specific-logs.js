// 加载环境变量
require('dotenv').config();

const CollectLog = require('./src/models/CollectLog');
const CollectTask = require('./src/models/CollectTask');
const db = require('./src/config/database');

async function fixSpecificLogs() {
  console.log('=== 修复特定日志状态 ===\n');

  try {
    // 初始化数据库连接
    console.log('初始化数据库连接...');
    await db.connect();
    console.log('✅ 数据库连接成功\n');

    // 处理日志18 - 有采集结果，应该是成功
    console.log('1. 处理日志18（有采集结果）...');
    try {
      const log18 = await CollectLog.findById(18);
      if (log18) {
        console.log(`日志18详情:`);
        console.log(`- 状态: ${log18.status}`);
        console.log(`- 类型: ${log18.type}`);
        console.log(`- 采集统计: 发现${log18.totalFound || 0}, 采集${log18.totalCollected || 0}, 跳过${log18.totalSkipped || 0}, 失败${log18.totalFailed || 0}`);
        
        if (log18.status === 'running' && (log18.totalCollected || 0) > 0) {
          const startTime = new Date(log18.startTime);
          const endTime = new Date();
          const duration = Math.floor((endTime - startTime) / 1000);
          
          await CollectLog.update(18, {
            status: 'success',
            endTime: endTime,
            duration: duration
          });
          
          console.log(`✅ 日志18更新为成功状态`);
          console.log(`   持续时间: ${Math.floor(duration / 60)}分${duration % 60}秒`);
        } else {
          console.log(`ℹ️ 日志18无需更新`);
        }
      } else {
        console.log(`❌ 日志18不存在`);
      }
    } catch (error) {
      console.log(`❌ 处理日志18失败:`, error.message);
    }

    // 处理日志17 - 无采集结果，应该是失败
    console.log('\n2. 处理日志17（无采集结果）...');
    try {
      const log17 = await CollectLog.findById(17);
      if (log17) {
        console.log(`日志17详情:`);
        console.log(`- 状态: ${log17.status}`);
        console.log(`- 类型: ${log17.type}`);
        console.log(`- 采集统计: 发现${log17.totalFound || 0}, 采集${log17.totalCollected || 0}, 跳过${log17.totalSkipped || 0}, 失败${log17.totalFailed || 0}`);
        
        if (log17.status === 'running' && (log17.totalCollected || 0) === 0) {
          const startTime = new Date(log17.startTime);
          const endTime = new Date();
          const duration = Math.floor((endTime - startTime) / 1000);
          
          await CollectLog.update(17, {
            status: 'failed',
            endTime: endTime,
            duration: duration,
            errorMessage: '任务异常终止，未采集到任何视频'
          });
          
          console.log(`✅ 日志17更新为失败状态`);
          console.log(`   持续时间: ${Math.floor(duration / 60)}分${duration % 60}秒`);
        } else {
          console.log(`ℹ️ 日志17无需更新`);
        }
      } else {
        console.log(`❌ 日志17不存在`);
      }
    } catch (error) {
      console.log(`❌ 处理日志17失败:`, error.message);
    }

    // 处理任务13的状态
    console.log('\n3. 处理任务13的状态...');
    try {
      const task13 = await CollectTask.findById(13);
      if (task13) {
        console.log(`任务13详情:`);
        console.log(`- 状态: ${task13.status}`);
        console.log(`- 运行次数: ${task13.runCount}`);
        console.log(`- 成功次数: ${task13.successCount}`);
        console.log(`- 失败次数: ${task13.failCount}`);
        
        if (task13.status === 'running') {
          await CollectTask.update(13, {
            status: 'active', // 任务完成后设置为active
            successCount: (task13.successCount || 0) + 30, // 日志18采集了30个
            runCount: (task13.runCount || 0) + 1
          });
          
          console.log(`✅ 任务13更新为完成状态`);
          console.log(`   成功采集: ${(task13.successCount || 0) + 30}个视频`);
        } else {
          console.log(`ℹ️ 任务13无需更新`);
        }
      } else {
        console.log(`❌ 任务13不存在`);
      }
    } catch (error) {
      console.log(`❌ 处理任务13失败:`, error.message);
    }

    // 验证修复结果
    console.log('\n4. 验证修复结果...');
    const remainingRunningLogs = await CollectLog.findAll({
      status: 'running',
      limit: 10
    });
    
    console.log(`修复后剩余运行中日志数: ${remainingRunningLogs.logs.length}`);
    
    if (remainingRunningLogs.logs.length > 0) {
      console.log('剩余的运行中日志:');
      remainingRunningLogs.logs.forEach(log => {
        const startTime = new Date(log.startTime);
        const now = new Date();
        const runningMinutes = Math.floor((now - startTime) / (1000 * 60));
        console.log(`- 日志 ${log.id}: ${log.sourceName}, 运行 ${runningMinutes}分钟, 采集${log.totalCollected || 0}个`);
      });
    } else {
      console.log('✅ 所有日志状态都已正确更新');
    }

    console.log('\n=== 修复完成 ===');
    console.log('🎯 现在可以访问日志页面查看正确的状态了！');
    console.log('📍 URL: http://localhost:3000/admin/collect/logs?taskId=13');

  } catch (error) {
    console.error('修复失败:', error);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  fixSpecificLogs();
}

module.exports = fixSpecificLogs;

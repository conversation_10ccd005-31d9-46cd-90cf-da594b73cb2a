// 修复tags提取逻辑的脚本
const fs = require('fs');
const path = require('path');

function fixTagsExtraction() {
  console.log('=== 修复Tags提取逻辑 ===\n');

  // 新的extractTags函数代码
  const newExtractTagsFunction = `  // 提取标签
  static extractTags(videoData) {
    // 从 vod_class 字段获取标签，该字段包含逗号分隔的标签
    if (!videoData.vod_class) {
      return [];
    }
    
    // 将逗号分隔的字符串转换为数组，并清理空白字符
    return videoData.vod_class
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
  }`;

  const newExtractTagsFunctionForProcessor = `  // 提取标签
  extractTags(videoData) {
    // 从 vod_class 字段获取标签，该字段包含逗号分隔的标签
    if (!videoData.vod_class) {
      return [];
    }
    
    // 将逗号分隔的字符串转换为数组，并清理空白字符
    return videoData.vod_class
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
  }`;

  // 测试新的提取逻辑
  console.log('1. 测试新的tags提取逻辑:');
  
  const mockVideoData = {
    vod_name: '测试视频',
    vod_class: '美尻,肛交,脚交,美腿'
  };

  console.log('输入数据:');
  console.log('- vod_class:', mockVideoData.vod_class);

  // 模拟新的提取函数
  function extractTags(videoData) {
    if (!videoData.vod_class) {
      return [];
    }
    
    return videoData.vod_class
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
  }

  const result = extractTags(mockVideoData);
  console.log('\n输出结果:');
  console.log('- 类型:', typeof result);
  console.log('- 是否为数组:', Array.isArray(result));
  console.log('- 内容:', result);
  console.log('- JSON格式:', JSON.stringify(result));

  // 测试边界情况
  console.log('\n2. 测试边界情况:');
  
  const testCases = [
    { name: '空字符串', data: { vod_class: '' } },
    { name: '单个标签', data: { vod_class: '美尻' } },
    { name: '带空格的标签', data: { vod_class: ' 美尻 , 肛交 , 脚交 ' } },
    { name: '空标签', data: { vod_class: '美尻,,脚交' } },
    { name: '没有vod_class字段', data: { vod_name: '测试' } }
  ];

  testCases.forEach(testCase => {
    const result = extractTags(testCase.data);
    console.log(`- ${testCase.name}: ${JSON.stringify(result)}`);
  });

  console.log('\n3. 数据库存储格式:');
  const finalResult = extractTags(mockVideoData);
  console.log('- 原始数据:', mockVideoData.vod_class);
  console.log('- 处理后数组:', finalResult);
  console.log('- 数据库存储格式:', JSON.stringify(finalResult));
  console.log('- 预期数据库值: ["美尻", "肛交", "脚交", "美腿"]');

  console.log('\n=== 修复说明 ===');
  console.log('✅ 修改前: 从多个字段组合生成tags');
  console.log('✅ 修改后: 直接从vod_class字段获取tags');
  console.log('✅ 格式: 逗号分隔字符串 → JSON数组');
  console.log('✅ 示例: "美尻,肛交,脚交,美腿" → ["美尻", "肛交", "脚交", "美腿"]');

  console.log('\n需要手动修改的文件:');
  console.log('1. backend/src/controllers/AdminCollectController.js');
  console.log('2. backend/src/services/CollectProcessor.js');
  
  console.log('\n新的extractTags函数代码:');
  console.log('--- AdminCollectController.js ---');
  console.log(newExtractTagsFunction);
  console.log('\n--- CollectProcessor.js ---');
  console.log(newExtractTagsFunctionForProcessor);
}

// 如果直接运行此文件
if (require.main === module) {
  fixTagsExtraction();
}

module.exports = fixTagsExtraction;

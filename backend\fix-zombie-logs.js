// 加载环境变量
require('dotenv').config();

const CollectLog = require('./src/models/CollectLog');
const logger = require('./src/utils/logger');
const db = require('./src/config/database');

async function fixZombieLogs() {
  console.log('=== 修复僵尸日志状态 ===\n');

  try {
    // 初始化数据库连接
    console.log('初始化数据库连接...');
    await db.connect();
    console.log('✅ 数据库连接成功\n');
    // 1. 查找所有运行中的日志
    console.log('1. 查找所有运行中的日志...');
    const runningLogs = await CollectLog.findAll({
      status: 'running',
      limit: 50
    });
    
    console.log(`发现 ${runningLogs.logs.length} 个运行中的日志`);
    
    if (runningLogs.logs.length === 0) {
      console.log('✅ 没有发现运行中的日志，无需修复');
      return;
    }

    // 2. 分析每个日志的状态
    const zombieLogs = [];
    const recentLogs = [];
    
    console.log('\n2. 分析日志状态...');
    
    for (const log of runningLogs.logs) {
      const startTime = new Date(log.startTime);
      const now = new Date();
      const runningMinutes = Math.floor((now - startTime) / (1000 * 60));
      
      console.log(`\n日志 ${log.id}:`);
      console.log(`- 采集源: ${log.sourceId} (${log.sourceName})`);
      console.log(`- 类型: ${log.type}`);
      console.log(`- 开始时间: ${log.startTime}`);
      console.log(`- 已运行: ${runningMinutes}分钟`);
      console.log(`- 采集统计: 发现${log.totalFound || 0}, 采集${log.totalCollected || 0}, 跳过${log.totalSkipped || 0}, 失败${log.totalFailed || 0}`);
      
      // 如果运行超过10分钟，认为是僵尸日志
      if (runningMinutes > 10) {
        console.log(`  🧟 僵尸日志（运行超过10分钟）`);
        zombieLogs.push(log);
      } else {
        console.log(`  ✅ 最近的日志（可能仍在运行）`);
        recentLogs.push(log);
      }
    }

    // 3. 修复僵尸日志
    if (zombieLogs.length > 0) {
      console.log(`\n3. 修复 ${zombieLogs.length} 个僵尸日志...`);
      
      for (const log of zombieLogs) {
        try {
          const startTime = new Date(log.startTime);
          const endTime = new Date();
          const duration = Math.floor((endTime - startTime) / 1000);
          
          // 判断状态：如果有采集到视频，认为是成功；否则认为是失败
          const hasCollected = (log.totalCollected || 0) > 0;
          const newStatus = hasCollected ? 'success' : 'failed';
          const errorMessage = hasCollected ? null : '任务异常终止，未正常完成';
          
          await CollectLog.update(log.id, {
            status: newStatus,
            endTime: endTime,
            duration: duration,
            errorMessage: errorMessage
          });
          
          console.log(`✅ 修复日志 ${log.id}: ${log.status} -> ${newStatus}`);
          console.log(`   持续时间: ${Math.floor(duration / 60)}分${duration % 60}秒`);
          
        } catch (error) {
          console.log(`❌ 修复日志 ${log.id} 失败:`, error.message);
        }
      }
      
      console.log(`\n✅ 成功修复 ${zombieLogs.length} 个僵尸日志`);
    } else {
      console.log('\n✅ 没有发现僵尸日志');
    }

    // 4. 显示最近的日志状态
    if (recentLogs.length > 0) {
      console.log(`\n4. 最近的 ${recentLogs.length} 个日志仍在运行中:`);
      recentLogs.forEach(log => {
        const startTime = new Date(log.startTime);
        const now = new Date();
        const runningMinutes = Math.floor((now - startTime) / (1000 * 60));
        console.log(`- 日志 ${log.id}: ${log.sourceName}, 运行 ${runningMinutes}分钟`);
      });
      console.log('这些日志可能仍在正常执行中，暂不修复');
    }

    // 5. 验证修复结果
    console.log('\n5. 验证修复结果...');
    const remainingRunningLogs = await CollectLog.findAll({
      status: 'running',
      limit: 10
    });
    
    console.log(`修复后剩余运行中日志数: ${remainingRunningLogs.logs.length}`);
    
    if (remainingRunningLogs.logs.length > 0) {
      console.log('剩余的运行中日志:');
      remainingRunningLogs.logs.forEach(log => {
        const startTime = new Date(log.startTime);
        const now = new Date();
        const runningMinutes = Math.floor((now - startTime) / (1000 * 60));
        console.log(`- 日志 ${log.id}: ${log.sourceName}, 运行 ${runningMinutes}分钟`);
      });
    }

    console.log('\n=== 修复完成 ===');
    console.log('🎯 现在可以重新查看日志页面，状态应该正确显示了');

  } catch (error) {
    console.error('修复失败:', error);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  fixZombieLogs();
}

module.exports = fixZombieLogs;

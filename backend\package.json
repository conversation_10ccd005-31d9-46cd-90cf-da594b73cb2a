{"name": "91jspg-backend", "version": "1.0.0", "description": "91JSPG.COM 后端API服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "migrate": "node src/scripts/migrate.js", "seed": "node src/scripts/seed.js"}, "keywords": ["jav", "video", "api", "express", "nodejs"], "author": "91JSPG Team", "license": "MIT", "dependencies": {"axios": "^1.11.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "nodemailer": "^6.9.7", "pg": "^8.11.3", "redis": "^4.6.11", "sharp": "^0.33.1", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/node": "^20.10.5", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}
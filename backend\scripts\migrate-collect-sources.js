// 加载环境变量
require('dotenv').config();

const fs = require('fs');
const path = require('path');
const db = require('../src/config/database');
const logger = require('../src/utils/logger');

async function runMigration() {
  try {
    console.log('开始执行采集源数据表迁移...');

    // 连接数据库
    await db.connect();

    // 读取SQL文件
    const sqlFile = path.join(__dirname, '../sql/create_collect_sources_table.sql');
    const sqlContent = fs.readFileSync(sqlFile, 'utf8');

    // 分割SQL语句（以分号分隔）
    const sqlStatements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    console.log(`找到 ${sqlStatements.length} 条SQL语句`);

    // 执行每条SQL语句
    for (let i = 0; i < sqlStatements.length; i++) {
      const statement = sqlStatements[i];

      if (statement.toLowerCase().includes('create table')) {
        const tableName = statement.match(/create table.*?`(\w+)`/i)?.[1];
        console.log(`创建表: ${tableName}`);
      } else if (statement.toLowerCase().includes('insert into')) {
        const tableName = statement.match(/insert into.*?`(\w+)`/i)?.[1];
        console.log(`插入数据到表: ${tableName}`);
      }

      try {
        await db.query(statement);
        console.log(`✓ 执行成功: 语句 ${i + 1}`);
      } catch (error) {
        if (error.code === 'ER_TABLE_EXISTS_ERROR') {
          console.log(`⚠ 表已存在，跳过创建`);
        } else if (error.code === 'ER_DUP_ENTRY') {
          console.log(`⚠ 数据已存在，跳过插入`);
        } else {
          console.error(`✗ 执行失败: 语句 ${i + 1}`);
          console.error('错误:', error.message);
          throw error;
        }
      }
    }
    
    console.log('\n✓ 采集源数据表迁移完成！');
    
    // 验证表是否创建成功
    console.log('\n验证表结构...');
    
    const tables = ['collect_sources', 'collect_logs', 'collect_tasks'];
    
    for (const tableName of tables) {
      try {
        const result = await db.query(`DESCRIBE ${tableName}`);
        console.log(`✓ 表 ${tableName} 创建成功，包含 ${result.rows.length} 个字段`);
      } catch (error) {
        console.error(`✗ 表 ${tableName} 验证失败:`, error.message);
      }
    }

    // 检查默认数据
    console.log('\n检查默认数据...');
    try {
      const result = await db.query('SELECT COUNT(*) as count FROM collect_sources');
      console.log(`✓ collect_sources 表包含 ${result.rows[0].count} 条记录`);

      if (result.rows[0].count > 0) {
        const sourcesResult = await db.query('SELECT id, name, url, status FROM collect_sources');
        console.log('默认采集源:');
        sourcesResult.rows.forEach(source => {
          console.log(`  - ID: ${source.id}, 名称: ${source.name}, 状态: ${source.status}`);
        });
      }
    } catch (error) {
      console.error('检查默认数据失败:', error.message);
    }
    
    console.log('\n🎉 迁移完成！');
    
  } catch (error) {
    console.error('迁移失败:', error);
    logger.error('采集源数据表迁移失败:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    if (db && db.disconnect) {
      await db.disconnect();
    }
    process.exit(0);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runMigration();
}

module.exports = runMigration;

-- 采集接口数据表
-- 用于存储管理员配置的采集接口信息

CREATE TABLE IF NOT EXISTS `collect_sources` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '资源库名称',
  `url` varchar(500) NOT NULL COMMENT '采集接口URL',
  `type` enum('maccms','feifei','maxcms','other') NOT NULL DEFAULT 'maccms' COMMENT '接口类型',
  `description` text COMMENT '接口描述',
  `status` enum('active','inactive','testing','error') NOT NULL DEFAULT 'active' COMMENT '状态：active=正常,inactive=禁用,testing=测试中,error=错误',
  
  -- 采集配置
  `collect_config` json COMMENT '采集配置JSON',
  `auto_collect` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否自动采集：0=否,1=是',
  `collect_interval` int(11) NOT NULL DEFAULT 60 COMMENT '采集间隔(分钟)',
  `collect_images` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否采集图片：0=否,1=是',
  `collect_categories` varchar(500) COMMENT '采集分类ID列表，逗号分隔',
  
  -- 统计信息
  `total_videos` int(11) NOT NULL DEFAULT 0 COMMENT '总视频数量',
  `collected_videos` int(11) NOT NULL DEFAULT 0 COMMENT '已采集视频数量',
  `last_collect_time` datetime COMMENT '最后采集时间',
  `last_check_time` datetime COMMENT '最后检测时间',
  `response_time` int(11) COMMENT '响应时间(毫秒)',
  `success_rate` decimal(5,2) DEFAULT 0.00 COMMENT '成功率(%)',
  
  -- 错误信息
  `last_error` text COMMENT '最后错误信息',
  `error_count` int(11) NOT NULL DEFAULT 0 COMMENT '错误次数',
  `last_error_time` datetime COMMENT '最后错误时间',
  
  -- 系统字段
  `created_by` int(11) COMMENT '创建者ID',
  `updated_by` int(11) COMMENT '更新者ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_status` (`status`),
  KEY `idx_type` (`type`),
  KEY `idx_auto_collect` (`auto_collect`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_last_collect_time` (`last_collect_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采集接口配置表';

-- 插入默认的采集接口数据
INSERT INTO `collect_sources` (
  `name`, 
  `url`, 
  `type`, 
  `description`, 
  `status`,
  `collect_config`,
  `auto_collect`,
  `collect_interval`,
  `collect_images`,
  `total_videos`,
  `collected_videos`
) VALUES 
(
  'FQ资源库',
  'https://fqzy.me/api.php/provide/vod/',
  'maccms',
  '高质量影视资源采集接口，支持多种分类',
  'active',
  JSON_OBJECT(
    'timeout', 30,
    'retryCount', 3,
    'pageSize', 20,
    'maxPages', 100,
    'collectDescription', true,
    'collectActors', true,
    'collectDirector', true,
    'skipExisting', true
  ),
  0,
  60,
  1,
  0,
  0
),
(
  '星巴资源库',
  'https://xingba222.com/api.php/provide/vod/',
  'maccms',
  '星巴影视资源采集接口，内容丰富',
  'active',
  JSON_OBJECT(
    'timeout', 30,
    'retryCount', 3,
    'pageSize', 20,
    'maxPages', 100,
    'collectDescription', true,
    'collectActors', true,
    'collectDirector', true,
    'skipExisting', true
  ),
  0,
  60,
  1,
  0,
  0
);

-- 创建采集日志表
CREATE TABLE IF NOT EXISTS `collect_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `source_id` int(11) NOT NULL COMMENT '采集源ID',
  `source_name` varchar(100) NOT NULL COMMENT '采集源名称',
  `type` enum('manual','auto','test') NOT NULL DEFAULT 'manual' COMMENT '采集类型：manual=手动,auto=自动,test=测试',
  `status` enum('running','success','failed','stopped') NOT NULL DEFAULT 'running' COMMENT '状态',
  
  -- 采集参数
  `collect_params` json COMMENT '采集参数JSON',
  `category_id` int(11) COMMENT '采集分类ID',
  `page_start` int(11) DEFAULT 1 COMMENT '开始页码',
  `page_end` int(11) COMMENT '结束页码',
  
  -- 采集结果
  `total_found` int(11) DEFAULT 0 COMMENT '发现总数',
  `total_collected` int(11) DEFAULT 0 COMMENT '采集成功数',
  `total_skipped` int(11) DEFAULT 0 COMMENT '跳过数量',
  `total_failed` int(11) DEFAULT 0 COMMENT '失败数量',
  
  -- 时间信息
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  `duration` int(11) COMMENT '耗时(秒)',
  
  -- 错误信息
  `error_message` text COMMENT '错误信息',
  `error_details` json COMMENT '错误详情JSON',
  
  -- 系统字段
  `created_by` int(11) COMMENT '创建者ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_source_id` (`source_id`),
  KEY `idx_status` (`status`),
  KEY `idx_type` (`type`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_created_at` (`created_at`),
  
  CONSTRAINT `fk_collect_logs_source` FOREIGN KEY (`source_id`) REFERENCES `collect_sources` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采集日志表';

-- 创建采集任务表（用于定时任务）
CREATE TABLE IF NOT EXISTS `collect_tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `source_id` int(11) NOT NULL COMMENT '采集源ID',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `task_type` enum('once','daily','weekly','monthly','custom') NOT NULL DEFAULT 'daily' COMMENT '任务类型',
  `cron_expression` varchar(100) COMMENT 'Cron表达式',
  `status` enum('active','inactive','running','paused') NOT NULL DEFAULT 'active' COMMENT '任务状态',
  
  -- 任务配置
  `task_config` json COMMENT '任务配置JSON',
  `collect_categories` varchar(500) COMMENT '采集分类',
  `max_pages` int(11) DEFAULT 10 COMMENT '最大页数',
  `collect_images` tinyint(1) DEFAULT 1 COMMENT '是否采集图片',
  
  -- 执行信息
  `last_run_time` datetime COMMENT '最后执行时间',
  `next_run_time` datetime COMMENT '下次执行时间',
  `run_count` int(11) DEFAULT 0 COMMENT '执行次数',
  `success_count` int(11) DEFAULT 0 COMMENT '成功次数',
  `fail_count` int(11) DEFAULT 0 COMMENT '失败次数',
  
  -- 系统字段
  `created_by` int(11) COMMENT '创建者ID',
  `updated_by` int(11) COMMENT '更新者ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_source_id` (`source_id`),
  KEY `idx_status` (`status`),
  KEY `idx_task_type` (`task_type`),
  KEY `idx_next_run_time` (`next_run_time`),
  
  CONSTRAINT `fk_collect_tasks_source` FOREIGN KEY (`source_id`) REFERENCES `collect_sources` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采集任务表';

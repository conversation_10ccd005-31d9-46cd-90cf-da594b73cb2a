const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const cookieParser = require('cookie-parser');
require('dotenv').config();

const logger = require('./utils/logger');

// 全局错误处理 - 处理未捕获的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  // 忽略网络连接中断错误，这些是正常的断开行为
  if (reason && (reason.code === 'ECONNABORTED' || reason.code === 'ECONNRESET' || reason.code === 'EPIPE')) {
    logger.debug('Network connection aborted (normal behavior):', reason.code);
    return;
  }

  logger.error('Unhandled Promise Rejection:', {
    reason: reason,
    promise: promise,
    stack: reason?.stack
  });
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  // 不要退出进程，让应用继续运行
});
const database = require('./config/database');
const redis = require('./config/redis');

// 路由导入
const apiRoutes = require('./routes/api');
const uploadRoutes = require('./routes/upload');
const adminRoutes = require('./routes/admin');

// 中间件导入
const errorHandler = require('./middleware/errorHandler');
const notFound = require('./middleware/notFound');

const app = express();



// 应用启动日志
logger.info('=== 91JSPG.COM API 服务器启动 ===');
logger.info('启动时间:', new Date().toISOString());
logger.info('Node.js 版本:', process.version);
logger.info('环境变量:', {
  NODE_ENV: process.env.NODE_ENV,
  PORT: process.env.PORT,
  HOST: process.env.HOST
});

// 基础中间件
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

app.use(compression());

// CORS配置
const corsOptions = {
  origin: true, // 允许所有来源
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

// 调试信息：显示当前CORS配置
console.log('🌐 CORS Configuration:');
console.log('Environment:', process.env.NODE_ENV);
console.log('CORS_ORIGIN from env:', process.env.CORS_ORIGIN);
const allowedOrigins = process.env.CORS_ORIGIN
  ? process.env.CORS_ORIGIN.split(',').map(url => url.trim())
  : ['http://localhost:3000'];
console.log('Allowed origins:', allowedOrigins);

app.use(cors(corsOptions));

// 日志中间件
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined', {
    stream: {
      write: (message) => logger.info(message.trim())
    }
  }));
}

// 请求解析
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// 静态文件服务
app.use('/uploads', express.static('uploads'));
app.use('/public', express.static('public'));



// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: require('../package.json').version
  });
});

// API路由
app.use('/api', apiRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/admin', adminRoutes);

// 根路径
app.get('/', (req, res) => {
  res.json({
    message: '91JSPG.COM API Server',
    version: require('../package.json').version,
    documentation: '/api/docs',
    status: 'running'
  });
});

// 错误处理中间件
app.use(notFound);
app.use(errorHandler);

// 数据库连接
logger.info('🔄 初始化数据库连接...');
database.connect()
  .then(() => {
    logger.info('🎉 数据库连接初始化完成');
  })
  .catch((error) => {
    logger.error('❌ 数据库连接初始化失败:', {
      message: error.message,
      code: error.code || 'N/A',
      stack: error.stack
    });
    logger.error('🛑 由于数据库连接失败，服务器将退出');
    process.exit(1);
  });

// Redis连接
logger.info('🔄 初始化 Redis 连接...');
redis.connect()
  .then(() => {
    logger.info('🎉 Redis 连接初始化完成');
  })
  .catch((error) => {
    logger.error('❌ Redis 连接初始化失败:', {
      message: error.message,
      code: error.code || 'N/A',
      stack: error.stack
    });
    logger.warn('⚠️ Redis 连接失败，但服务器将继续运行 (使用内存缓存)');
  });

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  database.disconnect();
  redis.disconnect();
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  database.disconnect();
  redis.disconnect();
  process.exit(0);
});

// 未捕获的异常处理
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

const PORT = process.env.PORT || 3001;
const HOST = process.env.HOST || 'localhost';

logger.info('🔄 启动 HTTP 服务器...');
logger.info('服务器配置:', {
  host: HOST,
  port: PORT,
  environment: process.env.NODE_ENV
});

const server = app.listen(PORT, HOST, () => {
  logger.info('🎉 HTTP 服务器启动成功!');
  logger.info('📍 服务器地址: http://' + HOST + ':' + PORT);
  logger.info('🌍 环境: ' + process.env.NODE_ENV);
  logger.info('📚 API 文档: http://' + HOST + ':' + PORT + '/api');
  logger.info('❤️ 健康检查: http://' + HOST + ':' + PORT + '/health');
  logger.info('=== 服务器启动完成，等待请求... ===');
});

server.on('error', (error) => {
  logger.error('❌ HTTP 服务器启动失败:', {
    message: error.message,
    code: error.code || 'N/A',
    port: PORT,
    host: HOST
  });

  if (error.code === 'EADDRINUSE') {
    logger.error('🔍 端口被占用 - 请检查:');
    logger.error('  1. 端口 ' + PORT + ' 是否已被其他程序使用');
    logger.error('  2. 尝试使用其他端口: PORT=3002 node src/app.js');
  }

  process.exit(1);
});

module.exports = app;

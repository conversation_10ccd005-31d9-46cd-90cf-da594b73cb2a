const mysql = require('mysql2/promise');
const logger = require('../utils/logger');

class Database {
  constructor() {
    this.pool = null;
  }

  async connect() {
    try {
      // 详细记录数据库配置检查
      logger.info('=== 数据库连接初始化 ===');
      logger.info('数据库配置检查:', {
        DB_HOST: process.env.DB_HOST || 'undefined',
        DB_PORT: process.env.DB_PORT || 'undefined',
        DB_NAME: process.env.DB_NAME || 'undefined',
        DB_USER: process.env.DB_USER || 'undefined',
        DB_PASSWORD: process.env.DB_PASSWORD ? '***已设置***' : 'undefined',
        DB_SSL: process.env.DB_SSL || 'undefined'
      });

      // 检查必要的数据库配置
      if (!process.env.DB_HOST || !process.env.DB_NAME || !process.env.DB_USER) {
        logger.error('❌ 数据库配置不完整，缺少必要参数');
        logger.error('缺少的配置:', {
          DB_HOST: !process.env.DB_HOST ? '缺少' : '已设置',
          DB_NAME: !process.env.DB_NAME ? '缺少' : '已设置',
          DB_USER: !process.env.DB_USER ? '缺少' : '已设置'
        });
        throw new Error('数据库配置不完整，请检查环境变量');
      }

      logger.info('🔄 创建数据库连接池...');
      const poolConfig = {
        host: process.env.DB_HOST,
        port: parseInt(process.env.DB_PORT) || 3306,
        database: process.env.DB_NAME,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        ssl: process.env.DB_SSL === 'true',
        connectionLimit: 20, // 最大连接数
        waitForConnections: true,
        queueLimit: 0,
        multipleStatements: true
      };

      logger.info('连接池配置:', {
        host: poolConfig.host,
        port: poolConfig.port,
        database: poolConfig.database,
        user: poolConfig.user,
        ssl: poolConfig.ssl,
        connectionLimit: poolConfig.connectionLimit
      });

      this.pool = mysql.createPool(poolConfig);
      logger.info('✅ 数据库连接池创建成功');

      // 测试连接
      logger.info('🔄 测试数据库连接...');
      const connection = await this.pool.getConnection();
      logger.info('✅ 获取数据库连接成功');

      const [rows] = await connection.query('SELECT NOW() as current_datetime, VERSION() as mysql_version');
      logger.info('✅ 数据库查询测试成功:', {
        current_datetime: rows[0].current_datetime,
        mysql_version: rows[0].mysql_version
      });

      connection.release();
      logger.info('✅ 数据库连接已释放');

      logger.info('🎉 MySQL 数据库连接建立成功');
      return this.pool;
    } catch (error) {
      logger.error('❌ 数据库连接失败，详细错误信息:', {
        errorType: error.constructor.name,
        message: error.message,
        code: error.code || 'N/A',
        errno: error.errno || 'N/A',
        sqlState: error.sqlState || 'N/A',
        stack: error.stack
      });

      if (error.code === 'ECONNREFUSED') {
        logger.error('🔍 连接被拒绝 - 请检查:');
        logger.error('  1. MySQL 服务是否正在运行');
        logger.error('  2. 端口号是否正确 (当前: ' + (process.env.DB_PORT || 3306) + ')');
        logger.error('  3. 主机地址是否正确 (当前: ' + process.env.DB_HOST + ')');
      } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
        logger.error('🔍 访问被拒绝 - 请检查:');
        logger.error('  1. 用户名是否正确 (当前: ' + process.env.DB_USER + ')');
        logger.error('  2. 密码是否正确');
        logger.error('  3. 用户是否有访问数据库的权限');
      } else if (error.code === 'ER_BAD_DB_ERROR') {
        logger.error('🔍 数据库不存在 - 请检查:');
        logger.error('  1. 数据库名称是否正确 (当前: ' + process.env.DB_NAME + ')');
        logger.error('  2. 数据库是否已创建');
      }

      logger.error('❌ 数据库连接失败，服务器无法启动');
      throw error;
    }
  }

  async disconnect() {
    if (this.pool) {
      await this.pool.end();
      logger.info('Database connection closed');
    }
  }

  async query(sql, params = []) {
    const startTime = Date.now();

    try {
      logger.debug('🔄 执行数据库查询:', {
        sql: sql.substring(0, 200) + (sql.length > 200 ? '...' : ''),
        params: params.length > 0 ? params : 'none'
      });

      // 使用 query 而不是 execute 来避免 MySQL 8.0.22+ 的严格参数验证
      const [rows] = await this.pool.query(sql, params);
      const duration = Date.now() - startTime;

      const result = {
        rows: Array.isArray(rows) ? rows : [rows],
        rowCount: Array.isArray(rows) ? rows.length : 1,
        insertId: rows.insertId,
        affectedRows: rows.affectedRows
      };

      logger.debug('✅ 数据库查询成功:', {
        rowCount: result.rowCount,
        duration: duration + 'ms',
        insertId: result.insertId || 'N/A',
        affectedRows: result.affectedRows || 'N/A'
      });

      // 记录慢查询
      if (duration > 1000) {
        logger.warn('🐌 慢查询检测:', {
          sql: sql.substring(0, 200) + (sql.length > 200 ? '...' : ''),
          duration: duration + 'ms',
          params: params.length > 0 ? params : 'none'
        });
      }

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('❌ 数据库查询失败:', {
        sql: sql.substring(0, 200) + (sql.length > 200 ? '...' : ''),
        params: params.length > 0 ? params : 'none',
        duration: duration + 'ms',
        error: error.message,
        code: error.code || 'N/A',
        errno: error.errno || 'N/A',
        sqlState: error.sqlState || 'N/A'
      });
      logger.error('错误堆栈:', error.stack);
      throw error;
    }
  }
}

module.exports = new Database();



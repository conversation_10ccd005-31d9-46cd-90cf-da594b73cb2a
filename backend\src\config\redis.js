const redis = require('redis');
const logger = require('../utils/logger');

class RedisClient {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.lastConnectionAttempt = 0;
    this.connectionCooldown = 5000; // 5秒冷却时间
  }

  async connect() {
    try {
      logger.info('=== Redis 连接初始化 ===');
      logger.info('Redis 配置:', {
        host: process.env.REDIS_HOST || 'localhost',
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD ? '***已设置***' : '未设置',
        db: process.env.REDIS_DB || 0,
        environment: process.env.NODE_ENV
      });

      // 开发环境下，如果Redis连接失败，使用内存模拟
      if (process.env.NODE_ENV === 'development') {
        try {
          logger.info('🔄 创建 Redis 客户端 (开发环境)...');
          const redisConfig = {
            host: process.env.REDIS_HOST || 'localhost',
            port: process.env.REDIS_PORT || 6379,
            password: process.env.REDIS_PASSWORD || undefined,
            db: process.env.REDIS_DB || 0,
            retry_strategy: (options) => {
              logger.debug('Redis 重试策略触发:', {
                attempt: options.attempt,
                total_retry_time: options.total_retry_time,
                error: options.error ? options.error.message : 'N/A'
              });

              // 开发环境更宽松的重试策略
              if (options.attempt > 5) {
                logger.warn('Redis 重试次数超过限制，放弃连接');
                return undefined; // 5次重试后放弃
              }
              // 指数退避，但最大延迟不超过2秒
              const delay = Math.min(options.attempt * 200, 2000);
              logger.debug(`Redis 将在 ${delay}ms 后重试`);
              return delay;
            },
            connect_timeout: 10000, // 10秒连接超时
            lazyConnect: true // 延迟连接，避免启动时的连接竞争
          };

          this.client = redis.createClient(redisConfig);

          this.client.on('connect', () => {
            logger.info('✅ Redis 客户端连接成功');
            this.isConnected = true;
          });

          this.client.on('ready', () => {
            logger.info('✅ Redis 客户端就绪');
          });

          this.client.on('reconnecting', (params) => {
            const delay = params?.delay || 'unknown';
            const attempt = params?.attempt || 'unknown';
            logger.info(`🔄 Redis 重新连接中... 延迟: ${delay}ms, 尝试: ${attempt}`);
          });

          this.client.on('error', (error) => {
            // 详细记录所有错误
            logger.error('❌ Redis 客户端错误:', {
              message: error.message,
              code: error.code || 'N/A',
              errno: error.errno || 'N/A',
              syscall: error.syscall || 'N/A',
              address: error.address || 'N/A',
              port: error.port || 'N/A'
            });

            // 根据错误类型提供诊断建议
            if (error.code === 'ECONNREFUSED') {
              logger.error('🔍 Redis 连接被拒绝 - 请检查:');
              logger.error('  1. Redis 服务是否正在运行');
              logger.error('  2. 端口号是否正确 (当前: ' + (process.env.REDIS_PORT || 6379) + ')');
              logger.error('  3. 主机地址是否正确 (当前: ' + (process.env.REDIS_HOST || 'localhost') + ')');
            } else if (error.code === 'ENOTFOUND') {
              logger.error('🔍 Redis 主机未找到 - 请检查主机地址');
            }

            this.isConnected = false;
          });

          this.client.on('end', () => {
            logger.info('🔌 Redis 客户端连接已断开');
            this.isConnected = false;
          });

          logger.info('🔄 尝试连接 Redis...');
          await this.client.connect();
          logger.info('🎉 Redis 连接建立成功 (开发环境)');
          return this.client;
        } catch (error) {
          logger.error('❌ Redis 连接失败 (开发环境):', {
            message: error.message,
            code: error.code || 'N/A',
            stack: error.stack
          });
          logger.warn('🔄 切换到内存缓存模式');
          this.client = { mock: true };
          this.mockCache = new Map();
          this.isConnected = true;
          logger.info('✅ 内存缓存模式已启用');
          return this.client;
        }
      } else {
        // 生产环境必须连接Redis
        this.client = redis.createClient({
          host: process.env.REDIS_HOST || 'localhost',
          port: process.env.REDIS_PORT || 6379,
          password: process.env.REDIS_PASSWORD || undefined,
          db: process.env.REDIS_DB || 0,
          retry_strategy: (options) => {
            if (options.error && options.error.code === 'ECONNREFUSED') {
              logger.error('Redis server refused connection');
              return new Error('Redis server refused connection');
            }
            if (options.total_retry_time > 1000 * 60 * 60) {
              logger.error('Redis retry time exhausted');
              return new Error('Retry time exhausted');
            }
            if (options.attempt > 10) {
              logger.error('Redis max retry attempts reached');
              return undefined;
            }
            return Math.min(options.attempt * 100, 3000);
          }
        });

        await this.client.connect();
        this.isConnected = true;
        return this.client;
      }
    } catch (error) {
      logger.error('Redis connection failed:', error);
      throw error;
    }
  }

  async disconnect() {
    if (this.client && this.isConnected) {
      await this.client.quit();
      logger.info('Redis connection closed');
    }
  }

  // 设置缓存
  async set(key, value, ttl = null) {
    if (!this.isConnected) {
      logger.warn('Redis not connected, skipping cache set');
      return false;
    }

    try {
      // 模拟模式使用内存缓存
      if (this.client && this.client.mock) {
        const serializedValue = JSON.stringify(value);
        this.mockCache.set(key, { value: serializedValue, expires: ttl ? Date.now() + ttl * 1000 : null });
        return true;
      }

      const serializedValue = JSON.stringify(value);
      if (ttl) {
        await this.client.setEx(key, ttl, serializedValue);
      } else {
        await this.client.set(key, serializedValue);
      }
      return true;
    } catch (error) {
      logger.error('Redis set error:', error);
      return false;
    }
  }

  // 获取缓存
  async get(key) {
    if (!this.isConnected) {
      logger.warn('Redis not connected, skipping cache get');
      return null;
    }

    try {
      // 模拟模式使用内存缓存
      if (this.client && this.client.mock) {
        const cached = this.mockCache.get(key);
        if (!cached) return null;

        // 检查是否过期
        if (cached.expires && Date.now() > cached.expires) {
          this.mockCache.delete(key);
          return null;
        }

        return JSON.parse(cached.value);
      }

      const value = await this.client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error('Redis get error:', error);
      return null;
    }
  }

  // 删除缓存
  async del(key) {
    if (!this.isConnected) {
      logger.warn('Redis not connected, skipping cache delete');
      return false;
    }

    try {
      await this.client.del(key);
      return true;
    } catch (error) {
      logger.error('Redis delete error:', error);
      return false;
    }
  }

  // 检查键是否存在
  async exists(key) {
    if (!this.isConnected) {
      return false;
    }

    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('Redis exists error:', error);
      return false;
    }
  }

  // 设置过期时间
  async expire(key, ttl) {
    if (!this.isConnected) {
      return false;
    }

    try {
      await this.client.expire(key, ttl);
      return true;
    } catch (error) {
      logger.error('Redis expire error:', error);
      return false;
    }
  }

  // 获取所有匹配的键
  async keys(pattern) {
    if (!this.isConnected) {
      return [];
    }

    try {
      return await this.client.keys(pattern);
    } catch (error) {
      logger.error('Redis keys error:', error);
      return [];
    }
  }

  // 清空所有缓存
  async flushAll() {
    if (!this.isConnected) {
      return false;
    }

    try {
      await this.client.flushAll();
      logger.info('Redis cache cleared');
      return true;
    } catch (error) {
      logger.error('Redis flush error:', error);
      return false;
    }
  }

  // 获取Redis信息
  async getInfo() {
    if (!this.isConnected) {
      return null;
    }

    try {
      const info = await this.client.info();
      return info;
    } catch (error) {
      logger.error('Redis info error:', error);
      return null;
    }
  }

  // 健康检查
  async healthCheck() {
    if (!this.isConnected || !this.client || this.client.mock) {
      return false;
    }

    try {
      await this.client.ping();
      return true;
    } catch (error) {
      logger.debug('Redis health check failed:', error.message);
      this.isConnected = false;
      return false;
    }
  }

  // 智能重连（带冷却时间）
  async smartReconnect() {
    const now = Date.now();
    if (now - this.lastConnectionAttempt < this.connectionCooldown) {
      return false; // 在冷却时间内，不尝试重连
    }

    this.lastConnectionAttempt = now;
    try {
      await this.connect();
      return true;
    } catch (error) {
      logger.debug('Smart reconnect failed:', error.message);
      return false;
    }
  }
}

// 创建单例实例
const redisClient = new RedisClient();

module.exports = redisClient;

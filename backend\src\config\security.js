/**
 * 安全配置文件
 */

// 获取允许的域名列表
const getAllowedDomains = () => {
  const siteUrl = process.env.SITE_URL;
  const domains = [];
  
  if (siteUrl) {
    domains.push(siteUrl);
    
    // 自动添加www版本
    if (!siteUrl.includes('www.')) {
      const wwwVersion = siteUrl.replace('https://', 'https://www.');
      domains.push(wwwVersion);
    }
    
    // 自动添加非www版本
    if (siteUrl.includes('www.')) {
      const nonWwwVersion = siteUrl.replace('www.', '');
      domains.push(nonWwwVersion);
    }
  }
  
  // 开发环境域名
  if (process.env.NODE_ENV === 'development') {
    domains.push('http://localhost:3000');
    domains.push('https://localhost:3000');
  }
  
  return domains;
};

// 检查是否为可疑的User-Agent
const isSuspiciousUserAgent = (userAgent) => {
  if (!userAgent) return true;
  
  const suspiciousPatterns = [
    /postman/i,
    /insomnia/i,
    /curl/i,
    /wget/i,
    /python-requests/i,
    /axios/i,
    /node-fetch/i,
    /scrapy/i,
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i
  ];
  
  // 检查是否匹配可疑模式
  const matchesSuspiciousPattern = suspiciousPatterns.some(pattern => 
    pattern.test(userAgent)
  );
  
  // 大部分真实浏览器都包含Mozilla
  const lacksCommonBrowserSignature = !userAgent.includes('Mozilla');
  
  return matchesSuspiciousPattern || lacksCommonBrowserSignature;
};

module.exports = {
  getAllowedDomains,
  isSuspiciousUserAgent
};

const ApiKey = require('../models/ApiKey');
const logger = require('../utils/logger');
const { validationResult } = require('express-validator');

class AdminApiKeysController {
  // 获取API密钥列表
  static async getApiKeys(req, res) {
    try {
      const options = {
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 20,
        status: req.query.status
      };

      logger.api('ADMIN_GET_API_KEYS', options);

      const result = await ApiKey.findAll(options);

      res.json({
        success: true,
        data: {
          apiKeys: result.apiKeys ? result.apiKeys.map(key => key.toJSON(true)) : result.map(key => key.toJSON(true)),
          pagination: result.pagination || null
        }
      });
    } catch (error) {
      logger.error('Error in admin getApiKeys:', error);
      res.status(500).json({
        success: false,
        message: '获取API密钥列表失败'
      });
    }
  }

  // 获取单个API密钥详情
  static async getApiKeyById(req, res) {
    try {
      const { id } = req.params;
      
      logger.api('ADMIN_GET_API_KEY_BY_ID', { id });

      const apiKey = await ApiKey.findById(id);
      
      if (!apiKey) {
        return res.status(404).json({
          success: false,
          message: 'API密钥不存在'
        });
      }

      res.json({
        success: true,
        data: apiKey.toJSON(true)
      });
    } catch (error) {
      logger.error('Error in admin getApiKeyById:', error);
      res.status(500).json({
        success: false,
        message: '获取API密钥详情失败'
      });
    }
  }

  // 创建API密钥
  static async createApiKey(req, res) {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const apiKeyData = {
        name: req.body.name,
        description: req.body.description,
        status: req.body.status || 'active'
      };

      logger.api('ADMIN_CREATE_API_KEY', { name: apiKeyData.name });

      const apiKey = await ApiKey.create(apiKeyData);
      
      res.status(201).json({
        success: true,
        message: 'API密钥创建成功',
        data: apiKey.toJSON(true)
      });
    } catch (error) {
      logger.error('Error in admin createApiKey:', error);
      res.status(500).json({
        success: false,
        message: '创建API密钥失败'
      });
    }
  }

  // 更新API密钥
  static async updateApiKey(req, res) {
    try {
      const { id } = req.params;
      const updateData = {};

      // 只更新提供的字段
      const allowedFields = ['name', 'description', 'status'];

      allowedFields.forEach(field => {
        if (req.body[field] !== undefined) {
          updateData[field] = req.body[field];
        }
      });

      if (Object.keys(updateData).length === 0) {
        return res.status(400).json({
          success: false,
          message: '没有提供要更新的字段'
        });
      }

      logger.api('ADMIN_UPDATE_API_KEY', { id, fields: Object.keys(updateData) });

      const apiKey = await ApiKey.update(id, updateData);
      
      if (!apiKey) {
        return res.status(404).json({
          success: false,
          message: 'API密钥不存在'
        });
      }

      res.json({
        success: true,
        message: 'API密钥更新成功',
        data: apiKey.toJSON(true)
      });
    } catch (error) {
      logger.error('Error in admin updateApiKey:', error);
      res.status(500).json({
        success: false,
        message: '更新API密钥失败'
      });
    }
  }

  // 删除API密钥
  static async deleteApiKey(req, res) {
    try {
      const { id } = req.params;

      logger.api('ADMIN_DELETE_API_KEY', { id });

      const success = await ApiKey.delete(id);
      
      if (!success) {
        return res.status(404).json({
          success: false,
          message: 'API密钥不存在'
        });
      }

      res.json({
        success: true,
        message: 'API密钥删除成功'
      });
    } catch (error) {
      logger.error('Error in admin deleteApiKey:', error);
      res.status(500).json({
        success: false,
        message: '删除API密钥失败'
      });
    }
  }

  // 重新生成API密钥
  static async regenerateApiKey(req, res) {
    try {
      const { id } = req.params;

      logger.api('ADMIN_REGENERATE_API_KEY', { id });

      const apiKey = await ApiKey.regenerate(id);
      
      if (!apiKey) {
        return res.status(404).json({
          success: false,
          message: 'API密钥不存在'
        });
      }

      res.json({
        success: true,
        message: 'API密钥重新生成成功',
        data: apiKey.toJSON(true)
      });
    } catch (error) {
      logger.error('Error in admin regenerateApiKey:', error);
      res.status(500).json({
        success: false,
        message: '重新生成API密钥失败'
      });
    }
  }

  // 获取API密钥使用统计
  static async getApiKeyStats(req, res) {
    try {
      const { id } = req.params;
      const { days = 30 } = req.query;

      logger.api('ADMIN_GET_API_KEY_STATS', { id, days });

      const stats = await ApiKey.getUsageStats(id, parseInt(days));
      
      if (!stats) {
        return res.status(404).json({
          success: false,
          message: 'API密钥不存在'
        });
      }

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error in admin getApiKeyStats:', error);
      res.status(500).json({
        success: false,
        message: '获取API密钥统计失败'
      });
    }
  }

  // 批量操作API密钥
  static async batchOperation(req, res) {
    try {
      const { operation, apiKeyIds, data } = req.body;

      if (!operation || !Array.isArray(apiKeyIds) || apiKeyIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: '参数错误'
        });
      }

      logger.api('ADMIN_BATCH_OPERATION_API_KEYS', { operation, count: apiKeyIds.length });

      let results = [];

      switch (operation) {
        case 'delete':
          for (const id of apiKeyIds) {
            try {
              await ApiKey.delete(id);
              results.push({ id, success: true });
            } catch (error) {
              results.push({ id, success: false, error: error.message });
            }
          }
          break;

        case 'updateStatus':
          if (!data || !data.status) {
            return res.status(400).json({
              success: false,
              message: '缺少状态参数'
            });
          }
          
          for (const id of apiKeyIds) {
            try {
              await ApiKey.update(id, { status: data.status });
              results.push({ id, success: true });
            } catch (error) {
              results.push({ id, success: false, error: error.message });
            }
          }
          break;

        default:
          return res.status(400).json({
            success: false,
            message: '不支持的操作类型'
          });
      }

      const successCount = results.filter(r => r.success).length;
      const failCount = results.length - successCount;

      res.json({
        success: true,
        message: `批量操作完成：成功 ${successCount} 个，失败 ${failCount} 个`,
        data: {
          results,
          summary: {
            total: results.length,
            success: successCount,
            failed: failCount
          }
        }
      });
    } catch (error) {
      logger.error('Error in admin batchOperation API keys:', error);
      res.status(500).json({
        success: false,
        message: '批量操作失败'
      });
    }
  }
}

module.exports = AdminApiKeysController;

const Category = require('../models/Category');
const logger = require('../utils/logger');
const { validationResult } = require('express-validator');

class AdminCategoriesController {
  // 获取分类列表（后台管理）
  static async getCategories(req, res) {
    try {
      const options = {
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 20,
        status: req.query.status,
        search: req.query.search
      };

      logger.api('ADMIN_GET_CATEGORIES', options);

      const result = await Category.findAll(options);

      res.json({
        success: true,
        data: {
          categories: result.categories ? result.categories.map(cat => cat.toJSON()) : result.map(cat => cat.toJSON()),
          pagination: result.pagination || null
        }
      });
    } catch (error) {
      logger.error('Error in admin getCategories:', error);
      res.status(500).json({
        success: false,
        message: '获取分类列表失败'
      });
    }
  }

  // 获取单个分类详情
  static async getCategoryById(req, res) {
    try {
      const { id } = req.params;
      
      logger.api('ADMIN_GET_CATEGORY_BY_ID', { id });

      const category = await Category.findById(id);
      
      if (!category) {
        return res.status(404).json({
          success: false,
          message: '分类不存在'
        });
      }

      res.json({
        success: true,
        data: category.toJSON()
      });
    } catch (error) {
      logger.error('Error in admin getCategoryById:', error);
      res.status(500).json({
        success: false,
        message: '获取分类详情失败'
      });
    }
  }

  // 创建分类
  static async createCategory(req, res) {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const categoryData = {
        name: req.body.name,
        slug: req.body.slug,
        description: req.body.description,
        coverUrl: req.body.cover_url,
        sortOrder: req.body.sort_order || 0,
        status: req.body.status || 'active'
      };

      logger.api('ADMIN_CREATE_CATEGORY', { name: categoryData.name });

      const category = await Category.create(categoryData);
      
      res.status(201).json({
        success: true,
        message: '分类创建成功',
        data: category.toJSON()
      });
    } catch (error) {
      logger.error('Error in admin createCategory:', error);
      
      if (error.message.includes('Duplicate entry')) {
        return res.status(409).json({
          success: false,
          message: '分类名称或别名已存在'
        });
      }
      
      res.status(500).json({
        success: false,
        message: '创建分类失败'
      });
    }
  }

  // 更新分类
  static async updateCategory(req, res) {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const { id } = req.params;
      const updateData = {};

      // 只更新提供的字段
      const allowedFields = ['name', 'slug', 'description', 'cover_url', 'sort_order', 'status'];

      allowedFields.forEach(field => {
        if (req.body[field] !== undefined) {
          const modelField = field === 'cover_url' ? 'coverUrl' :
                           field === 'sort_order' ? 'sortOrder' : field;
          updateData[modelField] = req.body[field];
        }
      });

      logger.api('ADMIN_UPDATE_CATEGORY', { id, fields: Object.keys(updateData) });

      const category = await Category.update(id, updateData);
      
      if (!category) {
        return res.status(404).json({
          success: false,
          message: '分类不存在'
        });
      }

      res.json({
        success: true,
        message: '分类更新成功',
        data: category.toJSON()
      });
    } catch (error) {
      logger.error('Error in admin updateCategory:', error);
      
      if (error.message.includes('Duplicate entry')) {
        return res.status(409).json({
          success: false,
          message: '分类名称或别名已存在'
        });
      }
      
      res.status(500).json({
        success: false,
        message: '更新分类失败'
      });
    }
  }

  // 删除分类
  static async deleteCategory(req, res) {
    try {
      const { id } = req.params;

      logger.api('ADMIN_DELETE_CATEGORY', { id });

      // 检查分类下是否有视频
      const videosCount = await Category.getVideosCount(id);
      
      if (videosCount > 0) {
        return res.status(400).json({
          success: false,
          message: `该分类下还有 ${videosCount} 个视频，无法删除`
        });
      }

      const success = await Category.delete(id);
      
      if (!success) {
        return res.status(404).json({
          success: false,
          message: '分类不存在'
        });
      }

      res.json({
        success: true,
        message: '分类删除成功'
      });
    } catch (error) {
      logger.error('Error in admin deleteCategory:', error);
      res.status(500).json({
        success: false,
        message: '删除分类失败'
      });
    }
  }

  // 批量操作分类
  static async batchOperation(req, res) {
    try {
      const { operation, categoryIds, data } = req.body;

      if (!operation || !Array.isArray(categoryIds) || categoryIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: '参数错误'
        });
      }

      logger.api('ADMIN_BATCH_OPERATION_CATEGORIES', { operation, count: categoryIds.length });

      let results = [];

      switch (operation) {
        case 'delete':
          for (const id of categoryIds) {
            try {
              // 检查分类下是否有视频
              const videosCount = await Category.getVideosCount(id);
              if (videosCount > 0) {
                results.push({ id, success: false, error: '分类下有视频，无法删除' });
                continue;
              }
              
              await Category.delete(id);
              results.push({ id, success: true });
            } catch (error) {
              results.push({ id, success: false, error: error.message });
            }
          }
          break;

        case 'updateStatus':
          if (!data || !data.status) {
            return res.status(400).json({
              success: false,
              message: '缺少状态参数'
            });
          }
          
          for (const id of categoryIds) {
            try {
              await Category.update(id, { status: data.status });
              results.push({ id, success: true });
            } catch (error) {
              results.push({ id, success: false, error: error.message });
            }
          }
          break;

        default:
          return res.status(400).json({
            success: false,
            message: '不支持的操作类型'
          });
      }

      const successCount = results.filter(r => r.success).length;
      const failCount = results.length - successCount;

      res.json({
        success: true,
        message: `批量操作完成：成功 ${successCount} 个，失败 ${failCount} 个`,
        data: {
          results,
          summary: {
            total: results.length,
            success: successCount,
            failed: failCount
          }
        }
      });
    } catch (error) {
      logger.error('Error in admin batchOperation categories:', error);
      res.status(500).json({
        success: false,
        message: '批量操作失败'
      });
    }
  }

  // 更新分类排序
  static async updateSortOrder(req, res) {
    try {
      const { categories } = req.body;

      if (!Array.isArray(categories)) {
        return res.status(400).json({
          success: false,
          message: '参数格式错误'
        });
      }

      logger.api('ADMIN_UPDATE_CATEGORY_SORT', { count: categories.length });

      const results = [];

      for (const item of categories) {
        try {
          await Category.update(item.id, { sortOrder: item.sortOrder });
          results.push({ id: item.id, success: true });
        } catch (error) {
          results.push({ id: item.id, success: false, error: error.message });
        }
      }

      const successCount = results.filter(r => r.success).length;

      res.json({
        success: true,
        message: `排序更新完成：成功 ${successCount} 个`,
        data: {
          results,
          summary: {
            total: results.length,
            success: successCount,
            failed: results.length - successCount
          }
        }
      });
    } catch (error) {
      logger.error('Error in admin updateSortOrder:', error);
      res.status(500).json({
        success: false,
        message: '更新排序失败'
      });
    }
  }

  // 获取分类统计信息
  static async getCategoryStats(req, res) {
    try {
      logger.api('ADMIN_GET_CATEGORY_STATS');

      const stats = await Category.getStats();

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error in admin getCategoryStats:', error);
      res.status(500).json({
        success: false,
        message: '获取统计信息失败'
      });
    }
  }
}

module.exports = AdminCategoriesController;

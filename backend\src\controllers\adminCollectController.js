const Video = require('../models/Video');
const Category = require('../models/Category');
const CollectLog = require('../models/CollectLog');
const logger = require('../utils/logger');

class AdminCollectController {
  // 采集单个视频
  static async collectSingleVideo(req, res) {
    let collectLog = null;
    const startTime = new Date();

    try {
      const {
        sourceId,
        videoId,
        targetCategoryId,
        updateExisting = false,
        collectImages = true,
        featured = false,
        videoData
      } = req.body;

      logger.api('ADMIN_COLLECT_SINGLE_VIDEO', {
        sourceId,
        videoId,
        targetCategoryId,
        updateExisting,
        collectImages
      });

      // 创建采集日志
      collectLog = await CollectLog.create({
        sourceId: sourceId || 0,
        sourceName: '单个视频采集',
        type: 'manual',
        status: 'running',
        collectParams: {
          videoId,
          targetCategoryId,
          updateExisting,
          collectImages,
          featured
        },
        categoryId: targetCategoryId,
        totalFound: 1,
        startTime: startTime,
        createdBy: 1 // TODO: 从请求中获取用户ID
      });

      // 验证必需参数
      if (!videoData || !targetCategoryId) {
        // 更新日志为失败状态
        if (collectLog) {
          await CollectLog.update(collectLog.id, {
            status: 'failed',
            endTime: new Date(),
            duration: Math.floor((new Date() - startTime) / 1000),
            errorMessage: '缺少必需参数：videoData 或 targetCategoryId'
          });
        }

        return res.status(400).json({
          success: false,
          message: '缺少必需参数：videoData 或 targetCategoryId'
        });
      }

      // 验证目标分类是否存在
      const targetCategory = await Category.findById(targetCategoryId);
      if (!targetCategory) {
        // 更新日志为失败状态
        if (collectLog) {
          await CollectLog.update(collectLog.id, {
            status: 'failed',
            endTime: new Date(),
            duration: Math.floor((new Date() - startTime) / 1000),
            errorMessage: '指定的目标分类不存在'
          });
        }

        return res.status(400).json({
          success: false,
          message: '指定的目标分类不存在'
        });
      }

      // 处理播放地址 - 提取$符号后的部分
      let processedVideoUrl = '';
      if (videoData.vod_play_url) {
        const playUrl = videoData.vod_play_url.toString();
        const dollarIndex = playUrl.indexOf('$');
        if (dollarIndex !== -1) {
          // 提取$符号后的部分
          processedVideoUrl = playUrl.substring(dollarIndex + 1);
        } else {
          // 如果没有$符号，使用原始URL
          processedVideoUrl = playUrl;
        }
      }

      // 生成随机观看次数 (100-10000之间)
      const randomViews = Math.floor(Math.random() * 9901) + 100;

      // 准备视频数据
      const newVideoData = {
        title: videoData.vod_name || '未知标题',
        description: AdminCollectController.cleanHtmlContent(videoData.vod_blurb || ''),
        coverUrl: collectImages ? (videoData.vod_pic || '') : '',
        videoUrl: processedVideoUrl,
        categoryId: parseInt(targetCategoryId),
        sourceId: AdminCollectController.generateSourceId(videoData.vod_en || videoData.vod_id || ''),
        tags: AdminCollectController.extractTags(videoData),
        duration: videoData.vod_duration || '',
        rating: AdminCollectController.parseRating(videoData.vod_score),
        views: randomViews,
        status: 'active',
        featured: featured
      };

      // 检查是否已存在相同的视频
      let existingVideo = null;
      if (newVideoData.sourceId) {
        existingVideo = await Video.findBySourceId(newVideoData.sourceId);
      }

      let result;
      if (existingVideo && updateExisting) {
        // 更新已存在的视频
        result = await Video.update(existingVideo.id, newVideoData);
        logger.info('视频更新成功:', {
          id: existingVideo.id,
          title: newVideoData.title,
          sourceId: newVideoData.sourceId
        });

        // 更新日志为成功状态
        if (collectLog) {
          await CollectLog.update(collectLog.id, {
            status: 'success',
            totalCollected: 1,
            endTime: new Date(),
            duration: Math.floor((new Date() - startTime) / 1000)
          });
        }

        res.json({
          success: true,
          message: '视频更新成功',
          data: {
            id: result.id,
            title: result.title,
            action: 'updated'
          }
        });
      } else if (existingVideo && !updateExisting) {
        // 视频已存在且不允许更新
        // 更新日志为跳过状态
        if (collectLog) {
          await CollectLog.update(collectLog.id, {
            status: 'success',
            totalSkipped: 1,
            endTime: new Date(),
            duration: Math.floor((new Date() - startTime) / 1000),
            errorMessage: '视频已存在，跳过采集'
          });
        }

        return res.status(409).json({
          success: false,
          message: '视频已存在，如需更新请勾选"如果已存在则更新"选项',
          data: {
            existingVideo: {
              id: existingVideo.id,
              title: existingVideo.title
            }
          }
        });
      } else {
        // 创建新视频
        result = await Video.create(newVideoData);
        logger.info('视频采集成功:', {
          id: result.id,
          title: newVideoData.title,
          sourceId: newVideoData.sourceId
        });

        // 更新日志为成功状态
        if (collectLog) {
          await CollectLog.update(collectLog.id, {
            status: 'success',
            totalCollected: 1,
            endTime: new Date(),
            duration: Math.floor((new Date() - startTime) / 1000)
          });
        }

        res.status(201).json({
          success: true,
          message: '视频采集成功',
          data: {
            id: result.id,
            title: result.title,
            action: 'created'
          }
        });
      }

    } catch (error) {
      logger.error('采集单个视频失败:', error);

      // 更新日志为失败状态
      if (collectLog) {
        try {
          await CollectLog.update(collectLog.id, {
            status: 'failed',
            totalFailed: 1,
            endTime: new Date(),
            duration: Math.floor((new Date() - startTime) / 1000),
            errorMessage: error.message,
            errorDetails: {
              stack: error.stack,
              name: error.name
            }
          });
        } catch (logError) {
          logger.error('更新采集日志失败:', logError);
        }
      }

      res.status(500).json({
        success: false,
        message: '采集失败：' + error.message
      });
    }
  }

  // 提取标签
  static extractTags(videoData) {
    // 从 vod_class 字段获取标签，该字段包含逗号分隔的标签
    if (!videoData.vod_class) {
      return [];
    }

    // 将逗号分隔的字符串转换为数组，并清理空白字符
    return videoData.vod_class
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
  }

  // 解析评分
  static parseRating(score) {
    if (!score) return 0;

    const rating = parseFloat(score);
    if (isNaN(rating)) return 0;

    // 确保评分在0-10之间
    return Math.max(0, Math.min(10, rating));
  }

  // 生成符合数据库长度限制的source_id
  static generateSourceId(vodEn) {
    if (!vodEn) return '';

    // 如果长度在255字符以内，直接返回
    if (vodEn.length <= 255) {
      return vodEn;
    }

    // 如果超过255字符，截取前245字符并添加哈希后缀
    const crypto = require('crypto');
    const truncated = vodEn.substring(0, 245);
    const hash = crypto.createHash('md5').update(vodEn).digest('hex').substring(0, 8);

    return `${truncated}_${hash}`;
  }

  // 清理HTML内容，只保留纯文本
  static cleanHtmlContent(htmlContent) {
    if (!htmlContent) return '';

    return htmlContent
      // 移除HTML标签
      .replace(/<[^>]*>/g, '')
      // 替换HTML实体
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&hellip;/g, '...')
      // 清理多余的空白字符
      .replace(/\s+/g, ' ')
      .trim();
  }
}

module.exports = AdminCollectController;

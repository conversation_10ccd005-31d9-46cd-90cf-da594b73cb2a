const Admin = require('../models/Admin');
const database = require('../config/database');
const logger = require('../utils/logger');
const { validationResult } = require('express-validator');

class AdminController {
  // 管理员登录
  static async login(req, res) {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const { username, password } = req.body;

      // 执行登录
      const result = await Admin.login(username, password);

      if (result.success) {
        res.json({
          success: true,
          message: result.message,
          data: {
            token: result.token,
            admin: result.admin
          }
        });
      } else {
        res.status(401).json({
          success: false,
          message: result.message
        });
      }
    } catch (error) {
      logger.error('Admin login error:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  // 获取当前管理员信息
  static async getCurrentAdmin(req, res) {
    try {
      const admin = req.admin; // 从认证中间件获取
      
      res.json({
        success: true,
        data: admin.toJSON()
      });
    } catch (error) {
      logger.error('Get current admin error:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  // 获取管理员列表
  static async getAdmins(req, res) {
    try {
      const { page = 1, limit = 10, status } = req.query;
      const offset = (page - 1) * limit;

      const options = {
        limit: parseInt(limit),
        offset: parseInt(offset)
      };

      if (status) {
        options.status = status;
      }

      const admins = await Admin.findAll(options);
      
      res.json({
        success: true,
        data: {
          admins: admins.map(admin => admin.toJSON()),
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: admins.length
          }
        }
      });
    } catch (error) {
      logger.error('Get admins error:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  // 创建管理员
  static async createAdmin(req, res) {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const { name, username, password, email, status } = req.body;

      const adminData = {
        name,
        username,
        password,
        email,
        status
      };

      const admin = await Admin.create(adminData);
      
      res.status(201).json({
        success: true,
        message: '管理员创建成功',
        data: admin.toJSON()
      });
    } catch (error) {
      logger.error('Create admin error:', error);
      
      if (error.message === '用户名已存在') {
        return res.status(409).json({
          success: false,
          message: error.message
        });
      }
      
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  // 更新管理员
  static async updateAdmin(req, res) {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const { id } = req.params;
      const updateData = req.body;

      // 查找管理员
      const admin = await Admin.findById(id);
      if (!admin) {
        return res.status(404).json({
          success: false,
          message: '管理员不存在'
        });
      }

      // 更新管理员
      const updatedAdmin = await admin.update(updateData);
      
      res.json({
        success: true,
        message: '管理员更新成功',
        data: updatedAdmin.toJSON()
      });
    } catch (error) {
      logger.error('Update admin error:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  // 删除管理员
  static async deleteAdmin(req, res) {
    try {
      const { id } = req.params;

      // 查找管理员
      const admin = await Admin.findById(id);
      if (!admin) {
        return res.status(404).json({
          success: false,
          message: '管理员不存在'
        });
      }

      // 不能删除自己
      if (admin.id === req.admin.id) {
        return res.status(400).json({
          success: false,
          message: '不能删除自己的账户'
        });
      }

      // 删除管理员
      await admin.delete();
      
      res.json({
        success: true,
        message: '管理员删除成功'
      });
    } catch (error) {
      logger.error('Delete admin error:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  // 修改密码
  static async changePassword(req, res) {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const { currentPassword, newPassword } = req.body;
      const admin = req.admin;

      // 验证当前密码
      const isCurrentPasswordValid = await admin.verifyPassword(currentPassword);
      if (!isCurrentPasswordValid) {
        return res.status(400).json({
          success: false,
          message: '当前密码错误'
        });
      }

      // 更新密码
      await admin.update({ password: newPassword });
      
      logger.security('PASSWORD_CHANGED', { username: admin.username, id: admin.id });
      
      res.json({
        success: true,
        message: '密码修改成功'
      });
    } catch (error) {
      logger.error('Change password error:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  // 获取仪表盘数据
  static async getDashboard(req, res) {
    try {
      logger.api('ADMIN_GET_DASHBOARD', { adminId: req.admin.id });

      // 获取各种统计数据
      const [
        videoStats,
        categoryStats,
        adminStats,
        recentVideos,
        todayViews
      ] = await Promise.all([
        // 视频统计
        database.query(`
          SELECT
            COUNT(*) as total,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active,
            COUNT(CASE WHEN featured = 1 THEN 1 END) as featured,
            COALESCE(SUM(views), 0) as total_views,
            COALESCE(AVG(rating), 0) as avg_rating
          FROM videos
        `),

        // 分类统计
        database.query(`
          SELECT
            COUNT(*) as total,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active
          FROM categories
        `),

        // 管理员统计
        database.query(`
          SELECT
            COUNT(*) as total,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active,
            COUNT(CASE WHEN last_login_at > DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as active_today
          FROM admins
        `),

        // 最近上传的视频
        database.query(`
          SELECT
            id, title, duration, created_at, status, views
          FROM videos
          ORDER BY created_at DESC
          LIMIT 10
        `),

        // 今日观看数
        database.query(`
          SELECT COALESCE(SUM(views), 0) as today_views
          FROM videos
          WHERE DATE(updated_at) = CURDATE()
        `)
      ]);

      // 处理统计数据
      const stats = {
        totalVideos: parseInt(videoStats.rows[0]?.total) || 0,
        activeVideos: parseInt(videoStats.rows[0]?.active) || 0,
        featuredVideos: parseInt(videoStats.rows[0]?.featured) || 0,
        totalViews: parseInt(videoStats.rows[0]?.total_views) || 0,
        averageRating: parseFloat(videoStats.rows[0]?.avg_rating) || 0,

        totalCategories: parseInt(categoryStats.rows[0]?.total) || 0,
        activeCategories: parseInt(categoryStats.rows[0]?.active) || 0,

        totalAdmins: parseInt(adminStats.rows[0]?.total) || 0,
        activeAdmins: parseInt(adminStats.rows[0]?.active) || 0,
        activeTodayAdmins: parseInt(adminStats.rows[0]?.active_today) || 0,

        todayViews: parseInt(todayViews.rows[0]?.today_views) || 0
      };

      // 处理最近视频数据
      const recentActivity = recentVideos.rows.map(video => ({
        id: video.id,
        title: video.title,
        duration: video.duration,
        uploadTime: video.created_at,
        status: video.status,
        views: video.views
      }));

      const dashboardData = {
        stats,
        recentActivity,
        systemInfo: {
          version: require('../../package.json').version,
          uptime: Math.floor(process.uptime()),
          environment: process.env.NODE_ENV,
          nodeVersion: process.version,
          platform: process.platform,
          memory: {
            used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
            total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
          }
        }
      };

      res.json({
        success: true,
        data: dashboardData
      });
    } catch (error) {
      logger.error('Get dashboard error:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
}

module.exports = AdminController;

const Video = require('../models/Video');
const Category = require('../models/Category');
const logger = require('../utils/logger');
const { validationResult } = require('express-validator');

class AdminVideosController {
  // 获取视频列表（后台管理）
  static async getVideos(req, res) {
    try {
      const options = {
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 20,
        category: req.query.category_id ? parseInt(req.query.category_id) : (req.query.category ? parseInt(req.query.category) : null),
        tag: req.query.tag,
        search: req.query.search,
        sort: req.query.sort || 'latest',
        status: req.query.status // 后台可以查看所有状态的视频
      };

      logger.api('ADMIN_GET_VIDEOS', options);

      const result = await Video.findAll(options);

      res.json({
        success: true,
        data: {
          videos: result.videos.map(video => video.toJSON()),
          pagination: result.pagination
        }
      });
    } catch (error) {
      logger.error('Error in admin getVideos:', error);
      res.status(500).json({
        success: false,
        message: '获取视频列表失败'
      });
    }
  }

  // 获取单个视频详情（后台管理）
  static async getVideoById(req, res) {
    try {
      const { id } = req.params;
      
      logger.api('ADMIN_GET_VIDEO_BY_ID', { id });

      const video = await Video.findById(id);
      
      if (!video) {
        return res.status(404).json({
          success: false,
          message: '视频不存在'
        });
      }

      res.json({
        success: true,
        data: video.toJSON()
      });
    } catch (error) {
      logger.error('Error in admin getVideoById:', error);
      res.status(500).json({
        success: false,
        message: '获取视频详情失败'
      });
    }
  }

  // 创建视频
  static async createVideo(req, res) {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const videoData = {
        title: req.body.title,
        description: req.body.description,
        coverUrl: req.body.cover_url,
        videoUrl: req.body.video_url,
        categoryId: req.body.category_id,
        tags: req.body.tags || [],
        duration: req.body.duration,
        status: req.body.status || 'active',
        featured: req.body.featured || false
      };

      logger.api('ADMIN_CREATE_VIDEO', { title: videoData.title });

      const video = await Video.create(videoData);
      
      res.status(201).json({
        success: true,
        message: '视频创建成功',
        data: video.toJSON()
      });
    } catch (error) {
      logger.error('Error in admin createVideo:', error);
      res.status(500).json({
        success: false,
        message: '创建视频失败'
      });
    }
  }

  // 更新视频
  static async updateVideo(req, res) {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const { id } = req.params;
      const updateData = {};

      // 只更新提供的字段
      const allowedFields = [
        'title', 'description', 'cover_url', 'video_url', 
        'category_id', 'tags', 'duration', 'status', 'featured'
      ];

      allowedFields.forEach(field => {
        if (req.body[field] !== undefined) {
          const modelField = field === 'cover_url' ? 'coverUrl' :
                           field === 'video_url' ? 'videoUrl' :
                           field === 'category_id' ? 'categoryId' : field;
          updateData[modelField] = req.body[field];
        }
      });

      logger.api('ADMIN_UPDATE_VIDEO', { id, fields: Object.keys(updateData) });

      const video = await Video.update(id, updateData);
      
      if (!video) {
        return res.status(404).json({
          success: false,
          message: '视频不存在'
        });
      }

      res.json({
        success: true,
        message: '视频更新成功',
        data: video.toJSON()
      });
    } catch (error) {
      logger.error('Error in admin updateVideo:', error);
      res.status(500).json({
        success: false,
        message: '更新视频失败'
      });
    }
  }

  // 删除视频
  static async deleteVideo(req, res) {
    try {
      const { id } = req.params;

      logger.api('ADMIN_DELETE_VIDEO', { id });

      const success = await Video.delete(id);
      
      if (!success) {
        return res.status(404).json({
          success: false,
          message: '视频不存在'
        });
      }

      res.json({
        success: true,
        message: '视频删除成功'
      });
    } catch (error) {
      logger.error('Error in admin deleteVideo:', error);
      res.status(500).json({
        success: false,
        message: '删除视频失败'
      });
    }
  }

  // 批量操作视频
  static async batchOperation(req, res) {
    try {
      const { operation, videoIds, data } = req.body;

      if (!operation || !Array.isArray(videoIds) || videoIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: '参数错误'
        });
      }

      logger.api('ADMIN_BATCH_OPERATION', { operation, count: videoIds.length });

      let results = [];

      switch (operation) {
        case 'delete':
          for (const id of videoIds) {
            try {
              await Video.delete(id);
              results.push({ id, success: true });
            } catch (error) {
              results.push({ id, success: false, error: error.message });
            }
          }
          break;

        case 'updateStatus':
          if (!data || !data.status) {
            return res.status(400).json({
              success: false,
              message: '缺少状态参数'
            });
          }
          
          for (const id of videoIds) {
            try {
              await Video.update(id, { status: data.status });
              results.push({ id, success: true });
            } catch (error) {
              results.push({ id, success: false, error: error.message });
            }
          }
          break;

        case 'updateFeatured':
          if (data.featured === undefined) {
            return res.status(400).json({
              success: false,
              message: '缺少推荐状态参数'
            });
          }
          
          for (const id of videoIds) {
            try {
              await Video.update(id, { featured: data.featured });
              results.push({ id, success: true });
            } catch (error) {
              results.push({ id, success: false, error: error.message });
            }
          }
          break;

        default:
          return res.status(400).json({
            success: false,
            message: '不支持的操作类型'
          });
      }

      const successCount = results.filter(r => r.success).length;
      const failCount = results.length - successCount;

      res.json({
        success: true,
        message: `批量操作完成：成功 ${successCount} 个，失败 ${failCount} 个`,
        data: {
          results,
          summary: {
            total: results.length,
            success: successCount,
            failed: failCount
          }
        }
      });
    } catch (error) {
      logger.error('Error in admin batchOperation:', error);
      res.status(500).json({
        success: false,
        message: '批量操作失败'
      });
    }
  }

  // 获取视频统计信息
  static async getVideoStats(req, res) {
    try {
      logger.api('ADMIN_GET_VIDEO_STATS');

      const stats = await Video.getStats();

      res.json({
        success: true,
        data: {
          total: parseInt(stats.total) || 0,
          active: parseInt(stats.active) || 0,
          featured: parseInt(stats.featured) || 0,
          totalViews: parseInt(stats.total_views) || 0,
          averageRating: parseFloat(stats.avg_rating) || 0
        }
      });
    } catch (error) {
      logger.error('Error in admin getVideoStats:', error);
      res.status(500).json({
        success: false,
        message: '获取统计信息失败'
      });
    }
  }
}

module.exports = AdminVideosController;

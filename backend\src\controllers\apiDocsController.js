const logger = require('../utils/logger');

class ApiDocsController {
  // 获取前端API文档
  static async getFrontendApiDocs(req, res) {
    try {
      logger.api('GET_FRONTEND_API_DOCS');

      const frontendApis = [
        {
          id: 1,
          name: '获取视频列表',
          method: 'GET',
          endpoint: '/api/videos',
          description: '获取视频列表，支持分页、搜索、分类筛选',
          parameters: [
            { name: 'page', type: 'number', required: false, description: '页码，默认1' },
            { name: 'limit', type: 'number', required: false, description: '每页数量，默认24' },
            { name: 'category', type: 'number', required: false, description: '分类ID' },
            { name: 'search', type: 'string', required: false, description: '搜索关键词' },
            { name: 'sort', type: 'string', required: false, description: '排序方式：latest, popular, views' }
          ],
          response: {
            success: true,
            data: {
              videos: '视频列表',
              pagination: '分页信息'
            }
          },
          status: 'active',
          requests: 15420
        },
        {
          id: 2,
          name: '获取视频详情',
          method: 'GET',
          endpoint: '/api/videos/:id',
          description: '根据视频ID获取详细信息',
          parameters: [
            { name: 'id', type: 'number', required: true, description: '视频ID' }
          ],
          response: {
            success: true,
            data: {
              video: '视频详细信息'
            }
          },
          status: 'active',
          requests: 8934
        },
        {
          id: 3,
          name: '获取分类列表',
          method: 'GET',
          endpoint: '/api/categories',
          description: '获取所有视频分类',
          parameters: [
            { name: 'page', type: 'number', required: false, description: '页码，默认1' },
            { name: 'limit', type: 'number', required: false, description: '每页数量，默认12' }
          ],
          response: {
            success: true,
            data: {
              categories: '分类列表',
              pagination: '分页信息'
            }
          },
          status: 'active',
          requests: 3245
        },
        {
          id: 4,
          name: '搜索视频',
          method: 'GET',
          endpoint: '/api/search',
          description: '根据关键词搜索视频',
          parameters: [
            { name: 'q', type: 'string', required: true, description: '搜索关键词' },
            { name: 'page', type: 'number', required: false, description: '页码，默认1' },
            { name: 'limit', type: 'number', required: false, description: '每页数量，默认24' }
          ],
          response: {
            success: true,
            data: {
              videos: '搜索结果',
              pagination: '分页信息'
            }
          },
          status: 'active',
          requests: 2156
        },
        {
          id: 5,
          name: '获取排行榜',
          method: 'GET',
          endpoint: '/api/rankings',
          description: '获取视频排行榜',
          parameters: [
            { name: 'type', type: 'string', required: false, description: '排行类型：daily, weekly, monthly' },
            { name: 'limit', type: 'number', required: false, description: '数量限制，默认50' }
          ],
          response: {
            success: true,
            data: {
              rankings: '排行榜数据'
            }
          },
          status: 'active',
          requests: 1876
        },
        {
          id: 6,
          name: '增加视频观看次数',
          method: 'POST',
          endpoint: '/api/videos/:id/view',
          description: '记录视频观看次数',
          parameters: [
            { name: 'id', type: 'number', required: true, description: '视频ID' }
          ],
          response: {
            success: true,
            message: '观看次数已更新'
          },
          status: 'active',
          requests: 12847
        }
      ];

      res.json({
        success: true,
        data: {
          apis: frontendApis,
          total: frontendApis.length,
          baseUrl: process.env.API_BASE_URL || 'http://localhost:3001',
          version: '1.0.0'
        }
      });
    } catch (error) {
      logger.error('Error in getFrontendApiDocs:', error);
      res.status(500).json({
        success: false,
        message: '获取前端API文档失败'
      });
    }
  }

  // 获取Python上传API文档
  static async getUploadApiDocs(req, res) {
    try {
      logger.api('GET_UPLOAD_API_DOCS');

      const uploadApis = [
        {
          id: 1,
          name: '上传视频数据',
          method: 'POST',
          endpoint: '/api/upload/video',
          description: '上传视频基本信息和元数据',
          authentication: 'API Key (Bearer Token)',
          parameters: [
            { name: 'title', type: 'string', required: true, description: '视频标题' },
            { name: 'description', type: 'string', required: false, description: '视频描述' },
            { name: 'category_id', type: 'number', required: true, description: '分类ID' },
            { name: 'duration', type: 'number', required: false, description: '视频时长（秒）' },
            { name: 'file_size', type: 'number', required: false, description: '文件大小（字节）' },
            { name: 'video_url', type: 'string', required: true, description: '视频文件URL' },
            { name: 'thumbnail_url', type: 'string', required: false, description: '缩略图URL' },
            { name: 'tags', type: 'array', required: false, description: '标签数组' }
          ],
          response: {
            success: true,
            message: '视频上传成功',
            data: {
              video_id: '新创建的视频ID'
            }
          },
          status: 'active',
          requests: 1234
        },
        {
          id: 2,
          name: '批量上传视频',
          method: 'POST',
          endpoint: '/api/upload/videos/batch',
          description: '批量上传多个视频数据',
          authentication: 'API Key (Bearer Token)',
          parameters: [
            { name: 'videos', type: 'array', required: true, description: '视频数据数组' }
          ],
          response: {
            success: true,
            message: '批量上传完成',
            data: {
              success_count: '成功数量',
              failed_count: '失败数量',
              video_ids: '成功创建的视频ID列表'
            }
          },
          status: 'active',
          requests: 456
        },
        {
          id: 3,
          name: '更新视频信息',
          method: 'PUT',
          endpoint: '/api/upload/video/:id',
          description: '更新已存在的视频信息',
          authentication: 'API Key (Bearer Token)',
          parameters: [
            { name: 'id', type: 'number', required: true, description: '视频ID' },
            { name: 'title', type: 'string', required: false, description: '视频标题' },
            { name: 'description', type: 'string', required: false, description: '视频描述' },
            { name: 'category_id', type: 'number', required: false, description: '分类ID' }
          ],
          response: {
            success: true,
            message: '视频信息更新成功'
          },
          status: 'active',
          requests: 234
        },
        {
          id: 4,
          name: '删除视频',
          method: 'DELETE',
          endpoint: '/api/upload/video/:id',
          description: '删除指定的视频',
          authentication: 'API Key (Bearer Token)',
          parameters: [
            { name: 'id', type: 'number', required: true, description: '视频ID' }
          ],
          response: {
            success: true,
            message: '视频删除成功'
          },
          status: 'active',
          requests: 89
        },
        {
          id: 5,
          name: '获取上传统计',
          method: 'GET',
          endpoint: '/api/upload/stats',
          description: '获取API密钥的上传统计信息',
          authentication: 'API Key (Bearer Token)',
          parameters: [
            { name: 'days', type: 'number', required: false, description: '统计天数，默认30' }
          ],
          response: {
            success: true,
            data: {
              total_uploads: '总上传数',
              success_uploads: '成功上传数',
              failed_uploads: '失败上传数',
              daily_stats: '每日统计'
            }
          },
          status: 'active',
          requests: 156
        }
      ];

      res.json({
        success: true,
        data: {
          apis: uploadApis,
          total: uploadApis.length,
          baseUrl: process.env.API_BASE_URL || 'http://localhost:3001',
          version: '1.0.0',
          authentication: {
            type: 'API Key',
            header: 'Authorization',
            format: 'Bearer {api_key}',
            note: '需要在管理后台创建API密钥'
          }
        }
      });
    } catch (error) {
      logger.error('Error in getUploadApiDocs:', error);
      res.status(500).json({
        success: false,
        message: '获取上传API文档失败'
      });
    }
  }
}

module.exports = ApiDocsController;

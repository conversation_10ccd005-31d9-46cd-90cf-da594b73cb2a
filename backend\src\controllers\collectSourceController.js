const CollectSource = require('../models/CollectSource');
const Category = require('../models/Category');
const CollectLog = require('../models/CollectLog');
const CollectTask = require('../models/CollectTask');
const collectProcessor = require('../services/CollectProcessor');
const logger = require('../utils/logger');
const axios = require('axios');

// 获取采集源列表
const getCollectSources = async (req, res) => {
  try {
    const options = {
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 20,
      status: req.query.status,
      type: req.query.type,
      search: req.query.search,
      sortField: req.query.sortField || 'created_at',
      sortDirection: req.query.sortDirection || 'DESC'
    };

    const result = await CollectSource.findAll(options);

    res.json({
      success: true,
      data: result.sources,
      pagination: result.pagination,
      message: '获取采集源列表成功'
    });
  } catch (error) {
    logger.error('获取采集源列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取采集源列表失败',
      error: error.message
    });
  }
};

// 获取单个采集源
const getCollectSource = async (req, res) => {
  try {
    const { id } = req.params;
    const source = await CollectSource.findById(id);

    if (!source) {
      return res.status(404).json({
        success: false,
        message: '采集源不存在'
      });
    }

    res.json({
      success: true,
      data: source,
      message: '获取采集源成功'
    });
  } catch (error) {
    logger.error('获取采集源失败:', error);
    res.status(500).json({
      success: false,
      message: '获取采集源失败',
      error: error.message
    });
  }
};

// 创建采集源
const createCollectSource = async (req, res) => {
  try {
    const {
      name,
      url,
      type = 'maccms',
      description,
      status = 'active',
      collectConfig = {},
      autoCollect = 0,
      collectInterval = 60,
      collectImages = 1,
      collectCategories
    } = req.body;

    // 验证必填字段
    if (!name || !url) {
      return res.status(400).json({
        success: false,
        message: '名称和URL是必填字段'
      });
    }

    // 验证URL格式
    try {
      new URL(url);
    } catch (e) {
      return res.status(400).json({
        success: false,
        message: 'URL格式不正确'
      });
    }

    const sourceData = {
      name,
      url,
      type,
      description,
      status,
      collectConfig,
      autoCollect,
      collectInterval,
      collectImages,
      collectCategories,
      createdBy: req.user?.id
    };

    const source = await CollectSource.create(sourceData);

    res.status(201).json({
      success: true,
      data: source,
      message: '采集源创建成功'
    });
  } catch (error) {
    logger.error('创建采集源失败:', error);
    
    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(400).json({
        success: false,
        message: '采集源名称已存在'
      });
    }

    res.status(500).json({
      success: false,
      message: '创建采集源失败',
      error: error.message
    });
  }
};

// 更新采集源
const updateCollectSource = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = {
      ...req.body,
      updatedBy: req.user?.id
    };

    // 如果更新URL，验证格式
    if (updateData.url) {
      try {
        new URL(updateData.url);
      } catch (e) {
        return res.status(400).json({
          success: false,
          message: 'URL格式不正确'
        });
      }
    }

    const source = await CollectSource.update(id, updateData);

    res.json({
      success: true,
      data: source,
      message: '采集源更新成功'
    });
  } catch (error) {
    logger.error('更新采集源失败:', error);
    
    if (error.message === '采集源不存在') {
      return res.status(404).json({
        success: false,
        message: '采集源不存在'
      });
    }

    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(400).json({
        success: false,
        message: '采集源名称已存在'
      });
    }

    res.status(500).json({
      success: false,
      message: '更新采集源失败',
      error: error.message
    });
  }
};

// 删除采集源
const deleteCollectSource = async (req, res) => {
  try {
    const { id } = req.params;
    await CollectSource.delete(id);

    res.json({
      success: true,
      message: '采集源删除成功'
    });
  } catch (error) {
    logger.error('删除采集源失败:', error);
    
    if (error.message === '采集源不存在') {
      return res.status(404).json({
        success: false,
        message: '采集源不存在'
      });
    }

    res.status(500).json({
      success: false,
      message: '删除采集源失败',
      error: error.message
    });
  }
};

// 测试采集源连接
const testCollectSource = async (req, res) => {
  try {
    const { id } = req.params;
    const source = await CollectSource.findById(id);

    if (!source) {
      return res.status(404).json({
        success: false,
        message: '采集源不存在'
      });
    }

    const startTime = Date.now();
    
    try {
      // 测试连接
      const response = await axios({
        method: 'GET',
        url: source.url,
        params: { ac: 'list', pg: 1 },
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        timeout: 30000
      });

      const responseTime = Date.now() - startTime;
      
      // 更新统计信息
      await CollectSource.updateStats(id, {
        responseTime,
        successRate: 100
      });

      res.json({
        success: true,
        data: {
          status: 'online',
          responseTime,
          dataFormat: response.data?.code === 1 ? 'valid' : 'invalid',
          totalVideos: response.data?.total || 0,
          message: '连接测试成功'
        },
        message: '连接测试成功'
      });

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      // 记录错误
      await CollectSource.recordError(id, error.message);

      res.json({
        success: false,
        data: {
          status: 'offline',
          responseTime,
          error: error.message,
          message: '连接测试失败'
        },
        message: '连接测试失败'
      });
    }
  } catch (error) {
    logger.error('测试采集源连接失败:', error);
    res.status(500).json({
      success: false,
      message: '测试连接失败',
      error: error.message
    });
  }
};

// 获取采集源统计信息
const getCollectSourceStats = async (req, res) => {
  try {
    const { id } = req.params;
    const source = await CollectSource.findById(id);

    if (!source) {
      return res.status(404).json({
        success: false,
        message: '采集源不存在'
      });
    }

    // 这里可以添加更详细的统计逻辑
    const stats = {
      totalVideos: source.totalVideos || 0,
      collectedVideos: source.collectedVideos || 0,
      successRate: source.successRate || 0,
      responseTime: source.responseTime || 0,
      lastCheckTime: source.lastCheckTime,
      lastCollectTime: source.lastCollectTime,
      errorCount: source.errorCount || 0,
      status: source.status
    };

    res.json({
      success: true,
      data: stats,
      message: '获取统计信息成功'
    });
  } catch (error) {
    logger.error('获取采集源统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取统计信息失败',
      error: error.message
    });
  }
};

// 获取采集源的分类数据
const getCollectSourceCategories = async (req, res) => {
  try {
    const { id } = req.params;
    const source = await CollectSource.findById(id);

    if (!source) {
      return res.status(404).json({
        success: false,
        message: '采集源不存在'
      });
    }

    // 调用外部API获取分类数据
    const response = await axios({
      method: 'GET',
      url: source.url,
      params: { ac: 'list' },
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      timeout: 30000
    });

    if (response.data && response.data.code === 1) {
      res.json({
        success: true,
        data: response.data.class || [],
        message: '获取分类数据成功'
      });
    } else {
      res.json({
        success: false,
        message: '获取分类数据失败',
        data: []
      });
    }
  } catch (error) {
    logger.error('获取采集源分类失败:', error);
    res.status(500).json({
      success: false,
      message: '获取分类数据失败',
      error: error.message,
      data: []
    });
  }
};

// 获取采集源的视频数据
const getCollectSourceVideos = async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 20, categoryId } = req.query;

    const source = await CollectSource.findById(id);

    if (!source) {
      return res.status(404).json({
        success: false,
        message: '采集源不存在'
      });
    }

    // 构建请求参数
    const params = {
      ac: 'list',
      pg: page
    };

    if (categoryId) {
      params.t = categoryId;
    }

    // 调用外部API获取视频数据
    const response = await axios({
      method: 'GET',
      url: source.url,
      params,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      timeout: 30000
    });

    if (response.data && response.data.code === 1) {
      res.json({
        success: true,
        data: {
          page: parseInt(response.data.page) || 1,
          pagecount: parseInt(response.data.pagecount) || 1,
          total: parseInt(response.data.total) || 0,
          limit: parseInt(response.data.limit) || 20,
          list: response.data.list || []
        },
        message: '获取视频数据成功'
      });
    } else {
      res.json({
        success: false,
        message: '获取视频数据失败',
        data: {
          page: 1,
          pagecount: 1,
          total: 0,
          limit: 20,
          list: []
        }
      });
    }
  } catch (error) {
    logger.error('获取采集源视频失败:', error);
    res.status(500).json({
      success: false,
      message: '获取视频数据失败',
      error: error.message,
      data: {
        page: 1,
        pagecount: 1,
        total: 0,
        limit: 20,
        list: []
      }
    });
  }
};

// 获取视频详情 - 通过外部接口获取完整视频信息
const getVideoDetail = async (req, res) => {
  try {
    const { id } = req.params; // 采集源ID
    const { videoId } = req.query; // 视频ID

    if (!videoId) {
      return res.status(400).json({
        success: false,
        message: '视频ID不能为空'
      });
    }

    // 获取采集源信息
    const source = await CollectSource.findById(id);
    if (!source) {
      return res.status(404).json({
        success: false,
        message: '采集源不存在'
      });
    }

    // 检查API URL是否有效
    if (!source.url || source.url.trim() === '') {
      return res.status(400).json({
        success: false,
        message: '采集源API地址无效',
        debug: {
          sourceId: id,
          apiUrl: source.url
        }
      });
    }

    // 构建详情请求URL
    const detailUrl = `${source.url}?ac=videolist&ids=${videoId}`;

    logger.info('获取视频详情', {
      sourceId: id,
      videoId,
      sourceApiUrl: source.url,
      detailUrl: detailUrl
    });

    // 调用外部接口获取视频详情
    const response = await axios.get(detailUrl, {
      timeout: 30000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    if (response.data && response.data.code === 1) {
      const videoDetail = response.data.list && response.data.list[0];

      if (videoDetail) {
        res.json({
          success: true,
          data: {
            video: videoDetail,
            source: {
              id: source.id,
              name: source.name,
              type: source.type
            }
          }
        });
      } else {
        res.status(404).json({
          success: false,
          message: '视频不存在'
        });
      }
    } else {
      res.status(500).json({
        success: false,
        message: '获取视频详情失败：' + (response.data?.msg || '接口返回异常')
      });
    }

  } catch (error) {
    logger.error('获取视频详情失败:', {
      error: error.message,
      stack: error.stack,
      sourceId: req.params.id,
      videoId: req.query.videoId
    });

    // 根据错误类型返回更具体的错误信息
    let errorMessage = '获取视频详情失败';
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      errorMessage = '无法连接到采集源API';
    } else if (error.code === 'ETIMEDOUT') {
      errorMessage = '请求超时，请稍后重试';
    } else if (error.message.includes('Invalid URL')) {
      errorMessage = 'API地址格式错误';
    }

    res.status(500).json({
      success: false,
      message: errorMessage,
      error: error.message,
      debug: {
        errorCode: error.code,
        sourceId: req.params.id,
        videoId: req.query.videoId
      }
    });
  }
};

// 获取本地分类列表 - 用于采集配置中的分类映射
const getLocalCategories = async (req, res) => {
  try {
    // 获取所有活跃的本地分类
    const categories = await Category.findAll({
      status: 'active',
      includeEmpty: true // 包含没有视频的分类
    });

    res.json({
      success: true,
      data: categories.map(category => ({
        id: category.id,
        name: category.name,
        slug: category.slug,
        videoCount: category.videoCount || 0
      }))
    });

  } catch (error) {
    logger.error('获取本地分类失败:', error);
    res.status(500).json({
      success: false,
      message: '获取本地分类失败',
      error: error.message
    });
  }
};

// 开始采集任务
const startCollectTask = async (req, res) => {
  try {
    const { id } = req.params;
    const config = req.body;

    // 验证采集源是否存在
    const source = await CollectSource.findById(id);
    if (!source) {
      return res.status(404).json({
        success: false,
        message: '采集源不存在'
      });
    }

    // 验证配置参数
    const collectConfig = {
      // 分类映射：源分类ID -> 本地分类ID
      categoryMappings: config.categoryMappings || {},
      // 时间范围筛选
      timeRange: {
        startDate: config.timeRange?.startDate || null,
        endDate: config.timeRange?.endDate || null,
        timeType: config.timeRange?.timeType || 'all' // all, today, week, month, custom
      },
      // 采集选项
      collectImages: config.collectImages !== false,
      collectVideos: config.collectVideos !== false,
      updateExisting: config.updateExisting || false,
      // 采集参数
      maxPages: Math.min(Math.max(config.maxPages || 10, 1), 100),
      interval: Math.min(Math.max(config.interval || 1000, 100), 10000),
      timeout: Math.min(Math.max(config.timeout || 30000, 5000), 60000)
    };

    logger.info('开始采集任务', {
      sourceId: id,
      sourceName: source.name,
      config: collectConfig
    });

    // 创建采集日志记录
    const collectLog = await CollectLog.create({
      sourceId: id,
      sourceName: source.name,
      type: 'manual',
      status: 'running',
      collectParams: collectConfig,
      startTime: new Date(),
      createdBy: 1 // TODO: 从请求中获取用户ID
    });

    // 创建采集任务记录
    const taskName = `手动采集-${source.name}-${new Date().toLocaleString()}`;
    const collectTask = await CollectTask.create({
      sourceId: id,
      taskName: taskName,
      taskType: 'once',
      status: 'running',
      taskConfig: collectConfig,
      maxPages: collectConfig.maxPages,
      collectImages: collectConfig.collectImages ? 1 : 0,
      lastRunTime: new Date(),
      runCount: 1,
      createdBy: 1 // TODO: 从请求中获取用户ID
    });

    // 启动实际的采集处理
    try {
      await collectProcessor.startCollectTask(collectTask.id, collectConfig);

      res.json({
        success: true,
        message: '采集任务已启动',
        data: {
          taskId: collectTask.id,
          logId: collectLog.id,
          config: collectConfig,
          status: 'running'
        }
      });
    } catch (processorError) {
      // 如果启动采集处理器失败，更新任务状态
      await CollectTask.update(collectTask.id, { status: 'inactive' }); // 使用 'inactive' 代替 'failed'
      await CollectLog.update(collectLog.id, {
        status: 'failed',
        errorMessage: processorError.message,
        endTime: new Date()
      });

      throw processorError;
    }

  } catch (error) {
    logger.error('启动采集任务失败:', error);
    res.status(500).json({
      success: false,
      message: '启动采集任务失败',
      error: error.message
    });
  }
};

// 获取采集任务列表
const getCollectTasks = async (req, res) => {
  try {
    const { page = 1, limit = 20, sourceId, status, taskType } = req.query;

    const result = await CollectTask.findAll({
      page: parseInt(page),
      limit: parseInt(limit),
      sourceId: sourceId ? parseInt(sourceId) : null,
      status,
      taskType
    });

    res.json({
      code: 200,
      success: true,
      message: '获取成功',
      data: {
        list: result.tasks,
        page: result.pagination.page,
        limit: result.pagination.limit,
        total: result.pagination.total,
        totalPages: result.pagination.totalPages
      }
    });
  } catch (error) {
    logger.error('获取采集任务列表失败:', error);
    res.status(500).json({
      code: 500,
      success: false,
      message: '获取采集任务列表失败',
      error: error.message
    });
  }
};

// 获取采集任务详情
const getCollectTask = async (req, res) => {
  try {
    const { id } = req.params;
    const task = await CollectTask.findById(id);

    if (!task) {
      return res.status(404).json({
        success: false,
        message: '采集任务不存在'
      });
    }

    // 获取实时状态
    const runtimeStatus = collectProcessor.getTaskStatus(id);
    if (runtimeStatus) {
      task.runtimeStatus = runtimeStatus;
    }

    res.json({
      success: true,
      data: task
    });
  } catch (error) {
    logger.error('获取采集任务详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取采集任务详情失败',
      error: error.message
    });
  }
};

// 停止采集任务
const stopCollectTask = async (req, res) => {
  try {
    const { id } = req.params;
    logger.info('🛑 收到停止任务请求:', { taskId: id });

    const task = await CollectTask.findById(id);
    if (!task) {
      logger.error('❌ 任务不存在:', { taskId: id });
      return res.status(404).json({
        success: false,
        message: '采集任务不存在'
      });
    }

    logger.info('✅ 找到任务，开始停止:', { taskId: id, taskName: task.taskName, status: task.status });

    const result = await collectProcessor.stopTask(id);

    logger.info('🎯 停止任务结果:', { taskId: id, result });

    res.json({
      success: result.success,
      message: result.message,
      data: {
        taskId: id,
        stopped: result.success,
        previousStatus: task.status
      }
    });
  } catch (error) {
    logger.error('❌ 停止采集任务失败:', { taskId: req.params.id, error: error.message, stack: error.stack });
    res.status(500).json({
      success: false,
      message: '停止采集任务失败',
      error: error.message
    });
  }
};

// 获取任务实时状态
const getTaskStatus = async (req, res) => {
  try {
    const { id } = req.params;

    const task = await CollectTask.findById(id);
    if (!task) {
      return res.status(404).json({
        success: false,
        message: '采集任务不存在'
      });
    }

    const runtimeStatus = collectProcessor.getTaskStatus(id);

    res.json({
      success: true,
      data: {
        taskId: id,
        dbStatus: task.status,
        runtimeStatus: runtimeStatus || null,
        isRunning: !!runtimeStatus
      }
    });
  } catch (error) {
    logger.error('获取任务状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取任务状态失败',
      error: error.message
    });
  }
};

// 获取采集日志列表
const getCollectLogs = async (req, res) => {
  try {
    const { page = 1, limit = 20, sourceId, status, type } = req.query;

    const result = await CollectLog.findAll({
      page: parseInt(page),
      limit: parseInt(limit),
      sourceId: sourceId ? parseInt(sourceId) : null,
      status,
      type
    });

    res.json({
      code: 200,
      success: true,
      data: {
        list: result.logs,
        page: result.pagination.page,
        limit: result.pagination.limit,
        total: result.pagination.total,
        totalPages: result.pagination.totalPages
      }
    });
  } catch (error) {
    logger.error('获取采集日志列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取采集日志列表失败',
      error: error.message
    });
  }
};

// 获取采集日志详情
const getCollectLog = async (req, res) => {
  try {
    const { id } = req.params;
    const log = await CollectLog.findById(id);

    if (!log) {
      return res.status(404).json({
        success: false,
        message: '采集日志不存在'
      });
    }

    res.json({
      success: true,
      data: log
    });
  } catch (error) {
    logger.error('获取采集日志详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取采集日志详情失败',
      error: error.message
    });
  }
};

module.exports = {
  getCollectSources,
  getCollectSource,
  createCollectSource,
  updateCollectSource,
  deleteCollectSource,
  testCollectSource,
  getCollectSourceStats,
  getCollectSourceCategories,
  getCollectSourceVideos,
  getVideoDetail,
  getLocalCategories,
  startCollectTask,
  getCollectTasks,
  getCollectTask,
  stopCollectTask,
  getTaskStatus,
  getCollectLogs,
  getCollectLog
};

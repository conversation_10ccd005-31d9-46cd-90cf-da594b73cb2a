const axios = require('axios');
const logger = require('../utils/logger');

/**
 * 工具函数：获取数据（主备API）
 */
async function fetchWithFallback(url, timeout = 5000) {
  try {
    const response = await axios.get(url, {
      timeout,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    return response.data;
  } catch (error) {
    logger.warn(`Failed to fetch from ${url}:`, error.message);
    return null;
  }
}

/**
 * 工具函数：Unicode解码
 */
function decodeUnicode(str) {
  if (!str) return '';
  return str.replace(/\\u[\dA-F]{4}/gi, (match) => {
    return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16));
  });
}

/**
 * 获取平台中文名称 - 直接解码Unicode
 */
function getPlatformChineseName(originalName, platformId) {
  // 尝试解码原始名称
  const decodedName = decodeUnicode(originalName);
  if (decodedName && decodedName !== originalName) {
    return decodedName;
  }

  // 如果解码失败，使用原始名称
  return originalName || platformId;
}

/**
 * 获取直播平台列表
 */
const getPlatforms = async (req, res) => {
  try {
    // 获取主备API数据
    const primaryData = await fetchWithFallback('http://api.hclyz.com:81/mf/json.txt');
    const backupData = await fetchWithFallback('http://api.maiyoux.com:81/mf/json.txt');
    
    // 合并数据，主API优先
    const rawPlatforms = primaryData?.pingtai || backupData?.pingtai || [];
    
    // 数据处理
    const platforms = rawPlatforms
      .map(platform => {
        const platformId = platform.address.replace('json', '').replace('.txt', '');
        const originalName = platform.title;
        const chineseName = getPlatformChineseName(originalName, platformId);

        return {
          id: platformId,
          name: chineseName,
          originalName: decodeUnicode(originalName), // 保留原始名称
          icon: platform.xinimg,
          roomCount: parseInt(platform.Number) || 0,
          status: parseInt(platform.Number) > 0 ? 'online' : 'offline',
          featured: parseInt(platform.Number) > 100,
          viewerCount: parseInt(platform.Number) * Math.floor(Math.random() * 50 + 10), // 模拟总观看人数
          description: `专业${chineseName}平台，高清画质，互动体验极佳`
        };
      })
      .filter(platform =>
        // 过滤卫视直播和无房间的平台
        !platform.name.includes('卫视直播') &&
        platform.roomCount > 0
      )
      .sort((a, b) => b.roomCount - a.roomCount); // 按房间数排序
    
    res.json({
      success: true,
      data: platforms,
      total: platforms.length,
      timestamp: Date.now()
    });

  } catch (error) {
    logger.error('Failed to fetch platforms:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch platforms'
    });
  }
};

/**
 * 获取平台房间列表
 */
const getPlatformRooms = async (req, res) => {
  const { platform } = req.params;

  if (!platform) {
    return res.status(400).json({
      success: false,
      message: 'Platform parameter required'
    });
  }

  try {
    // 首先获取平台列表数据，以获取正确的中文名称
    const primaryPlatformData = await fetchWithFallback('http://api.hclyz.com:81/mf/json.txt');
    const backupPlatformData = await fetchWithFallback('http://api.maiyoux.com:81/mf/json.txt');

    const rawPlatforms = primaryPlatformData?.pingtai || backupPlatformData?.pingtai || [];

    // 查找当前平台的信息
    const currentPlatform = rawPlatforms.find(p => {
      const platformId = p.address.replace('json', '').replace('.txt', '');
      return platformId === platform;
    });

    // 获取平台中文名称
    let platformChineseName = platform; // 默认使用平台ID
    if (currentPlatform) {
      platformChineseName = getPlatformChineseName(currentPlatform.title, platform);
    } else {
      // 如果在平台列表中找不到，使用平台ID作为名称
      platformChineseName = platform;
    }

    // 构建平台数据URL
    const primaryUrl = `http://api.hclyz.com:81/mf/json${platform}.txt`;
    const backupUrl = `http://api.maiyoux.com:81/mf/json${platform}.txt`;

    // 获取房间数据
    const primaryData = await fetchWithFallback(primaryUrl);
    const backupData = await fetchWithFallback(backupUrl);

    const rawRooms = primaryData?.zhubo || backupData?.zhubo || [];
    
    // 处理房间数据 - 过滤RTMP流，只保留HTTP-FLV
    const rooms = rawRooms
      .filter(room => {
        // 基础数据检查
        if (!room.address || !room.img || !room.title) {
          return false;
        }
        
        // 🚫 移除RTMP流
        if (room.address.startsWith('rtmp://')) {
          return false;
        }
        
        // 🚫 移除特定域名的RTMP流（额外保险）
        if (room.address.includes('vihyvz.top') || 
            room.address.includes('zzlsnp.top')) {
          return false;
        }
        
        // ✅ 只保留HTTP-FLV流
        return room.address.startsWith('http://') && 
               (room.address.includes('.flv') || 
                room.address.includes('/live/'));
      })
      .map((room, index) => ({
        id: `${platform}_${index}`,
        title: decodeUnicode(room.title),
        streamerName: decodeUnicode(room.title),
        thumbnail: room.img,
        streamUrl: room.address,
        streamType: 'http-flv', // 统一为HTTP-FLV
        viewerCount: Math.floor(Math.random() * 5000) + 100,
        rating: (Math.random() * 2 + 8).toFixed(1),
        status: 'live',
        platform: platform
      }))
      .slice(0, 50); // 限制返回数量

    res.json({
      success: true,
      data: rooms,
      platform: platform,
      platformName: platformChineseName, // 使用从平台列表获取的中文名称
      total: rooms.length,
      filteredOut: rawRooms.length - rooms.length, // 显示过滤掉的数量
      timestamp: Date.now()
    });

  } catch (error) {
    logger.error('Failed to fetch rooms:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch rooms'
    });
  }
};

module.exports = {
  getPlatforms,
  getPlatformRooms
};

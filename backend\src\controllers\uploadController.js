const Video = require('../models/Video');
const Category = require('../models/Category');
const ApiKey = require('../models/ApiKey');
const logger = require('../utils/logger');
const { validationResult } = require('express-validator');

class UploadController {
  // 上传单个视频
  static async uploadVideo(req, res) {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      // 验证分类是否存在
      if (req.body.category_id) {
        const category = await Category.findById(req.body.category_id);
        if (!category) {
          return res.status(400).json({
            success: false,
            message: '指定的分类不存在'
          });
        }
      }

      const videoData = {
        title: req.body.title,
        description: req.body.description,
        coverUrl: req.body.cover_url,
        videoUrl: req.body.video_url,
        categoryId: req.body.category_id,
        tags: req.body.tags || [],
        duration: req.body.duration,
        rating: req.body.rating || 0,
        views: req.body.views || 0,
        status: req.body.status || 'active',
        featured: req.body.featured || false
      };

      logger.api('PYTHON_UPLOAD_VIDEO', { 
        title: videoData.title,
        apiKey: req.apiKey?.name || 'unknown'
      });

      const video = await Video.create(videoData);
      
      // 记录API使用
      if (req.apiKey) {
        await ApiKey.updateUsage(req.apiKey.id);
      }
      
      res.status(201).json({
        success: true,
        message: '影片上传成功',
        data: {
          id: video.id,
          title: video.title,
          status: video.status,
          uploadDate: video.createdAt
        }
      });
    } catch (error) {
      logger.error('Error in uploadVideo:', error);
      res.status(500).json({
        success: false,
        message: '上传视频失败'
      });
    }
  }

  // 批量上传视频
  static async batchUpload(req, res) {
    try {
      const { videos } = req.body;
      
      if (!Array.isArray(videos) || videos.length === 0) {
        return res.status(400).json({
          success: false,
          message: '请提供有效的视频数组'
        });
      }

      const results = {
        success: [],
        failed: [],
        total: videos.length
      };

      logger.api('PYTHON_BATCH_UPLOAD', { 
        count: videos.length,
        apiKey: req.apiKey?.name || 'unknown'
      });

      for (let i = 0; i < videos.length; i++) {
        const videoData = videos[i];
        
        try {
          // 验证必需字段
          if (!videoData.title || !videoData.video_url) {
            results.failed.push({
              index: i,
              data: videoData,
              error: '缺少必需字段: title 或 video_url'
            });
            continue;
          }

          // 验证分类
          if (videoData.category_id) {
            const category = await Category.findById(videoData.category_id);
            if (!category) {
              results.failed.push({
                index: i,
                data: videoData,
                error: '指定的分类不存在'
              });
              continue;
            }
          }

          const video = await Video.create({
            title: videoData.title,
            description: videoData.description,
            coverUrl: videoData.cover_url,
            videoUrl: videoData.video_url,
            categoryId: videoData.category_id,
            tags: videoData.tags || [],
            duration: videoData.duration,
            rating: videoData.rating || 0,
            views: videoData.views || 0,
            status: videoData.status || 'active',
            featured: videoData.featured || false
          });

          results.success.push({
            index: i,
            id: video.id,
            title: video.title
          });

        } catch (error) {
          results.failed.push({
            index: i,
            data: videoData,
            error: error.message
          });
        }
      }

      // 记录API使用
      if (req.apiKey) {
        await ApiKey.updateUsage(req.apiKey.id);
      }

      res.status(201).json({
        success: true,
        message: `批量上传完成: 成功 ${results.success.length} 个，失败 ${results.failed.length} 个`,
        data: results
      });

    } catch (error) {
      logger.error('Error in batchUpload:', error);
      res.status(500).json({
        success: false,
        message: '批量上传失败'
      });
    }
  }

  // 更新视频信息
  static async updateVideo(req, res) {
    try {
      const { id } = req.params;
      
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      // 检查视频是否存在
      const existingVideo = await Video.findById(id);
      if (!existingVideo) {
        return res.status(404).json({
          success: false,
          message: '视频不存在'
        });
      }

      const updateData = {};
      const allowedFields = [
        'title', 'description', 'cover_url', 'video_url', 
        'category_id', 'tags', 'duration', 'status', 'featured'
      ];

      allowedFields.forEach(field => {
        if (req.body[field] !== undefined) {
          const modelField = field === 'cover_url' ? 'coverUrl' :
                           field === 'video_url' ? 'videoUrl' :
                           field === 'category_id' ? 'categoryId' : field;
          updateData[modelField] = req.body[field];
        }
      });

      // 验证分类
      if (updateData.categoryId) {
        const category = await Category.findById(updateData.categoryId);
        if (!category) {
          return res.status(400).json({
            success: false,
            message: '指定的分类不存在'
          });
        }
      }

      logger.api('PYTHON_UPDATE_VIDEO', { 
        id,
        apiKey: req.apiKey?.name || 'unknown'
      });

      const updatedVideo = await Video.update(id, updateData);
      
      // 记录API使用
      if (req.apiKey) {
        await ApiKey.updateUsage(req.apiKey.id);
      }

      res.json({
        success: true,
        message: '视频更新成功',
        data: updatedVideo.toJSON()
      });

    } catch (error) {
      logger.error('Error in updateVideo:', error);
      res.status(500).json({
        success: false,
        message: '更新视频失败'
      });
    }
  }

  // 删除视频
  static async deleteVideo(req, res) {
    try {
      const { id } = req.params;

      // 检查视频是否存在
      const video = await Video.findById(id);
      if (!video) {
        return res.status(404).json({
          success: false,
          message: '视频不存在'
        });
      }

      logger.api('PYTHON_DELETE_VIDEO', { 
        id,
        title: video.title,
        apiKey: req.apiKey?.name || 'unknown'
      });

      await Video.delete(id);
      
      // 记录API使用
      if (req.apiKey) {
        await ApiKey.updateUsage(req.apiKey.id);
      }

      res.json({
        success: true,
        message: '视频删除成功'
      });

    } catch (error) {
      logger.error('Error in deleteVideo:', error);
      res.status(500).json({
        success: false,
        message: '删除视频失败'
      });
    }
  }

  // 获取上传统计
  static async getUploadStats(req, res) {
    try {
      // 这里可以添加统计逻辑
      const stats = {
        totalUploads: 0,
        todayUploads: 0,
        successRate: 100,
        apiUsage: {
          keyName: req.apiKey?.name || 'unknown',
          usageCount: req.apiKey?.usageCount || 0,
          lastUsed: req.apiKey?.lastUsedAt || null
        }
      };

      logger.api('PYTHON_GET_STATS', { 
        apiKey: req.apiKey?.name || 'unknown'
      });

      // 记录API使用
      if (req.apiKey) {
        await ApiKey.updateUsage(req.apiKey.id);
      }

      res.json({
        success: true,
        data: stats
      });

    } catch (error) {
      logger.error('Error in getUploadStats:', error);
      res.status(500).json({
        success: false,
        message: '获取统计信息失败'
      });
    }
  }
}

module.exports = UploadController;

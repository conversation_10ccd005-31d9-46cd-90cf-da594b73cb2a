const Video = require('../models/Video');
const logger = require('../utils/logger');
const redis = require('../config/redis');

class VideosController {
  // 获取视频列表
  static async getVideos(req, res) {
    try {
      const options = {
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 24,
        category: req.query.category ? parseInt(req.query.category) : null,
        tag: req.query.tag,
        search: req.query.search,
        sort: req.query.sort || 'latest',
        status: req.query.status || 'active'
      };

      logger.api('GET_VIDEOS', options);

      // 使用真实数据库数据
      const result = await Video.findAll(options);

      res.json({
        success: true,
        data: {
          videos: result.videos.map(video => video.toJSON()),
          pagination: result.pagination
        }
      });
    } catch (error) {
      logger.error('Error in getVideos:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '获取视频列表失败'
        }
      });
    }
  }

  // 获取视频详情
  static async getVideoById(req, res) {
    try {
      const videoId = parseInt(req.params.id);

      logger.api('GET_VIDEO_BY_ID', { videoId });

      // 查找视频
      const video = await Video.findById(videoId);

      if (!video) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'VIDEO_NOT_FOUND',
            message: '视频不存在'
          }
        });
      }

      // 获取推荐视频
      const recommendedVideos = await Video.getRecommended(videoId, 6);

      // 增加观看次数
      await Video.incrementViews(videoId);
      video.views += 1;

      res.json({
        success: true,
        data: {
          video: video.toJSON(),
          recommendedVideos: recommendedVideos.map(v => v.toJSON())
        }
      });
    } catch (error) {
      logger.error('Error in getVideoById:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '获取视频详情失败'
        }
      });
    }
  }

  // 增加观看次数
  static async incrementViews(req, res) {
    try {
      const videoId = parseInt(req.params.id);
      const clientIp = req.ip || req.connection.remoteAddress;

      // 使用真实数据库操作
      const newViews = await Video.incrementViews(videoId);

      logger.api('INCREMENT_VIEWS', { videoId, views: newViews, ip: clientIp });

      res.json({
        success: true,
        data: { views: newViews }
      });
    } catch (error) {
      logger.error('Error in incrementViews:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '更新观看次数失败'
        }
      });
    }
  }

  // 获取推荐视频
  static async getRecommendedVideos(req, res) {
    try {
      const videoId = parseInt(req.params.id);
      const limit = parseInt(req.query.limit) || 6;

      logger.api('GET_RECOMMENDED_VIDEOS', { videoId, limit });

      // 使用真实数据库数据
      const videos = await Video.getRecommended(videoId, limit);

      res.json({
        success: true,
        data: videos.map(video => video.toJSON())
      });
    } catch (error) {
      logger.error('Error in getRecommendedVideos:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '获取推荐视频失败'
        }
      });
    }
  }

  // 获取热门视频
  static async getHotVideos(req, res) {
    try {
      const limit = parseInt(req.query.limit) || 12;

      logger.api('GET_HOT_VIDEOS', { limit });

      // 使用真实数据库数据
      const videos = await Video.getHot(limit);

      res.json({
        success: true,
        data: videos.map(video => video.toJSON())
      });
    } catch (error) {
      logger.error('Error in getHotVideos:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '获取热门视频失败'
        }
      });
    }
  }

  // 获取最新视频
  static async getLatestVideos(req, res) {
    try {
      const limit = parseInt(req.query.limit) || 12;

      logger.api('GET_LATEST_VIDEOS', { limit });

      // 使用真实数据库数据
      const videos = await Video.getLatest(limit);

      res.json({
        success: true,
        data: videos.map(video => video.toJSON())
      });
    } catch (error) {
      logger.error('Error in getLatestVideos:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '获取最新视频失败'
        }
      });
    }
  }

  // 获取推荐视频
  static async getFeaturedVideos(req, res) {
    try {
      const limit = parseInt(req.query.limit) || 12;

      logger.api('GET_FEATURED_VIDEOS', { limit });

      // 使用真实数据库数据
      const videos = await Video.getFeatured(limit);

      res.json({
        success: true,
        data: videos.map(video => video.toJSON())
      });
    } catch (error) {
      logger.error('Error in getFeaturedVideos:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '获取推荐视频失败'
        }
      });
    }
  }

  // 获取视频统计信息
  static async getVideoStats(req, res) {
    try {
      logger.api('GET_VIDEO_STATS');

      // 获取视频统计信息
      const stats = await Video.getStats();

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error in getVideoStats:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'SERVER_ERROR',
          message: '获取视频统计信息失败'
        }
      });
    }
  }
}

module.exports = VideosController;

const ApiKey = require('../models/ApiKey');
const logger = require('../utils/logger');

/**
 * API密钥认证中间件
 * 验证Python上传API的Bearer Token
 */
const authenticateApiKey = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'MISSING_AUTHORIZATION',
          message: '缺少认证头部'
        }
      });
    }

    if (!authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_AUTHORIZATION_FORMAT',
          message: '认证头部格式错误，应为: Bearer {api_key}'
        }
      });
    }

    const apiKey = authHeader.substring(7); // 移除 "Bearer " 前缀
    
    if (!apiKey) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'MISSING_API_KEY',
          message: 'API密钥不能为空'
        }
      });
    }

    // 验证API密钥格式 (支持新旧格式)
    const validPrefixes = ['91jspg_upload_', 'jbl_upload_'];
    const hasValidPrefix = validPrefixes.some(prefix => apiKey.startsWith(prefix));

    if (!hasValidPrefix) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_API_KEY_FORMAT',
          message: 'API密钥格式错误'
        }
      });
    }

    // 从数据库验证API密钥
    const apiKeyRecord = await ApiKey.findByKey(apiKey);
    
    if (!apiKeyRecord) {
      logger.security('INVALID_API_KEY_ATTEMPT', { 
        apiKey: apiKey.substring(0, 20) + '...',
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      
      return res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_API_KEY',
          message: 'API密钥无效'
        }
      });
    }

    // 检查API密钥状态
    if (apiKeyRecord.status !== 'active') {
      logger.security('INACTIVE_API_KEY_ATTEMPT', { 
        apiKeyId: apiKeyRecord.id,
        status: apiKeyRecord.status,
        ip: req.ip
      });
      
      return res.status(401).json({
        success: false,
        error: {
          code: 'API_KEY_INACTIVE',
          message: 'API密钥已被禁用'
        }
      });
    }

    // 检查API密钥是否过期
    if (apiKeyRecord.expiresAt && new Date() > new Date(apiKeyRecord.expiresAt)) {
      logger.security('EXPIRED_API_KEY_ATTEMPT', { 
        apiKeyId: apiKeyRecord.id,
        expiresAt: apiKeyRecord.expiresAt,
        ip: req.ip
      });
      
      return res.status(401).json({
        success: false,
        error: {
          code: 'API_KEY_EXPIRED',
          message: 'API密钥已过期'
        }
      });
    }

    // 检查权限
    // 由于数据库中没有 permissions 字段，我们使用 ApiKey 模型的默认权限
    let permissions = ['upload']; // 默认权限

    // 如果数据库中有 permissions 字段，尝试解析
    if (apiKeyRecord.permissions) {
      try {
        if (typeof apiKeyRecord.permissions === 'string') {
          permissions = JSON.parse(apiKeyRecord.permissions);
        } else if (Array.isArray(apiKeyRecord.permissions)) {
          permissions = apiKeyRecord.permissions;
        }
      } catch (error) {
        logger.error('Error parsing API key permissions:', error);
        // 解析失败时使用默认权限
        permissions = ['upload'];
      }
    }

    if (!permissions.includes('upload') && !permissions.includes('*')) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足，需要upload权限'
        }
      });
    }

    // 将API密钥信息附加到请求对象
    req.apiKey = apiKeyRecord;
    
    logger.api('API_KEY_AUTH_SUCCESS', { 
      apiKeyId: apiKeyRecord.id,
      name: apiKeyRecord.name,
      ip: req.ip
    });

    next();

  } catch (error) {
    logger.error('Error in API key authentication:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'AUTHENTICATION_ERROR',
        message: '认证过程中发生错误'
      }
    });
  }
};



module.exports = {
  authenticateApiKey
};

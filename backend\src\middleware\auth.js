const Admin = require('../models/Admin');
const ApiKey = require('../models/ApiKey');
const logger = require('../utils/logger');

// 管理员认证中间件
const authenticateAdmin = async (req, res, next) => {
  try {
    let token = null;

    // 首先尝试从Authorization header获取token
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7); // 移除 "Bearer " 前缀
    }

    // 如果header中没有token，尝试从cookie获取
    if (!token && req.cookies && req.cookies.admin_token) {
      token = req.cookies.admin_token;
    }

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '未提供认证令牌'
      });
    }
    
    // 验证JWT令牌
    const admin = await Admin.verifyToken(token);
    
    if (!admin) {
      return res.status(401).json({
        success: false,
        message: '无效的认证令牌'
      });
    }

    // 将管理员信息添加到请求对象
    req.admin = admin;
    next();
  } catch (error) {
    logger.error('Admin authentication error:', error);
    res.status(401).json({
      success: false,
      message: '认证失败'
    });
  }
};

// API密钥认证中间件
const authenticateApiKey = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: '未提供API密钥'
      });
    }

    const apiKey = authHeader.substring(7); // 移除 "Bearer " 前缀
    
    // 验证API密钥
    const validApiKey = await ApiKey.verify(apiKey);
    
    if (!validApiKey) {
      return res.status(401).json({
        success: false,
        message: '无效的API密钥'
      });
    }

    // 将API密钥信息添加到请求对象
    req.apiKey = validApiKey;
    next();
  } catch (error) {
    logger.error('API key authentication error:', error);
    res.status(401).json({
      success: false,
      message: 'API密钥认证失败'
    });
  }
};

// 可选的管理员认证中间件（用于某些可选认证的接口）
const optionalAdminAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const admin = await Admin.verifyToken(token);
      
      if (admin) {
        req.admin = admin;
      }
    }
    
    next();
  } catch (error) {
    // 可选认证失败不阻止请求继续
    logger.warn('Optional admin authentication failed:', error);
    next();
  }
};

// 权限检查中间件
const requirePermission = (permission) => {
  return (req, res, next) => {
    // 目前简单实现，所有活跃管理员都有所有权限
    // 后续可以扩展为基于角色的权限系统
    if (!req.admin) {
      return res.status(401).json({
        success: false,
        message: '需要管理员权限'
      });
    }

    if (req.admin.status !== 'active') {
      return res.status(403).json({
        success: false,
        message: '账户已被禁用'
      });
    }

    next();
  };
};

// 超级管理员权限检查
const requireSuperAdmin = (req, res, next) => {
  if (!req.admin) {
    return res.status(401).json({
      success: false,
      message: '需要管理员权限'
    });
  }

  // 这里可以添加超级管理员的判断逻辑
  // 目前简单实现为ID为1的管理员为超级管理员
  if (req.admin.id !== 1) {
    return res.status(403).json({
      success: false,
      message: '需要超级管理员权限'
    });
  }

  next();
};

// 记录API使用情况的中间件
const logApiUsage = async (req, res, next) => {
  const startTime = Date.now();
  
  // 保存原始的res.json方法
  const originalJson = res.json;
  
  // 重写res.json方法以记录响应状态
  res.json = function(data) {
    const responseTime = Date.now() - startTime;
    
    // 异步记录API使用情况
    setImmediate(async () => {
      try {
        if (req.apiKey) {
          // 记录API密钥使用情况
          const logData = {
            api_key_id: req.apiKey.id,
            endpoint: req.originalUrl,
            method: req.method,
            ip_address: req.ip || req.connection.remoteAddress,
            user_agent: req.get('User-Agent'),
            response_status: res.statusCode,
            response_time: responseTime
          };

          // 这里可以添加记录到数据库的逻辑
          logger.info('API usage logged:', logData);
        }
      } catch (error) {
        logger.error('Error logging API usage:', error);
      }
    });
    
    // 调用原始的json方法
    return originalJson.call(this, data);
  };
  
  next();
};



module.exports = {
  authenticateAdmin,
  authenticateApiKey,
  optionalAdminAuth,
  requirePermission,
  requireSuperAdmin,
  logApiUsage
};

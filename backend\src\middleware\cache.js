const redis = require('../config/redis');
const logger = require('../utils/logger');

// 缓存中间件
const cache = (ttl = 300) => {
  return async (req, res, next) => {
    // 只缓存GET请求
    if (req.method !== 'GET') {
      return next();
    }

    // 生成缓存键
    const cacheKey = `cache:${req.originalUrl}`;

    try {
      // 尝试从缓存获取数据
      const cachedData = await redis.get(cacheKey);
      
      if (cachedData) {
        logger.cache('HIT', cacheKey);
        
        // 设置缓存头部
        res.set({
          'X-Cache': 'HIT',
          'Cache-Control': `public, max-age=${ttl}, s-maxage=${ttl}`,
          'ETag': `"${Buffer.from(JSON.stringify(cachedData)).toString('base64').slice(0, 16)}"`,
          'Last-Modified': new Date(Date.now() - ttl * 1000).toUTCString()
        });
        
        return res.json(cachedData);
      }

      // 缓存未命中，继续处理请求
      logger.cache('MISS', cacheKey);
      
      // 重写res.json方法以缓存响应
      const originalJson = res.json;
      res.json = function(data) {
        // 只缓存成功的响应
        if (data && data.success !== false) {
          redis.set(cacheKey, data, ttl).catch(error => {
            logger.error('Cache set error:', error);
          });
          
          logger.cache('SET', cacheKey, { ttl });
        }
        
        // 设置缓存头部
        res.set({
          'X-Cache': 'MISS',
          'Cache-Control': `public, max-age=${ttl}, s-maxage=${ttl}`,
          'ETag': `"${Buffer.from(JSON.stringify(data)).toString('base64').slice(0, 16)}"`,
          'Last-Modified': new Date().toUTCString()
        });
        
        // 调用原始的json方法
        return originalJson.call(this, data);
      };

      next();
    } catch (error) {
      logger.error('Cache middleware error:', error);
      // 缓存错误不应该影响正常请求
      next();
    }
  };
};

// 清除缓存的辅助函数
const clearCache = async (pattern = '*') => {
  try {
    const keys = await redis.keys(`cache:${pattern}`);
    
    if (keys.length > 0) {
      await Promise.all(keys.map(key => redis.del(key)));
      logger.cache('CLEAR', pattern, { count: keys.length });
    }
    
    return keys.length;
  } catch (error) {
    logger.error('Clear cache error:', error);
    return 0;
  }
};

// 清除特定路径的缓存
const clearPathCache = async (path) => {
  try {
    const pattern = `cache:${path}*`;
    return await clearCache(pattern);
  } catch (error) {
    logger.error('Clear path cache error:', error);
    return 0;
  }
};

module.exports = {
  cache,
  clearCache,
  clearPathCache
};

const logger = require('../utils/logger');

// 错误处理中间件
const errorHandler = (error, req, res, next) => {
  // 详细记录错误日志
  logger.error('❌ 未处理的错误:', {
    timestamp: new Date().toISOString(),
    error: {
      name: error.name,
      message: error.message,
      code: error.code || 'N/A',
      errno: error.errno || 'N/A',
      sqlState: error.sqlState || 'N/A'
    },
    request: {
      url: req.url,
      method: req.method,
      headers: req.headers,
      query: req.query,
      body: req.body && Object.keys(req.body).length > 0 ? req.body : 'empty',
      params: req.params && Object.keys(req.params).length > 0 ? req.params : 'none'
    },
    client: {
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      referer: req.get('Referer') || 'none'
    }
  });

  // 记录完整的错误堆栈
  logger.error('错误堆栈:', error.stack);

  // 默认错误响应
  let statusCode = 500;
  let errorResponse = {
    success: false,
    error: {
      code: 'SERVER_ERROR',
      message: '服务器内部错误'
    }
  };

  // 根据错误类型设置不同的响应
  if (error.name === 'ValidationError') {
    statusCode = 400;
    errorResponse.error = {
      code: 'VALIDATION_ERROR',
      message: '数据验证失败',
      details: error.details
    };
  } else if (error.name === 'UnauthorizedError') {
    statusCode = 401;
    errorResponse.error = {
      code: 'UNAUTHORIZED',
      message: '未授权访问'
    };
  } else if (error.name === 'ForbiddenError') {
    statusCode = 403;
    errorResponse.error = {
      code: 'FORBIDDEN',
      message: '禁止访问'
    };
  } else if (error.name === 'NotFoundError') {
    statusCode = 404;
    errorResponse.error = {
      code: 'NOT_FOUND',
      message: '资源不存在'
    };
  } else if (error.code === '23505') { // PostgreSQL unique violation
    statusCode = 409;
    errorResponse.error = {
      code: 'DUPLICATE_ENTRY',
      message: '数据已存在'
    };
  } else if (error.code === '23503') { // PostgreSQL foreign key violation
    statusCode = 400;
    errorResponse.error = {
      code: 'FOREIGN_KEY_VIOLATION',
      message: '关联数据不存在'
    };
  } else if (error.code === '23502') { // PostgreSQL not null violation
    statusCode = 400;
    errorResponse.error = {
      code: 'REQUIRED_FIELD_MISSING',
      message: '必填字段缺失'
    };
  }

  // 开发环境返回详细错误信息
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error.stack = error.stack;
    errorResponse.error.details = error.details || error.message;
  }

  res.status(statusCode).json(errorResponse);
};

module.exports = errorHandler;

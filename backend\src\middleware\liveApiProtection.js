const logger = require('../utils/logger');
const { getAllowedDomains, isSuspiciousUserAgent } = require('../config/security');

/**
 * 直播API防护中间件
 * 只对直播相关API进行防护，其他API不受影响
 */
const liveApiProtection = (req, res, next) => {
  // 对直播和代理相关API进行防护
  if (!req.path.includes('/live/') && !req.path.includes('/proxy/')) {
    return next(); // 其他API不受影响
  }

  // 环境检测
  const isDev = process.env.NODE_ENV === 'development';
  const isLocalhost = req.get('host')?.includes('localhost');

  if (isDev || isLocalhost) {
    return next(); // 开发环境跳过检查
  }

  // 生产环境防护逻辑
  try {
    protectLiveAPI(req, res, next);
  } catch (error) {
    logger.error('Live API protection error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

function protectLiveAPI(req, res, next) {
  const referer = req.get('referer');
  const origin = req.get('origin');
  const userAgent = req.get('user-agent');
  const clientIP = req.ip || req.connection.remoteAddress;

  // 记录访问日志（用于监控，不限制访问）
  logger.info('Live/Proxy API access:', {
    ip: clientIP,
    path: req.path,
    userAgent: userAgent?.substring(0, 100), // 截取前100字符避免日志过长
    referer: referer?.substring(0, 100)
  });

  // 1. 域名白名单检查
  const allowedDomains = getAllowedDomains();

  // 2. Referer检查（主要防护）
  const hasValidReferer = allowedDomains.some(domain => 
    referer?.startsWith(domain)
  );

  // 3. Origin检查
  const hasValidOrigin = !origin || allowedDomains.includes(origin);



  // 5. 排除明显的API工具和爬虫
  const suspiciousUserAgent = isSuspiciousUserAgent(userAgent);

  // 6. 检查是否为直接API调用（无Referer通常表示程序调用）
  const isDirectAPICall = !referer && userAgent && suspiciousUserAgent;

  // 对代理API进行更严格的检查
  if (req.path.includes('/proxy/')) {
    // 代理API必须有有效的Referer
    if (!hasValidReferer) {
      logger.warn('Proxy API access denied - no valid referer:', {
        ip: req.ip,
        userAgent,
        referer,
        origin,
        path: req.path
      });

      return res.status(403).json({
        success: false,
        message: 'Proxy access denied'
      });
    }
  }

  // 生产环境严格模式：必须有有效的Referer
  const isProduction = process.env.NODE_ENV === 'production';
  const isDevelopment = process.env.NODE_ENV === 'development';

  if (isProduction) {
    // 生产环境：严格要求Referer验证
    if (!hasValidReferer) {
      logger.warn('Live API access denied in production - no valid referer:', {
        ip: clientIP,
        userAgent,
        referer,
        origin,
        path: req.path,
        allowedDomains
      });

      return res.status(403).json({
        success: false,
        message: 'Access denied - Invalid referer'
      });
    }
  } else if (isDevelopment) {
    // 开发环境：相对宽松，允许直接访问以便调试
    logger.info('Live API access in development mode:', {
      ip: clientIP,
      userAgent: userAgent?.substring(0, 50),
      referer: referer?.substring(0, 50),
      path: req.path,
      note: 'Development mode - protection relaxed'
    });

    // 开发环境下只阻止明显的恶意工具
    if (suspiciousUserAgent && userAgent?.includes('bot')) {
      logger.warn('Live API access denied in development - bot detected:', {
        ip: clientIP,
        userAgent,
        path: req.path
      });

      return res.status(403).json({
        success: false,
        message: 'Access denied - Bots not allowed'
      });
    }
  } else {
    // 其他环境：使用原有逻辑
    if ((!hasValidReferer && !hasValidOrigin) || isDirectAPICall) {
      logger.warn('Live API access denied:', {
        ip: clientIP,
        userAgent,
        referer,
        origin,
        path: req.path
      });

      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
  }

  // 记录成功访问
  logger.info('Live API access granted:', {
    ip: clientIP,
    path: req.path,
    referer: referer?.substring(0, 50),
    environment: process.env.NODE_ENV
  });

  next();
}

module.exports = liveApiProtection;

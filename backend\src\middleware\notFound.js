const logger = require('../utils/logger');

// 404处理中间件
const notFound = (req, res, next) => {
  logger.warn('Route not found:', {
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: '请求的资源不存在',
      path: req.url,
      method: req.method
    }
  });
};

module.exports = notFound;

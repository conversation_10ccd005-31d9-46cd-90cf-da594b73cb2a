const { body, param } = require('express-validator');

/**
 * 验证上传视频数据
 */
const validateUploadVideo = [
  body('title')
    .notEmpty()
    .withMessage('视频标题不能为空')
    .isLength({ min: 1, max: 200 })
    .withMessage('视频标题长度应在1-200字符之间'),
    
  body('description')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('视频描述长度不能超过2000字符'),
    
  body('video_url')
    .notEmpty()
    .withMessage('视频URL不能为空')
    .isURL()
    .withMessage('视频URL格式无效'),
    
  body('cover_url')
    .optional()
    .isURL()
    .withMessage('封面URL格式无效'),
    
  body('category_id')
    .notEmpty()
    .withMessage('分类ID不能为空')
    .isInt({ min: 1 })
    .withMessage('分类ID必须是正整数'),
    
  body('tags')
    .optional()
    .isArray()
    .withMessage('标签必须是数组格式'),
    
  body('tags.*')
    .optional()
    .isString()
    .withMessage('标签必须是字符串')
    .isLength({ min: 1, max: 50 })
    .withMessage('标签长度应在1-50字符之间'),
    
  body('duration')
    .optional()
    .matches(/^\d{2}:\d{2}:\d{2}$/)
    .withMessage('视频时长格式必须是HH:MM:SS'),

  body('rating')
    .optional()
    .isFloat({ min: 0, max: 10 })
    .withMessage('评分必须是0-10之间的数字'),

  body('views')
    .optional()
    .isInt({ min: 0 })
    .withMessage('观看数量必须是非负整数'),

  body('status')
    .optional()
    .isIn(['active', 'inactive', 'pending'])
    .withMessage('状态必须是: active, inactive, pending 之一'),

  body('featured')
    .optional()
    .isBoolean()
    .withMessage('推荐标志必须是布尔值')
];

/**
 * 验证批量上传数据
 */
const validateBatchUpload = [
  body('videos')
    .isArray({ min: 1, max: 50 })
    .withMessage('videos必须是数组，且包含1-50个元素'),
    
  body('videos.*.title')
    .notEmpty()
    .withMessage('每个视频的标题不能为空')
    .isLength({ min: 1, max: 200 })
    .withMessage('视频标题长度应在1-200字符之间'),
    
  body('videos.*.video_url')
    .notEmpty()
    .withMessage('每个视频的URL不能为空')
    .isURL()
    .withMessage('视频URL格式无效'),
    
  body('videos.*.category_id')
    .notEmpty()
    .withMessage('每个视频的分类ID不能为空')
    .isInt({ min: 1 })
    .withMessage('分类ID必须是正整数')
];

/**
 * 验证更新视频数据
 */
const validateUpdateVideo = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('视频ID必须是正整数'),
    
  body('title')
    .optional()
    .isLength({ min: 1, max: 200 })
    .withMessage('视频标题长度应在1-200字符之间'),
    
  body('description')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('视频描述长度不能超过2000字符'),
    
  body('video_url')
    .optional()
    .isURL()
    .withMessage('视频URL格式无效'),
    
  body('cover_url')
    .optional()
    .isURL()
    .withMessage('封面URL格式无效'),
    
  body('category_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('分类ID必须是正整数'),
    
  body('tags')
    .optional()
    .isArray()
    .withMessage('标签必须是数组格式'),
    
  body('duration')
    .optional()
    .matches(/^\d{2}:\d{2}:\d{2}$/)
    .withMessage('视频时长格式必须是HH:MM:SS'),

  body('rating')
    .optional()
    .isFloat({ min: 0, max: 10 })
    .withMessage('评分必须是0-10之间的数字'),

  body('views')
    .optional()
    .isInt({ min: 0 })
    .withMessage('观看数量必须是非负整数'),

  body('status')
    .optional()
    .isIn(['active', 'inactive', 'pending'])
    .withMessage('状态必须是: active, inactive, pending 之一'),

  body('featured')
    .optional()
    .isBoolean()
    .withMessage('推荐标志必须是布尔值')
];

/**
 * 验证删除视频参数
 */
const validateDeleteVideo = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('视频ID必须是正整数')
];

module.exports = {
  validateUploadVideo,
  validateBatchUpload,
  validateUpdateVideo,
  validateDeleteVideo
};

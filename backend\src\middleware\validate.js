const { validationResult } = require('express-validator');
const logger = require('../utils/logger');

// 验证中间件
const validate = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.param,
      message: error.msg,
      value: error.value
    }));

    logger.security('VALIDATION_ERROR', {
      url: req.url,
      method: req.method,
      errors: errorMessages,
      ip: req.ip
    });

    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: '请求参数验证失败',
        details: errorMessages
      }
    });
  }

  next();
};

module.exports = validate;

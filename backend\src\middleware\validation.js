const { body, param, query } = require('express-validator');

// 管理员登录验证
const validateAdminLogin = [
  body('username')
    .notEmpty()
    .withMessage('用户名不能为空')
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),
  
  body('password')
    .notEmpty()
    .withMessage('密码不能为空')
    .isLength({ min: 6 })
    .withMessage('密码长度至少6个字符')
];

// 创建管理员验证
const validateCreateAdmin = [
  body('name')
    .notEmpty()
    .withMessage('姓名不能为空')
    .isLength({ min: 2, max: 100 })
    .withMessage('姓名长度必须在2-100个字符之间'),
  
  body('username')
    .notEmpty()
    .withMessage('用户名不能为空')
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),
  
  body('password')
    .notEmpty()
    .withMessage('密码不能为空')
    .isLength({ min: 8 })
    .withMessage('密码长度至少8个字符')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密码必须包含至少一个小写字母、一个大写字母和一个数字'),
  
  body('email')
    .optional()
    .isEmail()
    .withMessage('邮箱格式不正确')
    .normalizeEmail(),
  
  body('status')
    .optional()
    .isIn(['active', 'inactive'])
    .withMessage('状态必须是active或inactive')
];

// 更新管理员验证
const validateUpdateAdmin = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('管理员ID必须是正整数'),
  
  body('name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('姓名长度必须在2-100个字符之间'),
  
  body('email')
    .optional()
    .isEmail()
    .withMessage('邮箱格式不正确')
    .normalizeEmail(),
  
  body('status')
    .optional()
    .isIn(['active', 'inactive'])
    .withMessage('状态必须是active或inactive'),
  
  body('password')
    .optional()
    .isLength({ min: 8 })
    .withMessage('密码长度至少8个字符')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密码必须包含至少一个小写字母、一个大写字母和一个数字')
];

// 修改密码验证
const validateChangePassword = [
  body('currentPassword')
    .notEmpty()
    .withMessage('当前密码不能为空'),
  
  body('newPassword')
    .notEmpty()
    .withMessage('新密码不能为空')
    .isLength({ min: 8 })
    .withMessage('新密码长度至少8个字符')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('新密码必须包含至少一个小写字母、一个大写字母和一个数字'),
  
  body('confirmPassword')
    .notEmpty()
    .withMessage('确认密码不能为空')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('确认密码与新密码不匹配');
      }
      return true;
    })
];

// 分页验证
const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间')
];

// ID参数验证
const validateId = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID必须是正整数')
];

// API密钥创建验证
const validateCreateApiKey = [
  body('name')
    .notEmpty()
    .withMessage('API密钥名称不能为空')
    .isLength({ min: 2, max: 100 })
    .withMessage('API密钥名称长度必须在2-100个字符之间'),
  
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('描述长度不能超过500个字符'),
  
  body('status')
    .optional()
    .isIn(['active', 'inactive'])
    .withMessage('状态必须是active或inactive')
];

// 视频创建验证
const validateCreateVideo = [
  body('title')
    .notEmpty()
    .withMessage('视频标题不能为空')
    .isLength({ min: 1, max: 255 })
    .withMessage('视频标题长度必须在1-255个字符之间'),
  
  body('description')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('视频描述长度不能超过2000个字符'),
  
  body('cover_url')
    .optional()
    .isURL()
    .withMessage('封面URL格式不正确'),
  
  body('video_url')
    .optional()
    .isURL()
    .withMessage('视频URL格式不正确'),
  
  body('category_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('分类ID必须是正整数'),
  
  body('tags')
    .optional()
    .isArray()
    .withMessage('标签必须是数组格式'),
  
  body('duration')
    .optional()
    .matches(/^\d{2}:\d{2}:\d{2}$/)
    .withMessage('时长格式必须是HH:MM:SS'),
  
  body('status')
    .optional()
    .isIn(['active', 'inactive', 'pending'])
    .withMessage('状态必须是active、inactive或pending'),
  
  body('featured')
    .optional()
    .isBoolean()
    .withMessage('推荐状态必须是布尔值')
];

// 分类创建验证
const validateCreateCategory = [
  body('name')
    .notEmpty()
    .withMessage('分类名称不能为空')
    .isLength({ min: 1, max: 100 })
    .withMessage('分类名称长度必须在1-100个字符之间'),
  
  body('slug')
    .optional()
    .matches(/^[a-z0-9-]+$/)
    .withMessage('分类别名只能包含小写字母、数字和连字符'),
  
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('分类描述长度不能超过500个字符'),
  
  body('cover_url')
    .optional()
    .isURL()
    .withMessage('封面URL格式不正确'),
  
  body('sort_order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('排序值必须是非负整数'),
  
  body('status')
    .optional()
    .isIn(['active', 'inactive'])
    .withMessage('状态必须是active或inactive')
];

// 搜索验证
const validateSearch = [
  query('q')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('搜索关键词长度必须在1-100个字符之间'),
  
  query('category')
    .optional()
    .isInt({ min: 1 })
    .withMessage('分类ID必须是正整数'),
  
  query('tags')
    .optional()
    .isArray()
    .withMessage('标签必须是数组格式'),
  
  query('sort')
    .optional()
    .isIn(['latest', 'popular', 'rating', 'views'])
    .withMessage('排序方式必须是latest、popular、rating或views')
];

module.exports = {
  validateAdminLogin,
  validateCreateAdmin,
  validateUpdateAdmin,
  validateChangePassword,
  validatePagination,
  validateId,
  validateCreateApiKey,
  validateCreateVideo,
  validateCreateCategory,
  validateSearch
};

const database = require('../config/database');
const logger = require('../utils/logger');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

class Admin {
  constructor(data = {}) {
    this.id = data.id;
    this.name = data.name;
    this.username = data.username;
    this.passwordHash = data.password_hash || data.passwordHash;
    this.email = data.email;
    this.status = data.status || 'active';
    this.lastLoginAt = data.last_login_at || data.lastLoginAt;
    this.loginCount = data.login_count || data.loginCount || 0;
    this.createdAt = data.created_at || data.createdAt;
    this.updatedAt = data.updated_at || data.updatedAt;
  }

  // 创建管理员
  static async create(adminData) {
    try {
      // 检查用户名是否已存在
      const existingAdmin = await this.findByUsername(adminData.username);
      if (existingAdmin) {
        throw new Error('用户名已存在');
      }

      // 加密密码
      const passwordHash = await bcrypt.hash(adminData.password, parseInt(process.env.BCRYPT_ROUNDS) || 12);
      
      const query = `
        INSERT INTO admins (name, username, password_hash, email, status)
        VALUES (?, ?, ?, ?, ?)
      `;
      
      const values = [
        adminData.name,
        adminData.username,
        passwordHash,
        adminData.email,
        adminData.status || 'active'
      ];

      const result = await database.query(query, values);
      const insertId = result.insertId;
      logger.db('CREATE', 'admins', { id: insertId });
      logger.security('ADMIN_CREATED', { username: adminData.username, id: insertId });

      // 获取创建的管理员信息
      return await this.findById(insertId);
    } catch (error) {
      logger.error('Error creating admin:', error);
      throw error;
    }
  }

  // 根据ID查找管理员
  static async findById(id) {
    try {
      const query = 'SELECT * FROM admins WHERE id = ?';
      const result = await database.query(query, [id]);
      
      if (result.rows.length === 0) {
        return null;
      }
      
      return new Admin(result.rows[0]);
    } catch (error) {
      logger.error('Error finding admin by ID:', error);
      throw error;
    }
  }

  // 根据用户名查找管理员
  static async findByUsername(username) {
    try {
      const query = 'SELECT * FROM admins WHERE username = ?';
      const result = await database.query(query, [username]);
      
      if (result.rows.length === 0) {
        return null;
      }
      
      return new Admin(result.rows[0]);
    } catch (error) {
      logger.error('Error finding admin by username:', error);
      throw error;
    }
  }

  // 获取所有管理员
  static async findAll(options = {}) {
    try {
      let query = 'SELECT * FROM admins';
      const conditions = [];
      const values = [];

      // 状态筛选
      if (options.status) {
        conditions.push('status = ?');
        values.push(options.status);
      }

      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }

      // 排序
      query += ' ORDER BY created_at DESC';

      // 分页
      if (options.limit) {
        query += ' LIMIT ?';
        values.push(parseInt(options.limit));
        
        if (options.offset) {
          query += ' OFFSET ?';
          values.push(parseInt(options.offset));
        }
      }

      const result = await database.query(query, values);
      return result.rows.map(row => new Admin(row));
    } catch (error) {
      logger.error('Error finding admins:', error);
      throw error;
    }
  }

  // 验证密码
  async verifyPassword(password) {
    try {
      return await bcrypt.compare(password, this.passwordHash);
    } catch (error) {
      logger.error('Error verifying password:', error);
      return false;
    }
  }

  // 管理员登录
  static async login(username, password) {
    try {
      // 查找管理员
      const admin = await this.findByUsername(username);
      if (!admin) {
        logger.security('LOGIN_FAILED', { username, reason: 'user_not_found' });
        return { success: false, message: '用户名或密码错误' };
      }

      // 检查状态
      if (admin.status !== 'active') {
        logger.security('LOGIN_FAILED', { username, reason: 'account_disabled' });
        return { success: false, message: '账户已被禁用' };
      }

      // 验证密码
      const isPasswordValid = await admin.verifyPassword(password);
      if (!isPasswordValid) {
        logger.security('LOGIN_FAILED', { username, reason: 'invalid_password' });
        return { success: false, message: '用户名或密码错误' };
      }

      // 更新登录信息
      await admin.updateLoginInfo();

      // 生成JWT令牌
      const token = admin.generateToken();

      logger.security('LOGIN_SUCCESS', { username, id: admin.id });
      
      return {
        success: true,
        message: '登录成功',
        token,
        admin: admin.toJSON()
      };
    } catch (error) {
      logger.error('Error during admin login:', error);
      return { success: false, message: '登录失败，请稍后重试' };
    }
  }

  // 更新登录信息
  async updateLoginInfo() {
    try {
      const query = `
        UPDATE admins 
        SET last_login_at = CURRENT_TIMESTAMP, login_count = login_count + 1 
        WHERE id = ?
      `;
      
      await database.query(query, [this.id]);
      this.lastLoginAt = new Date();
      this.loginCount += 1;
    } catch (error) {
      logger.error('Error updating login info:', error);
    }
  }

  // 生成JWT令牌
  generateToken() {
    const payload = {
      id: this.id,
      username: this.username,
      type: 'admin'
    };

    return jwt.sign(
      payload,
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );
  }

  // 验证JWT令牌
  static async verifyToken(token) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      if (decoded.type !== 'admin') {
        return null;
      }

      const admin = await this.findById(decoded.id);
      if (!admin || admin.status !== 'active') {
        return null;
      }

      return admin;
    } catch (error) {
      logger.error('Error verifying token:', error);
      return null;
    }
  }

  // 更新管理员信息
  async update(updateData) {
    try {
      const fields = [];
      const values = [];

      // 可更新的字段
      const allowedFields = ['name', 'email', 'status'];
      
      allowedFields.forEach(field => {
        if (updateData[field] !== undefined) {
          fields.push(`${field} = ?`);
          values.push(updateData[field]);
        }
      });

      // 如果有新密码，加密后更新
      if (updateData.password) {
        const passwordHash = await bcrypt.hash(updateData.password, parseInt(process.env.BCRYPT_ROUNDS) || 12);
        fields.push('password_hash = ?');
        values.push(passwordHash);
      }

      if (fields.length === 0) {
        return this;
      }

      values.push(this.id);
      
      const query = `UPDATE admins SET ${fields.join(', ')} WHERE id = ?`;
      await database.query(query, values);
      
      logger.db('UPDATE', 'admins', { id: this.id });
      
      // 重新获取更新后的数据
      return await Admin.findById(this.id);
    } catch (error) {
      logger.error('Error updating admin:', error);
      throw error;
    }
  }

  // 删除管理员
  async delete() {
    try {
      const query = 'DELETE FROM admins WHERE id = ?';
      await database.query(query, [this.id]);
      
      logger.db('DELETE', 'admins', { id: this.id });
      logger.security('ADMIN_DELETED', { username: this.username, id: this.id });
      
      return true;
    } catch (error) {
      logger.error('Error deleting admin:', error);
      throw error;
    }
  }

  // 转换为JSON格式（隐藏敏感信息）
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      username: this.username,
      email: this.email,
      status: this.status,
      lastLoginAt: this.lastLoginAt,
      loginCount: this.loginCount,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

module.exports = Admin;

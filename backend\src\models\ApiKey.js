const database = require('../config/database');
const logger = require('../utils/logger');
const crypto = require('crypto');

class ApiKey {
  constructor(data = {}) {
    this.id = data.id;
    this.name = data.name;
    this.keyValue = data.key_value || data.keyValue;
    this.description = data.description;
    this.permissions = data.permissions || ['upload']; // 默认上传权限（硬编码，不存储在数据库）
    this.status = data.status || 'active';
    this.expiresAt = data.expires_at || data.expiresAt;
    this.lastUsedAt = data.last_used_at || data.lastUsedAt;
    this.usageCount = data.usage_count || data.usageCount || 0;
    this.createdAt = data.created_at || data.createdAt;
    this.updatedAt = data.updated_at || data.updatedAt;
  }

  // 生成API密钥
  static generateKey() {
    const timestamp = Date.now();
    const randomBytes = crypto.randomBytes(16).toString('hex');
    const prefix = process.env.API_KEY_PREFIX || 'jbl_upload_';
    return `${prefix}${timestamp}_${randomBytes}`;
  }

  // 创建API密钥
  static async create(keyData) {
    try {
      const keyValue = this.generateKey();

      const query = `
        INSERT INTO api_keys (name, key_value, description, status)
        VALUES (?, ?, ?, ?)
      `;

      const values = [
        keyData.name,
        keyValue,
        keyData.description,
        keyData.status || 'active'
      ];

      const result = await database.query(query, values);
      logger.db('CREATE', 'api_keys', { id: result.insertId });
      logger.security('API_KEY_CREATED', { name: keyData.name, id: result.insertId });

      // 获取创建的API密钥
      return await this.findById(result.insertId);
    } catch (error) {
      logger.error('Error creating API key:', error);
      throw error;
    }
  }

  // 根据ID查找API密钥
  static async findById(id) {
    try {
      const query = 'SELECT * FROM api_keys WHERE id = ?';
      const result = await database.query(query, [id]);

      if (result.rows.length === 0) {
        return null;
      }

      return new ApiKey(result.rows[0]);
    } catch (error) {
      logger.error('Error finding API key by ID:', error);
      throw error;
    }
  }

  // 根据密钥值查找API密钥
  static async findByKey(keyValue) {
    try {
      const query = 'SELECT * FROM api_keys WHERE key_value = ?';
      const result = await database.query(query, [keyValue]);

      if (result.rows.length === 0) {
        return null;
      }

      return new ApiKey(result.rows[0]);
    } catch (error) {
      logger.error('Error finding API key by value:', error);
      throw error;
    }
  }

  // 验证API密钥
  static async verify(keyValue) {
    try {
      // 检查密钥格式
      const prefix = process.env.API_KEY_PREFIX || 'jbl_upload_';
      if (!keyValue || !keyValue.startsWith(prefix)) {
        logger.security('INVALID_API_KEY_FORMAT', { keyValue });
        return null;
      }

      const apiKey = await this.findByKey(keyValue);
      
      if (!apiKey) {
        logger.security('API_KEY_NOT_FOUND', { keyValue });
        return null;
      }

      if (apiKey.status !== 'active') {
        logger.security('API_KEY_INACTIVE', { keyValue, status: apiKey.status });
        return null;
      }

      // 更新最后使用时间和使用次数
      await this.updateUsage(apiKey.id);
      
      logger.security('API_KEY_VERIFIED', { id: apiKey.id, name: apiKey.name });
      return apiKey;
    } catch (error) {
      logger.error('Error verifying API key:', error);
      return null;
    }
  }

  // 更新使用记录
  static async updateUsage(id) {
    try {
      const query = `
        UPDATE api_keys
        SET last_used_at = CURRENT_TIMESTAMP,
            usage_count = usage_count + 1
        WHERE id = ?
      `;

      await database.query(query, [id]);

      // 获取更新后的使用次数
      const selectQuery = 'SELECT usage_count FROM api_keys WHERE id = ?';
      const result = await database.query(selectQuery, [id]);
      return result.rows[0]?.usage_count || 0;
    } catch (error) {
      logger.error('Error updating API key usage:', error);
      throw error;
    }
  }

  // 获取所有API密钥
  static async findAll(options = {}) {
    try {
      const { status, name, page = 1, limit = 20 } = options;
      const offset = (page - 1) * limit;

      let whereConditions = [];
      const queryParams = [];

      if (status) {
        whereConditions.push('status = ?');
        queryParams.push(status);
      }

      if (name) {
        whereConditions.push('name = ?');
        queryParams.push(name);
      }

      const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

      // 如果没有分页参数，返回简单列表
      if (!page || !limit) {
        const dataQuery = `SELECT * FROM api_keys ${whereClause} ORDER BY created_at DESC`;
        const dataResult = await database.query(dataQuery, queryParams);
        return dataResult.rows.map(row => new ApiKey(row));
      }

      // 查询总数
      const countQuery = `SELECT COUNT(*) as total FROM api_keys ${whereClause}`;
      const countResult = await database.query(countQuery, queryParams);
      const total = countResult.rows && countResult.rows[0] ? parseInt(countResult.rows[0].total) : 0;

      // 查询数据
      const dataQuery = `
        SELECT * FROM api_keys
        ${whereClause}
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
      `;

      // 为数据查询创建新的参数数组，包含 WHERE 条件参数 + LIMIT + OFFSET
      // 确保 limit 和 offset 是整数类型（MySQL 8.0.22+ 要求）
      const dataQueryParams = [...queryParams, parseInt(limit), parseInt(offset)];
      const dataResult = await database.query(dataQuery, dataQueryParams);

      const apiKeys = dataResult.rows.map(row => new ApiKey(row));

      return {
        apiKeys,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error finding API keys:', error);
      throw error;
    }
  }

  // 更新API密钥
  static async update(id, updateData) {
    try {
      const fields = [];
      const values = [];

      // 动态构建更新字段
      Object.keys(updateData).forEach(key => {
        if (updateData[key] !== undefined && key !== 'keyValue') { // 不允许更新密钥值
          const dbField = key === 'lastUsedAt' ? 'last_used_at' :
                         key === 'usageCount' ? 'usage_count' : key;

          fields.push(`${dbField} = ?`);
          values.push(updateData[key]);
        }
      });

      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      values.push(id);

      const query = `
        UPDATE api_keys
        SET ${fields.join(', ')}
        WHERE id = ?
      `;

      await database.query(query, values);
      logger.db('UPDATE', 'api_keys', { id });

      // 返回更新后的API密钥
      return await this.findById(id);
    } catch (error) {
      logger.error('Error updating API key:', error);
      throw error;
    }
  }

  // 删除API密钥
  static async delete(id) {
    try {
      const query = 'DELETE FROM api_keys WHERE id = ?';
      const result = await database.query(query, [id]);

      if (result.affectedRows === 0) {
        return false;
      }

      logger.db('DELETE', 'api_keys', { id });
      return true;
    } catch (error) {
      logger.error('Error deleting API key:', error);
      throw error;
    }
  }

  // 重新生成API密钥
  static async regenerate(id) {
    try {
      const newKeyValue = this.generateKey();

      const query = 'UPDATE api_keys SET key_value = ? WHERE id = ?';
      await database.query(query, [newKeyValue, id]);

      logger.db('REGENERATE', 'api_keys', { id });
      logger.security('API_KEY_REGENERATED', { id });

      return await this.findById(id);
    } catch (error) {
      logger.error('Error regenerating API key:', error);
      throw error;
    }
  }

  // 获取使用统计
  static async getUsageStats(id, days = 30) {
    try {
      // 检查API密钥是否存在
      const apiKey = await this.findById(id);
      if (!apiKey) {
        return null;
      }

      const query = `
        SELECT
          DATE(created_at) as date,
          COUNT(*) as requests,
          COUNT(DISTINCT ip_address) as unique_ips,
          AVG(response_time) as avg_response_time
        FROM api_usage_logs
        WHERE api_key_id = ?
          AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY DATE(created_at)
        ORDER BY date DESC
      `;

      const result = await database.query(query, [id, days]);

      return {
        apiKey: apiKey.toJSON(),
        stats: result.rows,
        summary: {
          totalRequests: result.rows.reduce((sum, row) => sum + parseInt(row.requests), 0),
          uniqueIps: new Set(result.rows.map(row => row.unique_ips)).size,
          avgResponseTime: result.rows.length > 0 ?
            result.rows.reduce((sum, row) => sum + parseFloat(row.avg_response_time || 0), 0) / result.rows.length : 0
        }
      };
    } catch (error) {
      logger.error('Error getting API key usage stats:', error);
      throw error;
    }
  }

  // 删除API密钥
  static async delete(id) {
    try {
      const query = 'DELETE FROM api_keys WHERE id = $1 RETURNING id, name';
      const result = await database.query(query, [id]);
      
      if (result.rows.length === 0) {
        return false;
      }

      logger.db('DELETE', 'api_keys', { id });
      logger.security('API_KEY_DELETED', { id, name: result.rows[0].name });
      return true;
    } catch (error) {
      logger.error('Error deleting API key:', error);
      throw error;
    }
  }

  // 禁用API密钥
  static async disable(id) {
    try {
      const result = await this.update(id, { status: 'inactive' });
      if (result) {
        logger.security('API_KEY_DISABLED', { id, name: result.name });
      }
      return result;
    } catch (error) {
      logger.error('Error disabling API key:', error);
      throw error;
    }
  }

  // 启用API密钥
  static async enable(id) {
    try {
      const result = await this.update(id, { status: 'active' });
      if (result) {
        logger.security('API_KEY_ENABLED', { id, name: result.name });
      }
      return result;
    } catch (error) {
      logger.error('Error enabling API key:', error);
      throw error;
    }
  }

  // 获取使用统计
  static async getUsageStats(id, days = 30) {
    try {
      const query = `
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as requests
        FROM api_usage_logs 
        WHERE api_key_id = $1 
          AND created_at >= CURRENT_DATE - INTERVAL '${days} days'
        GROUP BY DATE(created_at)
        ORDER BY date DESC
      `;
      
      const result = await database.query(query, [id]);
      return result.rows;
    } catch (error) {
      logger.error('Error getting usage stats:', error);
      throw error;
    }
  }

  // 记录API使用日志
  static async logUsage(apiKeyId, endpoint, ip, userAgent) {
    try {
      const query = `
        INSERT INTO api_usage_logs (api_key_id, endpoint, ip_address, user_agent)
        VALUES (?, ?, ?, ?)
      `;

      await database.query(query, [apiKeyId, endpoint, ip, userAgent]);
    } catch (error) {
      logger.error('Error logging API usage:', error);
      // 不抛出错误，避免影响主要功能
    }
  }

  // 获取API密钥统计
  static async getStats() {
    try {
      const query = `
        SELECT 
          COUNT(*) as total,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active,
          COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive,
          SUM(usage_count) as total_usage
        FROM api_keys
      `;
      
      const result = await database.query(query);
      return result.rows && result.rows[0] ? result.rows[0] : { total: 0, active: 0, inactive: 0, total_usage: 0 };
    } catch (error) {
      logger.error('Error getting API key stats:', error);
      throw error;
    }
  }

  // 转换为JSON格式（隐藏完整密钥）
  toJSON(showFullKey = false) {
    const keyValue = showFullKey ? this.keyValue : this.maskKey(this.keyValue);
    
    return {
      id: this.id,
      name: this.name,
      keyValue,
      description: this.description,
      status: this.status,
      lastUsedAt: this.lastUsedAt,
      usageCount: this.usageCount,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  // 掩码密钥显示
  maskKey(keyValue) {
    if (!keyValue || keyValue.length < 20) return keyValue;

    const prefix = keyValue.substring(0, 16); // 显示前16位
    const suffix = keyValue.substring(keyValue.length - 16); // 显示后16位
    const maskedLength = keyValue.length - 32; // 中间需要掩码的长度
    const masked = '*'.repeat(Math.max(maskedLength, 8)); // 至少8个星号

    return `${prefix}${masked}${suffix}`;
  }
}

module.exports = ApiKey;

const database = require('../config/database');
const logger = require('../utils/logger');

class Category {
  constructor(data = {}) {
    this.id = data.id;
    this.name = data.name;
    this.slug = data.slug;
    this.description = data.description;
    this.coverUrl = data.cover_url || data.coverUrl;
    this.sortOrder = data.sort_order || data.sortOrder || 0;
    this.status = data.status || 'active';
    this.videoCount = data.video_count || data.videoCount || 0;
    this.createdAt = data.created_at || data.createdAt;
    this.updatedAt = data.updated_at || data.updatedAt;
  }



  // 创建分类
  static async create(categoryData) {
    try {
      // 生成唯一的 slug
      let slug = categoryData.slug || this.generateSlug(categoryData.name);
      slug = await this.generateUniqueSlug(slug);

      const query = `
        INSERT INTO categories (name, slug, description, cover_url, sort_order, status)
        VALUES (?, ?, ?, ?, ?, ?)
      `;

      const values = [
        categoryData.name,
        slug,
        categoryData.description || null,
        categoryData.coverUrl || null,
        categoryData.sortOrder || 0,
        categoryData.status || 'active'
      ];

      const result = await database.query(query, values);
      logger.db('CREATE', 'categories', { id: result.insertId });

      // 获取创建的分类
      return await this.findById(result.insertId);
    } catch (error) {
      logger.error('Error creating category:', error);
      throw error;
    }
  }

  // 根据ID查找分类
  static async findById(id) {
    try {
      const query = `
        SELECT c.*,
               COUNT(v.id) as video_count
        FROM categories c
        LEFT JOIN videos v ON c.id = v.category_id AND v.status = 'active'
        WHERE c.id = ?
        GROUP BY c.id
      `;

      const result = await database.query(query, [id]);

      if (result.rows.length === 0) {
        return null;
      }

      return new Category(result.rows[0]);
    } catch (error) {
      logger.error('Error finding category by ID:', error);
      throw error;
    }
  }

  // 根据slug查找分类
  static async findBySlug(slug) {
    try {
      const query = `
        SELECT c.*,
               COUNT(v.id) as video_count
        FROM categories c
        LEFT JOIN videos v ON c.id = v.category_id AND v.status = 'active'
        WHERE c.slug = ?
        GROUP BY c.id
      `;

      const result = await database.query(query, [slug]);

      if (result.rows.length === 0) {
        return null;
      }

      return new Category(result.rows[0]);
    } catch (error) {
      logger.error('Error finding category by slug:', error);
      throw error;
    }
  }

  // 获取所有分类
  static async findAll(options = {}) {
    try {
      const { status = 'active', includeEmpty = false } = options;
      
      let whereCondition = 'WHERE c.status = $1';
      const queryParams = [status];
      
      if (!includeEmpty) {
        whereCondition += ' AND COUNT(v.id) > 0';
      }

      const query = `
        SELECT c.*, 
               COUNT(v.id) as video_count
        FROM categories c
        LEFT JOIN videos v ON c.id = v.category_id AND v.status = 'active'
        ${whereCondition}
        GROUP BY c.id
        ORDER BY c.sort_order ASC, c.name ASC
      `;
      
      const result = await database.query(query, queryParams);
      
      return result.rows.map(row => new Category(row));
    } catch (error) {
      logger.error('Error finding categories:', error);
      throw error;
    }
  }

  // 更新分类
  static async update(id, updateData) {
    try {
      const fields = [];
      const values = [];

      // 动态构建更新字段
      Object.keys(updateData).forEach(key => {
        if (updateData[key] !== undefined) {
          const dbField = key === 'coverUrl' ? 'cover_url' :
                         key === 'sortOrder' ? 'sort_order' : key;

          fields.push(`${dbField} = ?`);
          values.push(updateData[key]);
        }
      });

      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      values.push(id);

      const query = `
        UPDATE categories
        SET ${fields.join(', ')}
        WHERE id = ?
      `;

      await database.query(query, values);
      logger.db('UPDATE', 'categories', { id });

      // 返回更新后的分类
      return await this.findById(id);
    } catch (error) {
      logger.error('Error updating category:', error);
      throw error;
    }
  }

  // 删除分类
  static async delete(id) {
    try {
      const query = 'DELETE FROM categories WHERE id = ?';
      const result = await database.query(query, [id]);

      if (result.affectedRows === 0) {
        return false;
      }

      logger.db('DELETE', 'categories', { id });
      return true;
    } catch (error) {
      logger.error('Error deleting category:', error);
      throw error;
    }
  }

  // 获取分类下的视频数量
  static async getVideosCount(id) {
    try {
      const query = 'SELECT COUNT(*) as count FROM videos WHERE category_id = ?';
      const result = await database.query(query, [id]);
      return parseInt(result.rows[0].count) || 0;
    } catch (error) {
      logger.error('Error getting videos count:', error);
      return 0;
    }
  }

  // 获取所有分类（带分页）
  static async findAll(options = {}) {
    try {
      const { page, limit, status, search } = options;

      let whereConditions = [];
      let queryParams = [];

      // 状态筛选
      if (status) {
        whereConditions.push('c.status = ?');
        queryParams.push(status);
      }

      // 搜索筛选
      if (search) {
        whereConditions.push('(c.name LIKE ? OR c.description LIKE ?)');
        queryParams.push(`%${search}%`, `%${search}%`);
      }

      const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

      // 如果没有分页参数，返回简单列表
      if (!page || !limit) {
        const dataQuery = `
          SELECT c.*, COUNT(v.id) as video_count
          FROM categories c
          LEFT JOIN videos v ON c.id = v.category_id AND v.status = 'active'
          ${whereClause}
          GROUP BY c.id
          ORDER BY c.sort_order ASC, c.created_at DESC
        `;

        const dataResult = await database.query(dataQuery, queryParams);
        return dataResult.rows.map(row => {
          const category = new Category(row);
          category.videoCount = parseInt(row.video_count) || 0;
          return category;
        });
      }

      const offset = (page - 1) * limit;

      // 查询总数
      const countQuery = `SELECT COUNT(*) as total FROM categories c ${whereClause}`;
      const countResult = await database.query(countQuery, queryParams);
      const total = countResult.rows && countResult.rows[0] ? parseInt(countResult.rows[0].total) : 0;

      // 查询数据
      const dataQuery = `
        SELECT c.*, COUNT(v.id) as video_count
        FROM categories c
        LEFT JOIN videos v ON c.id = v.category_id AND v.status = 'active'
        ${whereClause}
        GROUP BY c.id
        ORDER BY c.sort_order ASC, c.created_at DESC
        LIMIT ? OFFSET ?
      `;

      // 为数据查询创建新的参数数组，包含 WHERE 条件参数 + LIMIT + OFFSET
      // 确保 limit 和 offset 是整数类型（MySQL 8.0.22+ 要求）
      const parsedLimit = parseInt(limit);
      const parsedOffset = parseInt(offset);
      const dataQueryParams = [...queryParams, parsedLimit, parsedOffset];





      const dataResult = await database.query(dataQuery, dataQueryParams);

      const categories = dataResult.rows.map(row => {
        const category = new Category(row);
        category.videoCount = parseInt(row.video_count) || 0;
        return category;
      });

      return {
        categories,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('❌ 查找分类失败:', {
        error: {
          name: error.name,
          message: error.message,
          code: error.code || 'N/A',
          errno: error.errno || 'N/A',
          sqlState: error.sqlState || 'N/A'
        },
        options: options,
        stack: error.stack
      });
      throw error;
    }
  }

  // 获取热门分类
  static async getPopular(limit = 10) {
    try {
      const query = `
        SELECT c.*,
               COUNT(v.id) as video_count,
               SUM(v.views) as total_views
        FROM categories c
        LEFT JOIN videos v ON c.id = v.category_id AND v.status = 'active'
        WHERE c.status = 'active'
        GROUP BY c.id
        HAVING COUNT(v.id) > 0
        ORDER BY total_views DESC, video_count DESC
        LIMIT ?
      `;

      const result = await database.query(query, [limit]);

      return result.rows.map(row => new Category(row));
    } catch (error) {
      logger.error('Error getting popular categories:', error);
      throw error;
    }
  }

  // 获取分类统计
  static async getStats() {
    try {
      const query = `
        SELECT 
          COUNT(*) as total,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active,
          COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive
        FROM categories
      `;
      
      const result = await database.query(query);
      return result.rows[0];
    } catch (error) {
      logger.error('Error getting category stats:', error);
      throw error;
    }
  }

  // 生成slug
  static generateSlug(name) {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\u4e00-\u9fff]/g, '-') // 保留中文字符
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }

  // 生成唯一的slug
  static async generateUniqueSlug(baseSlug, excludeId = null) {
    let slug = baseSlug;
    let counter = 1;

    while (!(await this.isSlugUnique(slug, excludeId))) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    return slug;
  }

  // 检查slug是否唯一
  static async isSlugUnique(slug, excludeId = null) {
    try {
      let query = 'SELECT id FROM categories WHERE slug = ?';
      const params = [slug];

      if (excludeId) {
        query += ' AND id != ?';
        params.push(excludeId);
      }

      const result = await database.query(query, params);
      return result.rows.length === 0;
    } catch (error) {
      logger.error('Error checking slug uniqueness:', error);
      throw error;
    }
  }

  // 重新排序分类
  static async reorder(categoryOrders) {
    try {
      for (const { id, sortOrder } of categoryOrders) {
        await database.query(
          'UPDATE categories SET sort_order = ? WHERE id = ?',
          [sortOrder, id]
        );
      }

      logger.db('REORDER', 'categories', { count: categoryOrders.length });
      return true;
    } catch (error) {
      logger.error('Error reordering categories:', error);
      throw error;
    }
  }

  // 获取统计信息
  static async getStats() {
    try {
      const query = `
        SELECT
          COUNT(*) as total,
          SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
          SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive
        FROM categories
      `;

      const result = await database.query(query);
      const stats = result.rows[0];

      return {
        total: parseInt(stats.total) || 0,
        active: parseInt(stats.active) || 0,
        inactive: parseInt(stats.inactive) || 0
      };
    } catch (error) {
      logger.error('Error getting category stats:', error);
      throw error;
    }
  }

  // 转换为JSON格式
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      slug: this.slug,
      description: this.description,
      coverUrl: this.coverUrl,
      sortOrder: this.sortOrder,
      status: this.status,
      videoCount: parseInt(this.videoCount) || 0,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

module.exports = Category;

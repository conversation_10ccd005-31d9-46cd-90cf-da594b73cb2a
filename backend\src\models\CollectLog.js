const db = require('../config/database');
const logger = require('../utils/logger');

class CollectLog {
  constructor(data = {}) {
    this.id = data.id;
    this.sourceId = data.source_id || data.sourceId;
    this.sourceName = data.source_name || data.sourceName;
    this.type = data.type || 'manual'; // manual, auto, test
    this.status = data.status || 'running'; // running, success, failed, stopped
    this.collectParams = data.collect_params || data.collectParams;
    this.categoryId = data.category_id || data.categoryId;
    this.pageStart = data.page_start || data.pageStart || 1;
    this.pageEnd = data.page_end || data.pageEnd;
    this.totalFound = data.total_found || data.totalFound || 0;
    this.totalCollected = data.total_collected || data.totalCollected || 0;
    this.totalSkipped = data.total_skipped || data.totalSkipped || 0;
    this.totalFailed = data.total_failed || data.totalFailed || 0;
    this.startTime = data.start_time || data.startTime;
    this.endTime = data.end_time || data.endTime;
    this.duration = data.duration;
    this.errorMessage = data.error_message || data.errorMessage;
    this.errorDetails = data.error_details || data.errorDetails;
    this.createdBy = data.created_by || data.createdBy;
    this.createdAt = data.created_at || data.createdAt;
    this.updatedAt = data.updated_at || data.updatedAt;
  }

  // 创建采集日志
  static async create(data) {
    try {
      const collectParams = typeof data.collectParams === 'object' 
        ? JSON.stringify(data.collectParams) 
        : data.collectParams;

      const errorDetails = typeof data.errorDetails === 'object' 
        ? JSON.stringify(data.errorDetails) 
        : data.errorDetails;

      const query = `
        INSERT INTO collect_logs (
          source_id, source_name, type, status, collect_params,
          category_id, page_start, page_end, total_found, total_collected,
          total_skipped, total_failed, start_time, end_time, duration,
          error_message, error_details, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        data.sourceId,
        data.sourceName,
        data.type || 'manual',
        data.status || 'running',
        collectParams,
        data.categoryId,
        data.pageStart || 1,
        data.pageEnd,
        data.totalFound || 0,
        data.totalCollected || 0,
        data.totalSkipped || 0,
        data.totalFailed || 0,
        data.startTime || new Date(),
        data.endTime,
        data.duration,
        data.errorMessage,
        errorDetails,
        data.createdBy
      ];

      const result = await db.query(query, values);
      logger.info('采集日志创建成功', { id: result.insertId, sourceId: data.sourceId });
      return await this.findById(result.insertId);
    } catch (error) {
      logger.error('创建采集日志失败:', error);
      throw error;
    }
  }

  // 根据ID查找
  static async findById(id) {
    try {
      const query = 'SELECT * FROM collect_logs WHERE id = ?';
      const result = await db.query(query, [id]);

      if (result.rows.length === 0) {
        return null;
      }

      const log = new CollectLog(result.rows[0]);
      
      // 解析JSON字段
      if (log.collectParams && typeof log.collectParams === 'string') {
        try {
          log.collectParams = JSON.parse(log.collectParams);
        } catch (e) {
          log.collectParams = {};
        }
      }

      if (log.errorDetails && typeof log.errorDetails === 'string') {
        try {
          log.errorDetails = JSON.parse(log.errorDetails);
        } catch (e) {
          log.errorDetails = {};
        }
      }

      return log;
    } catch (error) {
      logger.error('查找采集日志失败:', error);
      throw error;
    }
  }

  // 获取所有采集日志
  static async findAll(options = {}) {
    try {
      const { page = 1, limit = 20, sourceId, status, type } = options;
      const offset = (page - 1) * limit;

      let whereClause = '';
      const whereParams = [];

      if (sourceId) {
        whereClause += ' WHERE source_id = ?';
        whereParams.push(sourceId);
      }

      if (status) {
        whereClause += whereClause ? ' AND status = ?' : ' WHERE status = ?';
        whereParams.push(status);
      }

      if (type) {
        whereClause += whereClause ? ' AND type = ?' : ' WHERE type = ?';
        whereParams.push(type);
      }

      const query = `
        SELECT * FROM collect_logs 
        ${whereClause}
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
      `;

      const countQuery = `
        SELECT COUNT(*) as total FROM collect_logs 
        ${whereClause}
      `;

      const [result, countResult] = await Promise.all([
        db.query(query, [...whereParams, limit, offset]),
        db.query(countQuery, whereParams)
      ]);

      const logs = result.rows.map(row => {
        const log = new CollectLog(row);
        
        // 解析JSON字段
        if (log.collectParams && typeof log.collectParams === 'string') {
          try {
            log.collectParams = JSON.parse(log.collectParams);
          } catch (e) {
            log.collectParams = {};
          }
        }

        return log;
      });

      const total = countResult.rows[0].total;

      return {
        logs,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('获取采集日志列表失败:', error);
      throw error;
    }
  }

  // 更新采集日志
  static async update(id, data) {
    try {
      const fields = [];
      const values = [];

      const allowedFields = [
        'status', 'total_found', 'total_collected', 'total_skipped', 
        'total_failed', 'end_time', 'duration', 'error_message'
      ];

      allowedFields.forEach(field => {
        const dataField = field.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
        
        if (data[dataField] !== undefined || data[field] !== undefined) {
          fields.push(`${field} = ?`);
          values.push(data[dataField] || data[field]);
        }
      });

      // 处理JSON字段
      if (data.errorDetails !== undefined) {
        fields.push('error_details = ?');
        values.push(typeof data.errorDetails === 'object' 
          ? JSON.stringify(data.errorDetails) 
          : data.errorDetails);
      }

      if (fields.length === 0) {
        throw new Error('没有要更新的字段');
      }

      values.push(id);
      const query = `UPDATE collect_logs SET ${fields.join(', ')} WHERE id = ?`;
      
      await db.query(query, values);
      logger.info('采集日志更新成功', { id });
      
      return await this.findById(id);
    } catch (error) {
      logger.error('更新采集日志失败:', error);
      throw error;
    }
  }

  // 删除采集日志
  static async delete(id) {
    try {
      const query = 'DELETE FROM collect_logs WHERE id = ?';
      await db.query(query, [id]);
      logger.info('采集日志删除成功', { id });
      return true;
    } catch (error) {
      logger.error('删除采集日志失败:', error);
      throw error;
    }
  }

  // 获取统计信息
  static async getStats(sourceId = null) {
    try {
      let whereClause = '';
      const params = [];

      if (sourceId) {
        whereClause = 'WHERE source_id = ?';
        params.push(sourceId);
      }

      const query = `
        SELECT 
          COUNT(*) as total_logs,
          SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success_count,
          SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_count,
          SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as running_count,
          SUM(total_collected) as total_collected_videos,
          AVG(duration) as avg_duration
        FROM collect_logs 
        ${whereClause}
      `;

      const result = await db.query(query, params);
      return result.rows[0];
    } catch (error) {
      logger.error('获取采集日志统计失败:', error);
      throw error;
    }
  }
}

module.exports = CollectLog;

const db = require('../config/database');
const logger = require('../utils/logger');

class CollectSource {
  constructor(data = {}) {
    this.id = data.id;
    this.name = data.name;
    this.url = data.url;
    this.type = data.type || 'maccms';
    this.description = data.description;
    this.status = data.status || 'active';
    this.collectConfig = data.collect_config || data.collectConfig;
    this.autoCollect = data.auto_collect || data.autoCollect || 0;
    this.collectInterval = data.collect_interval || data.collectInterval || 60;
    this.collectImages = data.collect_images || data.collectImages || 1;
    this.collectCategories = data.collect_categories || data.collectCategories;
    this.totalVideos = data.total_videos || data.totalVideos || 0;
    this.collectedVideos = data.collected_videos || data.collectedVideos || 0;
    this.lastCollectTime = data.last_collect_time || data.lastCollectTime;
    this.lastCheckTime = data.last_check_time || data.lastCheckTime;
    this.responseTime = data.response_time || data.responseTime;
    this.successRate = data.success_rate || data.successRate || 0;
    this.lastError = data.last_error || data.lastError;
    this.errorCount = data.error_count || data.errorCount || 0;
    this.lastErrorTime = data.last_error_time || data.lastErrorTime;
    this.createdBy = data.created_by || data.createdBy;
    this.updatedBy = data.updated_by || data.updatedBy;
    this.createdAt = data.created_at || data.createdAt;
    this.updatedAt = data.updated_at || data.updatedAt;
  }

  // 创建采集源
  static async create(data) {
    try {
      const collectConfig = typeof data.collectConfig === 'object' 
        ? JSON.stringify(data.collectConfig) 
        : data.collectConfig;

      const query = `
        INSERT INTO collect_sources (
          name, url, type, description, status,
          collect_config, auto_collect, collect_interval, 
          collect_images, collect_categories, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        data.name,
        data.url,
        data.type || 'maccms',
        data.description,
        data.status || 'active',
        collectConfig,
        data.autoCollect || 0,
        data.collectInterval || 60,
        data.collectImages || 1,
        data.collectCategories,
        data.createdBy
      ];

      const result = await db.query(query, values);

      logger.info('采集源创建成功', { id: result.insertId, name: data.name });
      return await this.findById(result.insertId);
    } catch (error) {
      logger.error('创建采集源失败:', error);
      throw error;
    }
  }

  // 根据ID查找
  static async findById(id) {
    try {
      const query = 'SELECT * FROM collect_sources WHERE id = ?';
      const result = await db.query(query, [id]);

      if (result.rows.length === 0) {
        return null;
      }

      const source = new CollectSource(result.rows[0]);
      
      // 解析JSON字段
      if (source.collectConfig && typeof source.collectConfig === 'string') {
        try {
          source.collectConfig = JSON.parse(source.collectConfig);
        } catch (e) {
          source.collectConfig = {};
        }
      }

      return source;
    } catch (error) {
      logger.error('查找采集源失败:', error);
      throw error;
    }
  }

  // 查找所有采集源
  static async findAll(options = {}) {
    try {
      let query = 'SELECT * FROM collect_sources';
      const conditions = [];
      const values = [];

      // 状态筛选
      if (options.status) {
        conditions.push('status = ?');
        values.push(options.status);
      }

      // 类型筛选
      if (options.type) {
        conditions.push('type = ?');
        values.push(options.type);
      }

      // 搜索
      if (options.search) {
        conditions.push('(name LIKE ? OR description LIKE ?)');
        values.push(`%${options.search}%`, `%${options.search}%`);
      }

      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }

      // 排序
      const sortField = options.sortField || 'created_at';
      const sortDirection = options.sortDirection || 'DESC';
      query += ` ORDER BY ${sortField} ${sortDirection}`;

      // 分页
      const page = parseInt(options.page) || 1;
      const limit = parseInt(options.limit) || 20;
      const offset = (page - 1) * limit;

      // 获取总数
      const countQuery = query.replace('SELECT *', 'SELECT COUNT(*) as total');
      const countResult = await db.query(countQuery, values);
      const total = countResult.rows[0].total;

      // 获取数据
      query += ' LIMIT ? OFFSET ?';
      values.push(limit, offset);

      const result = await db.query(query, values);
      
      const sources = result.rows.map(row => {
        const source = new CollectSource(row);
        
        // 解析JSON字段
        if (source.collectConfig && typeof source.collectConfig === 'string') {
          try {
            source.collectConfig = JSON.parse(source.collectConfig);
          } catch (e) {
            source.collectConfig = {};
          }
        }

        return source;
      });

      return {
        sources,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('查找采集源列表失败:', error);
      throw error;
    }
  }

  // 更新采集源
  static async update(id, data) {
    try {
      const fields = [];
      const values = [];

      // 动态构建更新字段
      const allowedFields = [
        'name', 'url', 'type', 'description', 'status',
        'auto_collect', 'collect_interval', 'collect_images', 
        'collect_categories', 'updated_by'
      ];

      allowedFields.forEach(field => {
        const dbField = field;
        const dataField = field.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
        
        if (data[dataField] !== undefined || data[field] !== undefined) {
          fields.push(`${dbField} = ?`);
          values.push(data[dataField] || data[field]);
        }
      });

      // 处理JSON字段
      if (data.collectConfig !== undefined) {
        fields.push('collect_config = ?');
        values.push(typeof data.collectConfig === 'object' 
          ? JSON.stringify(data.collectConfig) 
          : data.collectConfig);
      }

      if (fields.length === 0) {
        throw new Error('没有要更新的字段');
      }

      values.push(id);
      const query = `UPDATE collect_sources SET ${fields.join(', ')} WHERE id = ?`;

      const updateResult = await db.query(query, values);

      if (updateResult.affectedRows === 0) {
        throw new Error('采集源不存在');
      }

      logger.info('采集源更新成功', { id, fields: fields.length });
      return await this.findById(id);
    } catch (error) {
      logger.error('更新采集源失败:', error);
      throw error;
    }
  }

  // 更新统计信息
  static async updateStats(id, stats) {
    try {
      const query = `
        UPDATE collect_sources SET 
          total_videos = ?, 
          collected_videos = ?,
          last_check_time = NOW(),
          response_time = ?,
          success_rate = ?
        WHERE id = ?
      `;

      const values = [
        stats.totalVideos || 0,
        stats.collectedVideos || 0,
        stats.responseTime || null,
        stats.successRate || 0,
        id
      ];

      await db.query(query, values);
      logger.info('采集源统计更新成功', { id, stats });
    } catch (error) {
      logger.error('更新采集源统计失败:', error);
      throw error;
    }
  }

  // 记录错误
  static async recordError(id, error) {
    try {
      const query = `
        UPDATE collect_sources SET 
          last_error = ?,
          error_count = error_count + 1,
          last_error_time = NOW(),
          status = 'error'
        WHERE id = ?
      `;

      await db.query(query, [error, id]);
      logger.info('采集源错误记录成功', { id, error });
    } catch (err) {
      logger.error('记录采集源错误失败:', err);
      throw err;
    }
  }

  // 删除采集源
  static async delete(id) {
    try {
      const query = 'DELETE FROM collect_sources WHERE id = ?';
      const result = await db.query(query, [id]);

      if (result.affectedRows === 0) {
        throw new Error('采集源不存在');
      }

      logger.info('采集源删除成功', { id });
      return true;
    } catch (error) {
      logger.error('删除采集源失败:', error);
      throw error;
    }
  }

  // 获取活跃的采集源
  static async getActiveSources() {
    try {
      const query = 'SELECT * FROM collect_sources WHERE status = "active" ORDER BY created_at DESC';
      const result = await db.query(query);

      return result.rows.map(row => {
        const source = new CollectSource(row);
        
        if (source.collectConfig && typeof source.collectConfig === 'string') {
          try {
            source.collectConfig = JSON.parse(source.collectConfig);
          } catch (e) {
            source.collectConfig = {};
          }
        }

        return source;
      });
    } catch (error) {
      logger.error('获取活跃采集源失败:', error);
      throw error;
    }
  }
}

module.exports = CollectSource;

const db = require('../config/database');
const logger = require('../utils/logger');

class CollectTask {
  constructor(data = {}) {
    this.id = data.id;
    this.sourceId = data.source_id || data.sourceId;
    this.taskName = data.task_name || data.taskName;
    this.taskType = data.task_type || data.taskType || 'once'; // once, daily, weekly, monthly, custom
    this.cronExpression = data.cron_expression || data.cronExpression;
    this.status = data.status || 'active'; // active, inactive, running, paused
    this.taskConfig = data.task_config || data.taskConfig;
    this.collectCategories = data.collect_categories || data.collectCategories;
    this.maxPages = data.max_pages || data.maxPages || 10;
    this.collectImages = data.collect_images || data.collectImages || 1;
    this.lastRunTime = data.last_run_time || data.lastRunTime;
    this.nextRunTime = data.next_run_time || data.nextRunTime;
    this.runCount = data.run_count || data.runCount || 0;
    this.successCount = data.success_count || data.successCount || 0;
    this.failCount = data.fail_count || data.failCount || 0;
    this.createdBy = data.created_by || data.createdBy;
    this.updatedBy = data.updated_by || data.updatedBy;
    this.createdAt = data.created_at || data.createdAt;
    this.updatedAt = data.updated_at || data.updatedAt;
  }

  // 创建采集任务
  static async create(data) {
    try {
      const taskConfig = typeof data.taskConfig === 'object' 
        ? JSON.stringify(data.taskConfig) 
        : data.taskConfig;

      const query = `
        INSERT INTO collect_tasks (
          source_id, task_name, task_type, cron_expression, status,
          task_config, collect_categories, max_pages, collect_images,
          last_run_time, next_run_time, run_count, success_count, fail_count,
          created_by, updated_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        data.sourceId,
        data.taskName,
        data.taskType || 'once',
        data.cronExpression,
        data.status || 'active',
        taskConfig,
        data.collectCategories,
        data.maxPages || 10,
        data.collectImages || 1,
        data.lastRunTime,
        data.nextRunTime,
        data.runCount || 0,
        data.successCount || 0,
        data.failCount || 0,
        data.createdBy,
        data.updatedBy
      ];

      const result = await db.query(query, values);
      logger.info('采集任务创建成功', { id: result.insertId, taskName: data.taskName });
      return await this.findById(result.insertId);
    } catch (error) {
      logger.error('创建采集任务失败:', error);
      throw error;
    }
  }

  // 根据ID查找
  static async findById(id) {
    try {
      const query = `
        SELECT t.*, s.name as source_name, s.url as source_url, s.type as source_type
        FROM collect_tasks t
        LEFT JOIN collect_sources s ON t.source_id = s.id
        WHERE t.id = ?
      `;
      const result = await db.query(query, [id]);

      if (result.rows.length === 0) {
        return null;
      }

      const task = new CollectTask(result.rows[0]);
      task.sourceName = result.rows[0].source_name;
      task.sourceUrl = result.rows[0].source_url;
      task.sourceType = result.rows[0].source_type;
      
      // 解析JSON字段
      if (task.taskConfig && typeof task.taskConfig === 'string') {
        try {
          task.taskConfig = JSON.parse(task.taskConfig);
        } catch (e) {
          task.taskConfig = {};
        }
      }

      return task;
    } catch (error) {
      logger.error('查找采集任务失败:', error);
      throw error;
    }
  }

  // 获取所有采集任务
  static async findAll(options = {}) {
    try {
      const { page = 1, limit = 20, sourceId, status, taskType } = options;
      const offset = (page - 1) * limit;

      let whereClause = '';
      const whereParams = [];

      if (sourceId) {
        whereClause += ' WHERE t.source_id = ?';
        whereParams.push(sourceId);
      }

      if (status) {
        whereClause += whereClause ? ' AND t.status = ?' : ' WHERE t.status = ?';
        whereParams.push(status);
      }

      if (taskType) {
        whereClause += whereClause ? ' AND t.task_type = ?' : ' WHERE t.task_type = ?';
        whereParams.push(taskType);
      }

      const query = `
        SELECT t.*, s.name as source_name, s.url as source_url, s.type as source_type
        FROM collect_tasks t
        LEFT JOIN collect_sources s ON t.source_id = s.id
        ${whereClause}
        ORDER BY t.created_at DESC 
        LIMIT ? OFFSET ?
      `;

      const countQuery = `
        SELECT COUNT(*) as total FROM collect_tasks t
        ${whereClause}
      `;

      const [result, countResult] = await Promise.all([
        db.query(query, [...whereParams, limit, offset]),
        db.query(countQuery, whereParams)
      ]);

      const tasks = result.rows.map(row => {
        const task = new CollectTask(row);
        task.sourceName = row.source_name;
        task.sourceUrl = row.source_url;
        task.sourceType = row.source_type;
        
        // 解析JSON字段
        if (task.taskConfig && typeof task.taskConfig === 'string') {
          try {
            task.taskConfig = JSON.parse(task.taskConfig);
          } catch (e) {
            task.taskConfig = {};
          }
        }

        return task;
      });

      const total = countResult.rows[0].total;

      return {
        tasks,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('获取采集任务列表失败:', error);
      throw error;
    }
  }

  // 更新采集任务
  static async update(id, data) {
    try {
      const fields = [];
      const values = [];

      const allowedFields = [
        'task_name', 'task_type', 'cron_expression', 'status',
        'collect_categories', 'max_pages', 'collect_images',
        'last_run_time', 'next_run_time', 'run_count', 'success_count', 'fail_count',
        'updated_by'
      ];

      allowedFields.forEach(field => {
        const dataField = field.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
        
        if (data[dataField] !== undefined || data[field] !== undefined) {
          fields.push(`${field} = ?`);
          values.push(data[dataField] || data[field]);
        }
      });

      // 处理JSON字段
      if (data.taskConfig !== undefined) {
        fields.push('task_config = ?');
        values.push(typeof data.taskConfig === 'object' 
          ? JSON.stringify(data.taskConfig) 
          : data.taskConfig);
      }

      if (fields.length === 0) {
        throw new Error('没有要更新的字段');
      }

      values.push(id);
      const query = `UPDATE collect_tasks SET ${fields.join(', ')} WHERE id = ?`;
      
      await db.query(query, values);
      logger.info('采集任务更新成功', { id });
      
      return await this.findById(id);
    } catch (error) {
      logger.error('更新采集任务失败:', error);
      throw error;
    }
  }

  // 删除采集任务
  static async delete(id) {
    try {
      const query = 'DELETE FROM collect_tasks WHERE id = ?';
      await db.query(query, [id]);
      logger.info('采集任务删除成功', { id });
      return true;
    } catch (error) {
      logger.error('删除采集任务失败:', error);
      throw error;
    }
  }

  // 获取统计信息
  static async getStats(sourceId = null) {
    try {
      let whereClause = '';
      const params = [];

      if (sourceId) {
        whereClause = 'WHERE source_id = ?';
        params.push(sourceId);
      }

      const query = `
        SELECT 
          COUNT(*) as total_tasks,
          SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_count,
          SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as running_count,
          SUM(CASE WHEN status = 'paused' THEN 1 ELSE 0 END) as paused_count,
          SUM(run_count) as total_runs,
          SUM(success_count) as total_success,
          SUM(fail_count) as total_fails
        FROM collect_tasks 
        ${whereClause}
      `;

      const result = await db.query(query, params);
      return result.rows[0];
    } catch (error) {
      logger.error('获取采集任务统计失败:', error);
      throw error;
    }
  }
}

module.exports = CollectTask;

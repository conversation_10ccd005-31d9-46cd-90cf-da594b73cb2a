const database = require('../config/database');
const logger = require('../utils/logger');

class Video {
  constructor(data = {}) {
    this.id = data.id;
    this.title = data.title;
    this.description = data.description;
    this.coverUrl = data.cover_url || data.coverUrl;
    this.videoUrl = data.video_url || data.videoUrl;
    this.categoryId = data.category_id || data.categoryId;
    this.sourceId = data.source_id || data.sourceId;
    this.tags = data.tags || [];
    this.duration = data.duration;
    this.views = data.views || 0;
    this.rating = data.rating || 0;
    this.status = data.status || 'active';
    this.featured = data.featured || false;
    this.createdAt = data.created_at || data.createdAt;
    this.updatedAt = data.updated_at || data.updatedAt;
  }

  // 创建视频
  static async create(videoData) {
    try {
      const query = `
        INSERT INTO videos (
          title, description, cover_url, video_url, category_id, source_id,
          tags, duration, rating, views, status, featured
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        videoData.title,
        videoData.description || null,
        videoData.coverUrl || null,
        videoData.videoUrl || null,
        videoData.categoryId || null,
        videoData.sourceId || null,
        JSON.stringify(videoData.tags || []),
        videoData.duration || null,
        videoData.rating || 0,
        videoData.views || 0,
        videoData.status || 'active',
        videoData.featured || false
      ];

      const result = await database.query(query, values);
      logger.db('CREATE', 'videos', { id: result.insertId });

      // 获取创建的视频
      return await this.findById(result.insertId);
    } catch (error) {
      logger.error('Error creating video:', error);
      throw error;
    }
  }

  // 根据ID查找视频
  static async findById(id) {
    try {
      const query = `
        SELECT v.*, c.name as category_name
        FROM videos v
        LEFT JOIN categories c ON v.category_id = c.id
        WHERE v.id = ?
      `;

      const result = await database.query(query, [id]);

      if (result.rows.length === 0) {
        return null;
      }

      const video = new Video(result.rows[0]);
      video.categoryName = result.rows[0].category_name;

      return video;
    } catch (error) {
      logger.error('Error finding video by ID:', error);
      throw error;
    }
  }

  // 根据source_id查找视频
  static async findBySourceId(sourceId) {
    try {
      const query = `
        SELECT v.*, c.name as category_name
        FROM videos v
        LEFT JOIN categories c ON v.category_id = c.id
        WHERE v.source_id = ?
      `;

      const result = await database.query(query, [sourceId]);

      if (result.rows.length === 0) {
        return null;
      }

      const video = new Video(result.rows[0]);
      video.categoryName = result.rows[0].category_name;

      return video;
    } catch (error) {
      logger.error('Error finding video by source ID:', error);
      throw error;
    }
  }

  // 获取视频列表
  static async findAll(options = {}) {
    try {
      const {
        page = 1,
        limit = 24,
        category,
        tag,
        search,
        sort = 'latest',
        status = 'active',
        timeFilter
      } = options;

      const offset = (page - 1) * limit;
      let whereConditions = ['v.status = ?'];
      let queryParams = [status];

      // 分类筛选
      if (category) {
        whereConditions.push('v.category_id = ?');
        queryParams.push(category);
      }

      // 标签筛选
      if (tag) {
        whereConditions.push('JSON_SEARCH(v.tags, "one", ?) IS NOT NULL');
        queryParams.push(tag);
      }

      // 搜索筛选
      if (search) {
        whereConditions.push('(v.title LIKE ? OR v.description LIKE ?)');
        queryParams.push(`%${search}%`, `%${search}%`);
      }

      // 时间筛选
      if (timeFilter) {
        whereConditions.push('v.created_at >= ?');
        queryParams.push(timeFilter);
      }

      // 排序
      let orderBy = 'v.created_at DESC';
      switch (sort) {
        case 'popular':
          orderBy = 'v.views DESC, v.created_at DESC';
          break;
        case 'rating':
          orderBy = 'v.rating DESC, v.created_at DESC';
          break;
        case 'views':
          orderBy = 'v.views DESC, v.created_at DESC';
          break;
        case 'latest':
        default:
          orderBy = 'v.created_at DESC';
          break;
      }

      const whereClause = whereConditions.join(' AND ');

      // 查询总数
      const countQuery = `
        SELECT COUNT(*) as total 
        FROM videos v 
        WHERE ${whereClause}
      `;
      const countResult = await database.query(countQuery, queryParams);
      const total = countResult.rows && countResult.rows[0] ? parseInt(countResult.rows[0].total) : 0;

      // 查询数据
      const dataQuery = `
        SELECT v.*, c.name as category_name
        FROM videos v
        LEFT JOIN categories c ON v.category_id = c.id
        WHERE ${whereClause}
        ORDER BY ${orderBy}
        LIMIT ? OFFSET ?
      `;

      // 为数据查询创建新的参数数组，包含 WHERE 条件参数 + LIMIT + OFFSET
      // 确保 limit 和 offset 是整数类型（MySQL 8.0.22+ 要求）
      const dataQueryParams = [...queryParams, parseInt(limit), parseInt(offset)];
      const dataResult = await database.query(dataQuery, dataQueryParams);

      const videos = dataResult.rows.map(row => {
        const video = new Video(row);
        video.categoryName = row.category_name;
        return video;
      });

      return {
        videos,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('❌ 查找视频失败:', {
        error: {
          name: error.name,
          message: error.message,
          code: error.code || 'N/A',
          errno: error.errno || 'N/A',
          sqlState: error.sqlState || 'N/A'
        },
        options: options,
        stack: error.stack
      });
      throw error;
    }
  }

  // 更新视频
  static async update(id, updateData) {
    try {
      const fields = [];
      const values = [];

      // 动态构建更新字段
      Object.keys(updateData).forEach(key => {
        if (updateData[key] !== undefined) {
          const dbField = key === 'coverUrl' ? 'cover_url' :
                         key === 'videoUrl' ? 'video_url' :
                         key === 'categoryId' ? 'category_id' :
                         key === 'sourceId' ? 'source_id' : key;

          fields.push(`${dbField} = ?`);
          values.push(key === 'tags' ? JSON.stringify(updateData[key]) : updateData[key]);
        }
      });

      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      values.push(id);

      const query = `
        UPDATE videos
        SET ${fields.join(', ')}
        WHERE id = ?
      `;

      await database.query(query, values);
      logger.db('UPDATE', 'videos', { id });

      // 返回更新后的视频
      return await this.findById(id);
    } catch (error) {
      logger.error('Error updating video:', error);
      throw error;
    }
  }

  // 删除视频
  static async delete(id) {
    try {
      const query = 'DELETE FROM videos WHERE id = ?';
      const result = await database.query(query, [id]);

      if (result.affectedRows === 0) {
        return false;
      }

      logger.db('DELETE', 'videos', { id });
      return true;
    } catch (error) {
      logger.error('Error deleting video:', error);
      throw error;
    }
  }

  // 增加观看次数
  static async incrementViews(id) {
    try {
      const query = `
        UPDATE videos
        SET views = views + 1
        WHERE id = ?
      `;

      await database.query(query, [id]);

      // 获取更新后的观看次数
      const selectQuery = 'SELECT views FROM videos WHERE id = ?';
      const result = await database.query(selectQuery, [id]);
      return result.rows[0]?.views || 0;
    } catch (error) {
      logger.error('Error incrementing views:', error);
      throw error;
    }
  }

  // 获取推荐视频
  static async getRecommended(videoId, limit = 6) {
    try {
      const query = `
        SELECT v.*, c.name as category_name
        FROM videos v
        LEFT JOIN categories c ON v.category_id = c.id
        WHERE v.id != ? AND v.status = 'active'
        ORDER BY v.views DESC, v.rating DESC
        LIMIT ?
      `;

      const result = await database.query(query, [videoId, limit]);

      return result.rows.map(row => {
        const video = new Video(row);
        video.categoryName = row.category_name;
        return video;
      });
    } catch (error) {
      logger.error('Error getting recommended videos:', error);
      throw error;
    }
  }

  // 获取热门视频
  static async getHot(limit = 12) {
    try {
      const query = `
        SELECT v.*, c.name as category_name
        FROM videos v
        LEFT JOIN categories c ON v.category_id = c.id
        WHERE v.status = 'active'
        ORDER BY v.views DESC, v.rating DESC
        LIMIT ?
      `;

      const result = await database.query(query, [limit]);

      return result.rows.map(row => {
        const video = new Video(row);
        video.categoryName = row.category_name;
        return video;
      });
    } catch (error) {
      logger.error('Error getting hot videos:', error);
      throw error;
    }
  }

  // 获取最新视频
  static async getLatest(limit = 12) {
    try {
      const query = `
        SELECT v.*, c.name as category_name
        FROM videos v
        LEFT JOIN categories c ON v.category_id = c.id
        WHERE v.status = 'active'
        ORDER BY v.created_at DESC
        LIMIT ?
      `;

      const result = await database.query(query, [limit]);

      return result.rows.map(row => {
        const video = new Video(row);
        video.categoryName = row.category_name;
        return video;
      });
    } catch (error) {
      logger.error('Error getting latest videos:', error);
      throw error;
    }
  }

  // 获取推荐视频
  static async getFeatured(limit = 12) {
    try {
      const query = `
        SELECT v.*, c.name as category_name
        FROM videos v
        LEFT JOIN categories c ON v.category_id = c.id
        WHERE v.status = 'active' AND v.featured = true
        ORDER BY v.views DESC, v.created_at DESC
        LIMIT ?
      `;

      const result = await database.query(query, [limit]);

      return result.rows.map(row => {
        const video = new Video(row);
        video.categoryName = row.category_name;
        return video;
      });
    } catch (error) {
      logger.error('Error getting featured videos:', error);
      throw error;
    }
  }

  // 获取统计信息
  static async getStats() {
    try {
      const query = `
        SELECT
          COUNT(*) as total,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active,
          COUNT(CASE WHEN featured = true THEN 1 END) as featured,
          SUM(views) as total_views,
          AVG(rating) as avg_rating
        FROM videos
      `;

      const result = await database.query(query);
      return result.rows[0];
    } catch (error) {
      logger.error('Error getting video stats:', error);
      throw error;
    }
  }

  // 转换为JSON格式
  toJSON() {
    return {
      id: this.id,
      title: this.title,
      description: this.description,
      coverUrl: this.coverUrl,
      videoUrl: this.videoUrl,
      categoryId: this.categoryId,
      categoryName: this.categoryName,
      sourceId: this.sourceId,
      tags: Array.isArray(this.tags) ? this.tags : JSON.parse(this.tags || '[]'),
      duration: this.duration,
      views: this.views,
      rating: this.rating ? parseFloat(this.rating) : 0,  // 确保rating是数字类型
      status: this.status,
      featured: this.featured,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

module.exports = Video;

const express = require('express');
const router = express.Router();
const AdminController = require('../controllers/adminController');
const AdminVideosController = require('../controllers/adminVideosController');
const AdminCategoriesController = require('../controllers/adminCategoriesController');
const AdminApiKeysController = require('../controllers/adminApiKeysController');
const AdminCollectController = require('../controllers/adminCollectController');

const ApiDocsController = require('../controllers/apiDocsController');
const { authenticateAdmin, requirePermission } = require('../middleware/auth');
const {
  validateAdminLogin,
  validateCreateAdmin,
  validateUpdateAdmin,
  validateChangePassword,
  validatePagination,
  validateId,
  validateCreateVideo,
  validateCreateCategory,
  validateCreateApiKey
} = require('../middleware/validation');

// 公开路由（无需认证）
router.post('/login', validateAdminLogin, AdminController.login);

// API文档路由
router.get('/', (req, res) => {
  res.json({
    message: 'Admin API endpoints',
    version: '1.0.0',
    endpoints: {
      // 认证相关
      'POST /api/admin/login': '管理员登录',
      'GET /api/admin/me': '获取当前管理员信息',
      'POST /api/admin/change-password': '修改密码',

      // 仪表盘
      'GET /api/admin/dashboard': '获取仪表盘数据',

      // 管理员管理
      'GET /api/admin/admins': '获取管理员列表',
      'POST /api/admin/admins': '创建管理员',
      'PUT /api/admin/admins/:id': '更新管理员',
      'DELETE /api/admin/admins/:id': '删除管理员',

      // 其他管理功能
      'GET /api/admin/videos': '管理视频',
      'GET /api/admin/categories': '管理分类',
      'GET /api/admin/api-keys': '管理API密钥',


    },
    authentication: 'Bearer Token (JWT)',
    rateLimit: {
      login: '每15分钟最多5次登录尝试',
      general: '每分钟最多100次请求'
    }
  });
});

// 需要认证的路由
router.use(authenticateAdmin);

// 当前管理员信息
router.get('/me', AdminController.getCurrentAdmin);

// 修改密码
router.post('/change-password', validateChangePassword, AdminController.changePassword);

// 仪表盘
router.get('/dashboard', AdminController.getDashboard);

// 管理员管理路由
router.get('/admins', validatePagination, AdminController.getAdmins);
router.post('/admins', validateCreateAdmin, requirePermission('admin.create'), AdminController.createAdmin);
router.put('/admins/:id', validateUpdateAdmin, requirePermission('admin.update'), AdminController.updateAdmin);
router.delete('/admins/:id', validateId, requirePermission('admin.delete'), AdminController.deleteAdmin);

// 视频管理路由
router.get('/videos', validatePagination, AdminVideosController.getVideos);
router.get('/videos/stats', AdminVideosController.getVideoStats);
router.get('/videos/:id', validateId, AdminVideosController.getVideoById);
router.post('/videos', validateCreateVideo, requirePermission('video.create'), AdminVideosController.createVideo);
router.put('/videos/:id', validateId, requirePermission('video.update'), AdminVideosController.updateVideo);
router.delete('/videos/:id', validateId, requirePermission('video.delete'), AdminVideosController.deleteVideo);
router.post('/videos/batch', requirePermission('video.batch'), AdminVideosController.batchOperation);

// 分类管理路由
router.get('/categories', validatePagination, AdminCategoriesController.getCategories);
router.get('/categories/stats', AdminCategoriesController.getCategoryStats);
router.get('/categories/:id', validateId, AdminCategoriesController.getCategoryById);
router.post('/categories', validateCreateCategory, requirePermission('category.create'), AdminCategoriesController.createCategory);
router.put('/categories/:id', validateId, requirePermission('category.update'), AdminCategoriesController.updateCategory);
router.delete('/categories/:id', validateId, requirePermission('category.delete'), AdminCategoriesController.deleteCategory);
router.post('/categories/batch', requirePermission('category.batch'), AdminCategoriesController.batchOperation);
router.post('/categories/sort', requirePermission('category.update'), AdminCategoriesController.updateSortOrder);

// API密钥管理路由
router.get('/api-keys', validatePagination, AdminApiKeysController.getApiKeys);
router.get('/api-keys/:id', validateId, AdminApiKeysController.getApiKeyById);
router.get('/api-keys/:id/stats', validateId, AdminApiKeysController.getApiKeyStats);
router.post('/api-keys', validateCreateApiKey, requirePermission('apikey.create'), AdminApiKeysController.createApiKey);
router.put('/api-keys/:id', validateId, requirePermission('apikey.update'), AdminApiKeysController.updateApiKey);
router.delete('/api-keys/:id', validateId, requirePermission('apikey.delete'), AdminApiKeysController.deleteApiKey);
router.post('/api-keys/:id/regenerate', validateId, requirePermission('apikey.regenerate'), AdminApiKeysController.regenerateApiKey);
router.post('/api-keys/batch', requirePermission('apikey.batch'), AdminApiKeysController.batchOperation);

// 采集管理路由
router.post('/collect/single-video', requirePermission('video.create'), AdminCollectController.collectSingleVideo);

// API文档路由
router.get('/api-docs/frontend', ApiDocsController.getFrontendApiDocs);
router.get('/api-docs/upload', ApiDocsController.getUploadApiDocs);



module.exports = router;

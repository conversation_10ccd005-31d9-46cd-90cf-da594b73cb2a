const express = require('express');
const router = express.Router();
const Category = require('../../models/Category');
const logger = require('../../utils/logger');

// 获取所有分类（支持分页）
router.get('/', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const search = req.query.search || '';

    const options = {
      page,
      limit,
      status: 'active'
    };

    if (search) {
      options.search = search;
    }

    const result = await Category.findAll(options);

    // 如果没有分页参数，返回简单列表格式
    if (!req.query.page && !req.query.limit) {
      res.json({
        success: true,
        data: result.map ? result.map(category => category.toJSON()) : result.categories.map(category => category.toJSON())
      });
    } else {
      // 返回分页格式
      res.json({
        success: true,
        data: {
          categories: result.categories.map(category => category.toJSON()),
          pagination: result.pagination
        }
      });
    }
  } catch (error) {
    logger.error('Error fetching categories:', error);
    res.status(500).json({
      success: false,
      message: '获取分类列表失败'
    });
  }
});

// 根据ID获取分类详情
router.get('/:id', async (req, res) => {
  try {
    const id = parseInt(req.params.id);

    if (isNaN(id)) {
      return res.status(400).json({
        success: false,
        message: '无效的分类ID'
      });
    }

    const category = await Category.findById(id);

    if (!category) {
      return res.status(404).json({
        success: false,
        message: '分类不存在'
      });
    }

    res.json({
      success: true,
      data: category.toJSON()
    });
  } catch (error) {
    logger.error('Error fetching category:', error);
    res.status(500).json({
      success: false,
      message: '获取分类详情失败'
    });
  }
});

// 获取分类下的视频列表（支持分页）
router.get('/:id/videos', async (req, res) => {
  try {
    const categoryId = parseInt(req.params.id);
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 24;
    const sort = req.query.sort || 'latest';

    if (isNaN(categoryId)) {
      return res.status(400).json({
        success: false,
        message: '无效的分类ID'
      });
    }

    // 首先检查分类是否存在
    const category = await Category.findById(categoryId);
    if (!category) {
      return res.status(404).json({
        success: false,
        message: '分类不存在'
      });
    }

    // 导入Video模型
    const Video = require('../../models/Video');

    const options = {
      page,
      limit,
      category: categoryId,
      sort,
      status: 'active'
    };

    const result = await Video.findAll(options);

    res.json({
      success: true,
      data: {
        category: category.toJSON(),
        videos: result.videos.map(video => video.toJSON()),
        pagination: result.pagination
      }
    });
  } catch (error) {
    logger.error('Error fetching category videos:', error);
    res.status(500).json({
      success: false,
      message: '获取分类视频失败'
    });
  }
});

module.exports = router;

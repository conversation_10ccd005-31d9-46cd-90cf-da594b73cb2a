const express = require('express');
const router = express.Router();

// 导入子路由
const videosRouter = require('./videos');
const categoriesRouter = require('./categories');
const searchRouter = require('./search');
const rankingsRouter = require('./rankings');
const collectRouter = require('./collect');
const liveRouter = require('./live');
const proxyRouter = require('./proxy');



// API文档路由
router.get('/', (req, res) => {
  res.json({
    message: '91JSPG.COM API v1.0',
    documentation: {
      videos: '/api/videos',
      categories: '/api/categories',
      search: '/api/search',
      rankings: '/api/rankings',
      collect: '/api/collect',
      live: '/api/live',
      proxy: '/api/proxy'
    },
    endpoints: {
      'GET /api/videos': '获取视频列表',
      'GET /api/videos/:id': '获取视频详情',
      'GET /api/categories': '获取分类列表',
      'GET /api/categories/:id': '获取分类详情',
      'GET /api/search': '搜索视频',
      'GET /api/rankings': '获取排行榜',
      'GET /api/collect': '采集接口文档',
      'GET /api/collect/vod': '视频采集接口',
      'GET /api/collect/art': '文章采集接口',
      'GET /api/live/platforms': '获取直播平台列表',
      'GET /api/live/:platform/rooms': '获取平台房间列表',
      'GET /api/proxy/stream': '获取流媒体代理信息',
      'GET /api/proxy/direct': '直接代理HTTP-FLV流'
    },
    version: '1.0.0',
    status: 'active'
  });
});

// 注册子路由
router.use('/videos', videosRouter);
router.use('/categories', categoriesRouter);
router.use('/search', searchRouter);
router.use('/rankings', rankingsRouter);
router.use('/collect', collectRouter);
router.use('/live', liveRouter);
router.use('/proxy', proxyRouter);

module.exports = router;

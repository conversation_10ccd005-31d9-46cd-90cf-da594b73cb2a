const express = require('express');
const router = express.Router();
const liveController = require('../../controllers/liveController');
const liveApiProtection = require('../../middleware/liveApiProtection');

// 应用直播API防护中间件
router.use(liveApiProtection);

/**
 * @route GET /api/live/platforms
 * @desc 获取直播平台列表
 * @access Protected (需要有效的Referer)
 */
router.get('/platforms', liveController.getPlatforms);

/**
 * @route GET /api/live/:platform/rooms
 * @desc 获取指定平台的房间列表
 * @access Protected (需要有效的Referer)
 */
router.get('/:platform/rooms', liveController.getPlatformRooms);

/**
 * @route GET /api/live/stats
 * @desc 获取直播统计信息
 * @access Protected (需要有效的Referer)
 */
router.get('/stats', async (req, res) => {
  try {
    // 这里可以添加统计逻辑
    res.json({
      success: true,
      stats: {
        message: 'Stats endpoint ready for implementation'
      },
      timestamp: Date.now()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to get stats'
    });
  }
});

module.exports = router;

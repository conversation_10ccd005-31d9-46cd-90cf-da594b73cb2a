const express = require('express');
const router = express.Router();
const proxyController = require('../../controllers/proxyController');
const liveApiProtection = require('../../middleware/liveApiProtection');

/**
 * @route GET /api/proxy/collect
 * @desc 苹果CMS接口代理 - 解决CORS问题
 * @access Public (不需要Referer验证)
 */
router.get('/collect', proxyController.collectProxy);

// 应用直播API防护中间件到其他代理路由
router.use(liveApiProtection);

/**
 * @route GET /api/proxy/stream
 * @desc 获取流媒体代理信息
 * @access Protected (需要有效的Referer)
 */
router.get('/stream', proxyController.streamProxy);

/**
 * @route GET /api/proxy/direct
 * @desc 直接代理HTTP-FLV流
 * @access Protected (需要有效的Referer)
 */
router.get('/direct', proxyController.directProxy);

/**
 * @route POST /api/proxy/disconnect
 * @desc 强制断开指定流的连接
 * @access Protected (需要有效的Referer)
 */
router.post('/disconnect', proxyController.forceDisconnectStream);

/**
 * @route GET /api/proxy/stats
 * @desc 获取代理统计信息
 * @access Protected (需要有效的Referer)
 */
router.get('/stats', proxyController.getStats);



module.exports = router;

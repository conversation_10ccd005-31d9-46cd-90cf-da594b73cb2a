const express = require('express');
const router = express.Router();

// 临时占位路由
router.get('/', (req, res) => {
  const type = req.query.type || 'hot';
  const limit = parseInt(req.query.limit) || 50;

  res.json({
    success: true,
    data: {
      type,
      videos: [
        {
          id: 1,
          title: 'SSIS-123 美女教师的秘密课程',
          coverUrl: 'https://example.com/cover1.jpg',
          category: '角色剧情',
          duration: '2:15:30',
          views: 125000,
          rating: 9.2,
          rankingValue: 98500
        },
        {
          id: 2,
          title: 'MIDE-456 办公室恋情物语',
          coverUrl: 'https://example.com/cover2.jpg',
          category: '制服诱惑',
          duration: '1:58:45',
          views: 98000,
          rating: 8.9,
          rankingValue: 87600
        }
      ]
    }
  });
});

module.exports = router;

const express = require('express');
const router = express.Router();
const logger = require('../../utils/logger');
const database = require('../../config/database');
const Video = require('../../models/Video');

// 搜索视频
router.get('/', async (req, res) => {
  try {
    const keyword = req.query.q || '';
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 24;
    const sort = req.query.sort || 'latest';

    if (!keyword.trim()) {
      return res.json({
        success: true,
        data: {
          keyword,
          videos: [],
          pagination: {
            page,
            limit,
            total: 0,
            totalPages: 0
          }
        }
      });
    }

    logger.api('SEARCH_VIDEOS', { keyword, page, limit, sort });

    const options = {
      page,
      limit,
      search: keyword,
      sort,
      status: 'active'
    };

    const result = await Video.findAll(options);

    res.json({
      success: true,
      data: {
        keyword,
        videos: result.videos.map(video => video.toJSON()),
        pagination: result.pagination
      }
    });
  } catch (error) {
    logger.error('Error searching videos:', error);
    res.status(500).json({
      success: false,
      message: '搜索失败'
    });
  }
});



module.exports = router;

const express = require('express');
const router = express.Router();
const videosController = require('../../controllers/videosController');
const { cache } = require('../../middleware/cache');

// 获取视频列表 - 短缓存时间，因为内容经常变化
router.get('/', cache(60), videosController.getVideos);

// 获取热门视频 - 中等缓存时间
router.get('/featured/hot', cache(180), videosController.getHotVideos);

// 获取最新视频 - 短缓存时间，因为经常更新
router.get('/featured/latest', cache(60), videosController.getLatestVideos);

// 获取推荐视频 - 中等缓存时间
router.get('/featured/recommended', cache(180), videosController.getFeaturedVideos);

// 获取视频统计信息 - 放在动态路由之前
router.get('/stats', cache(300), videosController.getVideoStats);

// 获取视频详情 - 长缓存时间，因为详情不经常变化
router.get('/:id', cache(600), videosController.getVideoById);

// 增加视频观看次数
router.post('/:id/view', videosController.incrementViews);

// 获取推荐视频
router.get('/:id/recommended', videosController.getRecommendedVideos);

module.exports = router;

const express = require('express');
const router = express.Router();
const UploadController = require('../controllers/uploadController');
const { authenticateApiKey } = require('../middleware/apiAuth');
const {
  validateUploadVideo,
  validateBatchUpload,
  validateUpdateVideo,
  validateDeleteVideo
} = require('../middleware/uploadValidation');



// API接口信息 (无需认证)
router.get('/', (req, res) => {
  res.json({
    message: 'Upload API endpoints',
    endpoints: {
      'POST /api/upload/video': '上传单个视频',
      'POST /api/upload/batch': '批量上传视频',
      'PUT /api/upload/update/:id': '更新视频信息',
      'DELETE /api/upload/delete/:id': '删除视频',
      'GET /api/upload/stats': '获取上传统计'
    },
    authentication: {
      type: 'API Key',
      header: 'Authorization',
      format: 'Bearer {api_key}',
      note: '需要在管理后台创建API密钥'
    },
    rateLimit: {
      window: '1分钟',
      max: '60次请求'
    }
  });
});

// 应用认证到所有需要认证的路由
router.use(authenticateApiKey);

// 上传单个视频
router.post('/video', validateUploadVideo, UploadController.uploadVideo);

// 批量上传视频
router.post('/batch', validateBatchUpload, UploadController.batchUpload);

// 更新视频信息
router.put('/update/:id', validateUpdateVideo, UploadController.updateVideo);

// 删除视频
router.delete('/delete/:id', validateDeleteVideo, UploadController.deleteVideo);

// 获取上传统计
router.get('/stats', UploadController.getUploadStats);

module.exports = router;

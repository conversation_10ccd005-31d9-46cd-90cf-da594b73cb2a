require('dotenv').config();
const database = require('../config/database');
const logger = require('../utils/logger');

async function checkSettings() {
  try {
    logger.info('检查设置数据...');
    
    // 连接数据库
    await database.connect();
    
    // 查询所有设置
    const query = 'SELECT * FROM settings ORDER BY category, `key`';
    const result = await database.query(query);
    const results = result && result.rows ? result.rows : [];

    console.log('数据库查询结果:');
    console.log('结果类型:', typeof result);
    console.log('有rows属性:', result && result.rows ? true : false);
    console.log('rows长度:', results.length);

    if (results && results.length > 0) {
      console.log('\n所有设置数据:');
      for (const row of results) {
        console.log('---');
        console.log('ID:', row.id);
        console.log('Key:', row.key);
        console.log('Value:', row.value);
        console.log('Value Type:', typeof row.value);
        console.log('Category:', row.category);
        console.log('Description:', row.description);
        console.log('Type:', row.type);
        console.log('Created:', row.created_at);
        console.log('Updated:', row.updated_at);
      }
      
      // 测试格式化逻辑
      console.log('\n测试格式化逻辑:');
      const formatted = {};
      
      for (const setting of results) {
        if (!formatted[setting.category]) {
          formatted[setting.category] = {};
        }
        
        console.log(`处理设置: ${setting.key}, 原始值: ${setting.value}, 类型: ${typeof setting.value}`);
        
        // 解析JSON值
        try {
          const parsedValue = JSON.parse(setting.value);
          formatted[setting.category][setting.key] = parsedValue;
          console.log(`  解析后: ${parsedValue} (${typeof parsedValue})`);
        } catch (e) {
          // 如果不是JSON，直接使用字符串值
          formatted[setting.category][setting.key] = setting.value;
          console.log(`  直接使用: ${setting.value} (${typeof setting.value})`);
        }
      }
      
      console.log('\n最终格式化结果:');
      console.log(JSON.stringify(formatted, null, 2));
    } else {
      console.log('没有找到设置数据或查询结果格式不正确');
    }
    
    return true;
  } catch (error) {
    logger.error('检查设置数据失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkSettings()
    .then(() => {
      logger.info('检查设置数据脚本执行成功');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('检查设置数据脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { checkSettings };

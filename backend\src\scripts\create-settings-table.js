require('dotenv').config();
const database = require('../config/database');
const logger = require('../utils/logger');

async function createSettingsTable() {
  try {
    logger.info('Creating settings table in video_cms database...');
    
    // 连接数据库
    await database.connect();
    
    // 删除现有的settings表
    await database.query('DROP TABLE IF EXISTS settings');
    logger.info('Dropped existing settings table');
    
    // 创建新的settings表 - 将 value 字段改为 TEXT 类型
    const createTableSQL = `
      CREATE TABLE settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        \`key\` VARCHAR(100) UNIQUE NOT NULL,
        value TEXT,
        category VARCHAR(50) DEFAULT 'general',
        description TEXT,
        type VARCHAR(20) DEFAULT 'string',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `;
    
    await database.query(createTableSQL);
    logger.info('Created new settings table');
    
    // 创建索引
    await database.query('CREATE INDEX idx_settings_key ON settings(`key`)');
    await database.query('CREATE INDEX idx_settings_category ON settings(category)');
    await database.query('CREATE INDEX idx_settings_type ON settings(type)');
    logger.info('Created indexes');
    
    logger.info('Settings table created successfully in video_cms database');
    return true;
  } catch (error) {
    logger.error('Settings table creation failed:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  createSettingsTable()
    .then(() => {
      logger.info('Create settings table script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Create settings table script failed:', error);
      process.exit(1);
    });
}

module.exports = { createSettingsTable };

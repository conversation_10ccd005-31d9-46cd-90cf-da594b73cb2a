require('dotenv').config();
const database = require('../config/database');
const logger = require('../utils/logger');

// 数据库表结构定义 (MySQL语法)
const migrations = [
  {
    name: 'create_categories_table',
    sql: `
      CREATE TABLE IF NOT EXISTS categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        slug VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        cover_url VARCHAR(500),
        sort_order INT DEFAULT 0,
        status VARCHAR(20) DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      );

      CREATE INDEX idx_categories_status ON categories(status);
      CREATE INDEX idx_categories_sort_order ON categories(sort_order);
    `
  },
  {
    name: 'create_videos_table',
    sql: `
      CREATE TABLE IF NOT EXISTS videos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        cover_url VARCHAR(500),
        video_url VARCHAR(500),
        category_id INT,
        tags JSON,
        duration VARCHAR(20),
        views INT DEFAULT 0,
        rating DECIMAL(3,2) DEFAULT 0,
        status VARCHAR(20) DEFAULT 'active',
        featured BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
      );

      CREATE INDEX idx_videos_category_id ON videos(category_id);
      CREATE INDEX idx_videos_status ON videos(status);
      CREATE INDEX idx_videos_featured ON videos(featured);
      CREATE INDEX idx_videos_created_at ON videos(created_at);
      CREATE INDEX idx_videos_views ON videos(views);
      CREATE INDEX idx_videos_rating ON videos(rating);
    `
  },
  {
    name: 'create_api_keys_table',
    sql: `
      CREATE TABLE IF NOT EXISTS api_keys (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        key_value VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        status VARCHAR(20) DEFAULT 'active',
        last_used_at TIMESTAMP NULL,
        usage_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      );

      CREATE INDEX idx_api_keys_key_value ON api_keys(key_value);
      CREATE INDEX idx_api_keys_status ON api_keys(status);
    `
  },
  {
    name: 'create_admins_table',
    sql: `
      CREATE TABLE IF NOT EXISTS admins (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        username VARCHAR(50) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        status VARCHAR(20) DEFAULT 'active',
        last_login_at TIMESTAMP NULL,
        login_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      );

      CREATE INDEX idx_admins_username ON admins(username);
      CREATE INDEX idx_admins_status ON admins(status);
    `
  },
  {
    name: 'create_tags_table',
    sql: `
      CREATE TABLE IF NOT EXISTS tags (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) UNIQUE NOT NULL,
        category VARCHAR(50) DEFAULT 'other',
        count INT DEFAULT 0,
        is_hot BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      );

      CREATE INDEX idx_tags_name ON tags(name);
      CREATE INDEX idx_tags_category ON tags(category);
      CREATE INDEX idx_tags_count ON tags(count);
      CREATE INDEX idx_tags_is_hot ON tags(is_hot);
    `
  },
  {
    name: 'recreate_settings_table',
    sql: `
      DROP TABLE IF EXISTS settings;

      CREATE TABLE settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        \`key\` VARCHAR(100) UNIQUE NOT NULL,
        value JSON,
        category VARCHAR(50) DEFAULT 'general',
        description TEXT,
        type VARCHAR(20) DEFAULT 'string',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      );

      CREATE INDEX idx_settings_key ON settings(\`key\`);
      CREATE INDEX idx_settings_category ON settings(category);
      CREATE INDEX idx_settings_type ON settings(type);
    `
  },
  {
    name: 'create_api_usage_logs_table',
    sql: `
      CREATE TABLE IF NOT EXISTS api_usage_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        api_key_id INT,
        endpoint VARCHAR(255),
        method VARCHAR(10),
        ip_address VARCHAR(45),
        user_agent TEXT,
        response_status INT,
        response_time INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE CASCADE
      );

      CREATE INDEX idx_api_usage_logs_api_key_id ON api_usage_logs(api_key_id);
      CREATE INDEX idx_api_usage_logs_created_at ON api_usage_logs(created_at);
      CREATE INDEX idx_api_usage_logs_endpoint ON api_usage_logs(endpoint);
    `
  },
  {
    name: 'create_video_stats_table',
    sql: `
      CREATE TABLE IF NOT EXISTS video_stats (
        id INT AUTO_INCREMENT PRIMARY KEY,
        video_id INT,
        date DATE NOT NULL,
        views INT DEFAULT 0,
        unique_views INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE
      );

      CREATE UNIQUE INDEX idx_video_stats_video_date ON video_stats(video_id, date);
      CREATE INDEX idx_video_stats_date ON video_stats(date);
    `
  },
  {
    name: 'add_source_id_to_videos',
    sql: `
      ALTER TABLE videos ADD COLUMN source_id VARCHAR(100);
      CREATE INDEX idx_videos_source_id ON videos(source_id);
    `
  }
];

// 迁移状态表 (MySQL语法)
const createMigrationsTable = `
  CREATE TABLE IF NOT EXISTS migrations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );
`;

// 检查迁移是否已执行
async function isMigrationExecuted(name) {
  try {
    const result = await database.query(
      'SELECT id FROM migrations WHERE name = ?',
      [name]
    );
    return result.rows.length > 0;
  } catch (error) {
    return false;
  }
}

// 记录迁移执行
async function recordMigration(name) {
  try {
    await database.query(
      'INSERT INTO migrations (name) VALUES (?)',
      [name]
    );
  } catch (error) {
    logger.error(`Error recording migration ${name}:`, error);
  }
}

// 执行单个迁移
async function executeMigration(migration) {
  try {
    logger.info(`Executing migration: ${migration.name}`);

    // 将SQL语句按分号分割，分别执行
    const statements = migration.sql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    for (const statement of statements) {
      if (statement.trim()) {
        await database.query(statement);
      }
    }

    await recordMigration(migration.name);

    logger.info(`Migration completed: ${migration.name}`);
    return true;
  } catch (error) {
    logger.error(`Migration failed: ${migration.name}`, error);
    throw error;
  }
}

// 执行所有迁移
async function runMigrations() {
  try {
    logger.info('Starting database migrations...');
    
    // 连接数据库
    await database.connect();
    
    // 创建迁移状态表
    await database.query(createMigrationsTable);
    
    let executedCount = 0;
    
    // 执行所有迁移
    for (const migration of migrations) {
      const isExecuted = await isMigrationExecuted(migration.name);
      
      if (!isExecuted) {
        await executeMigration(migration);
        executedCount++;
      } else {
        logger.info(`Migration already executed: ${migration.name}`);
      }
    }
    
    if (executedCount > 0) {
      logger.info(`Database migrations completed. Executed ${executedCount} migrations.`);
    } else {
      logger.info('All migrations are up to date.');
    }
    
    return true;
  } catch (error) {
    logger.error('Database migration failed:', error);
    throw error;
  }
}

// 回滚迁移（简单实现）
async function rollbackMigration(migrationName) {
  try {
    logger.warn(`Rolling back migration: ${migrationName}`);

    // 从迁移记录中删除
    await database.query(
      'DELETE FROM migrations WHERE name = ?',
      [migrationName]
    );

    logger.info(`Migration rollback completed: ${migrationName}`);
    return true;
  } catch (error) {
    logger.error(`Migration rollback failed: ${migrationName}`, error);
    throw error;
  }
}

// 获取迁移状态
async function getMigrationStatus() {
  try {
    const result = await database.query(
      'SELECT name, executed_at FROM migrations ORDER BY executed_at DESC'
    );
    
    return {
      executed: result.rows,
      pending: migrations.filter(m => 
        !result.rows.some(r => r.name === m.name)
      ).map(m => ({ name: m.name }))
    };
  } catch (error) {
    logger.error('Error getting migration status:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runMigrations()
    .then(() => {
      logger.info('Migration script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = {
  runMigrations,
  rollbackMigration,
  getMigrationStatus,
  executeMigration
};

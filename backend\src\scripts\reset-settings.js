require('dotenv').config();
const database = require('../config/database');
const logger = require('../utils/logger');

async function resetSettingsTable() {
  try {
    logger.info('Resetting settings table...');
    
    // 连接数据库
    await database.connect();
    
    // 删除旧的迁移记录
    await database.query("DELETE FROM migrations WHERE name IN ('create_settings_table', 'update_settings_table_add_type')");
    logger.info('Deleted old migration records');
    
    // 删除现有的settings表
    await database.query('DROP TABLE IF EXISTS settings');
    logger.info('Dropped existing settings table');
    
    // 创建新的settings表
    const createTableSQL = `
      CREATE TABLE settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        \`key\` VARCHAR(100) UNIQUE NOT NULL,
        value JSON,
        category VARCHAR(50) DEFAULT 'general',
        description TEXT,
        type VARCHAR(20) DEFAULT 'string',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `;
    
    await database.query(createTableSQL);
    logger.info('Created new settings table');
    
    // 创建索引
    await database.query('CREATE INDEX idx_settings_key ON settings(`key`)');
    await database.query('CREATE INDEX idx_settings_category ON settings(category)');
    await database.query('CREATE INDEX idx_settings_type ON settings(type)');
    logger.info('Created indexes');
    
    // 记录迁移
    await database.query("INSERT INTO migrations (name) VALUES ('recreate_settings_table')");
    logger.info('Recorded migration');
    
    logger.info('Settings table reset completed successfully');
    return true;
  } catch (error) {
    logger.error('Settings table reset failed:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  resetSettingsTable()
    .then(() => {
      logger.info('Reset script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Reset script failed:', error);
      process.exit(1);
    });
}

module.exports = { resetSettingsTable };

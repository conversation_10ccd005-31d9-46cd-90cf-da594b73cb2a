require('dotenv').config();
const database = require('../config/database');
const logger = require('../utils/logger');

async function seedSettings() {
  try {
    logger.info('开始初始化系统设置数据...');
    
    // 连接数据库
    await database.connect();
    
    // 清空现有设置数据
    await database.query('DELETE FROM settings');
    logger.info('清空现有设置数据');
    
    // 默认设置数据
    const defaultSettings = [
      // 基本设置
      {
        key: 'siteName',
        value: JSON.stringify('影视内容管理系统'),
        category: 'basic',
        description: '系统名称',
        type: 'string'
      },
      {
        key: 'siteDescription',
        value: JSON.stringify('高质量影视内容管理系统'),
        category: 'basic',
        description: '系统描述',
        type: 'string'
      },
      {
        key: 'contactEmail',
        value: JSON.stringify('<EMAIL>'),
        category: 'basic',
        description: '联系邮箱',
        type: 'email'
      },
      {
        key: 'siteStatus',
        value: JSON.stringify('online'),
        category: 'basic',
        description: '系统状态',
        type: 'select'
      },
      {
        key: 'copyright',
        value: JSON.stringify('© 2024 影视CMS. All rights reserved.'),
        category: 'basic',
        description: '版权信息',
        type: 'string'
      },
      
      // 显示设置
      {
        key: 'videosPerPage',
        value: JSON.stringify('24'),
        category: 'display',
        description: '每页显示影片数量',
        type: 'number'
      },
      {
        key: 'themeColor',
        value: JSON.stringify('orange'),
        category: 'display',
        description: '主题色彩',
        type: 'select'
      },
      {
        key: 'showViewCount',
        value: JSON.stringify(true),
        category: 'display',
        description: '显示观看次数',
        type: 'boolean'
      },
      {
        key: 'showDuration',
        value: JSON.stringify(true),
        category: 'display',
        description: '显示影片时长',
        type: 'boolean'
      },
      {
        key: 'showTags',
        value: JSON.stringify(true),
        category: 'display',
        description: '显示标签',
        type: 'boolean'
      }
    ];
    
    // 插入设置数据
    for (const setting of defaultSettings) {
      const insertSQL = `
        INSERT INTO settings (\`key\`, value, category, description, type)
        VALUES (?, ?, ?, ?, ?)
      `;
      
      await database.query(insertSQL, [
        setting.key,
        setting.value,
        setting.category,
        setting.description,
        setting.type
      ]);
      
      logger.info(`插入设置: ${setting.key} = ${setting.value}`);
    }
    
    logger.info(`成功插入 ${defaultSettings.length} 条设置数据`);
    
    // 验证数据
    const result = await database.query('SELECT COUNT(*) as count FROM settings');
    const count = result && result[0] ? result[0].count : 0;
    logger.info(`数据库中现有 ${count} 条设置记录`);
    
    // 显示所有设置
    const allSettings = await database.query('SELECT * FROM settings ORDER BY category, `key`');
    logger.info('当前所有设置:');
    if (allSettings && Array.isArray(allSettings)) {
      for (const setting of allSettings) {
        logger.info(`  ${setting.category}.${setting.key} = ${setting.value} (${setting.type})`);
      }
    } else {
      logger.info('  无法获取设置列表');
    }
    
    logger.info('系统设置数据初始化完成');
    return true;
  } catch (error) {
    logger.error('设置数据初始化失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  seedSettings()
    .then(() => {
      logger.info('设置数据种子脚本执行成功');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('设置数据种子脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { seedSettings };

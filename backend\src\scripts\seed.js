require('dotenv').config();
const database = require('../config/database');
const logger = require('../utils/logger');
const Admin = require('../models/Admin');
const Category = require('../models/Category');
const ApiKey = require('../models/ApiKey');
const Video = require('../models/Video');

// 默认管理员数据
const defaultAdmins = [
  {
    name: '超级管理员',
    username: 'admin',
    password: 'admin123',
    email: '<EMAIL>',
    status: 'active'
  }
];

// 默认分类数据
const defaultCategories = [
  {
    name: '热门推荐',
    slug: 'hot',
    description: '最受欢迎的视频内容',
    sortOrder: 1,
    status: 'active'
  },
  {
    name: '最新上传',
    slug: 'latest',
    description: '最新上传的视频内容',
    sortOrder: 2,
    status: 'active'
  },
  {
    name: '高清精选',
    slug: 'hd',
    description: '高清画质精选视频',
    sortOrder: 3,
    status: 'active'
  },
  {
    name: '经典收藏',
    slug: 'classic',
    description: '经典收藏视频',
    sortOrder: 4,
    status: 'active'
  }
];

// 默认API密钥数据
const defaultApiKeys = [
  {
    name: 'Python上传脚本',
    description: '用于Python脚本批量上传视频的API密钥',
    status: 'active'
  }
];

// 默认视频数据
const defaultVideos = [
  {
    title: 'SSIS-123 美女教师的秘密课程',
    description: '美女教师的秘密课程，精彩剧情不容错过。这是一部关于教师与学生之间复杂关系的作品，剧情跌宕起伏，演员表现出色。',
    coverUrl: 'https://picsum.photos/400/600?random=1',
    videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
    categoryId: 1, // 热门推荐
    tags: ['教师', '制服', '剧情'],
    duration: '2:15:30',
    views: 125000,
    rating: 9.2,
    status: 'active',
    featured: true
  },
  {
    title: 'MIDE-456 办公室恋情物语',
    description: '办公室恋情物语，职场浪漫故事。展现现代都市白领的情感生活，剧情真实感人。',
    coverUrl: 'https://picsum.photos/400/600?random=2',
    videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4',
    categoryId: 2, // 最新上传
    tags: ['办公室', '制服', '恋情'],
    duration: '1:58:45',
    views: 98000,
    rating: 8.9,
    status: 'active',
    featured: false
  },
  {
    title: 'PRED-789 秘书的诱惑',
    description: '秘书的诱惑，职场题材经典之作。精美画面配合出色演技，带来视觉盛宴。',
    coverUrl: 'https://picsum.photos/400/600?random=3',
    videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_3mb.mp4',
    categoryId: 1, // 热门推荐
    tags: ['秘书', '职场', '诱惑'],
    duration: '2:05:12',
    views: 87000,
    rating: 8.7,
    status: 'active',
    featured: true
  },
  {
    title: 'STARS-456 校园青春物语',
    description: '校园青春物语，青春校园题材。回忆青春岁月，感受纯真美好的校园时光。',
    coverUrl: 'https://picsum.photos/400/600?random=4',
    videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_4mb.mp4',
    categoryId: 3, // 高清精选
    tags: ['校园', '青春', '学生'],
    duration: '1:45:30',
    views: 65000,
    rating: 8.5,
    status: 'active',
    featured: true
  },
  {
    title: 'JUL-234 成熟女性的魅力',
    description: '成熟女性的魅力，展现成熟女性的独特韵味。优雅气质与成熟魅力的完美结合。',
    coverUrl: 'https://picsum.photos/400/600?random=5',
    videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_5mb.mp4',
    categoryId: 4, // 经典收藏
    tags: ['成熟', '魅力', '优雅'],
    duration: '2:20:15',
    views: 112000,
    rating: 9.0,
    status: 'active',
    featured: false
  },
  {
    title: 'CAWD-123 清纯少女的初体验',
    description: '清纯少女的初体验，青春活力的完美展现。清新自然的表演风格令人印象深刻。',
    coverUrl: 'https://picsum.photos/400/600?random=6',
    videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_6mb.mp4',
    categoryId: 2, // 最新上传
    tags: ['清纯', '少女', '青春'],
    duration: '1:52:40',
    views: 78000,
    rating: 8.6,
    status: 'active',
    featured: false
  }
];

// 创建默认管理员
async function seedAdmins() {
  try {
    logger.info('开始创建默认管理员...');
    
    for (const adminData of defaultAdmins) {
      // 检查管理员是否已存在
      const existingAdmin = await Admin.findByUsername(adminData.username);
      
      if (!existingAdmin) {
        const admin = await Admin.create(adminData);
        logger.info(`创建管理员成功: ${admin.username} (ID: ${admin.id})`);
      } else {
        logger.info(`管理员已存在: ${adminData.username}`);
      }
    }
    
    logger.info('默认管理员创建完成');
  } catch (error) {
    logger.error('创建默认管理员失败:', error);
    throw error;
  }
}

// 创建默认分类
async function seedCategories() {
  try {
    logger.info('开始创建默认分类...');
    
    for (const categoryData of defaultCategories) {
      // 检查分类是否已存在
      const existingCategory = await Category.findBySlug(categoryData.slug);
      
      if (!existingCategory) {
        const category = await Category.create(categoryData);
        logger.info(`创建分类成功: ${category.name} (ID: ${category.id})`);
      } else {
        logger.info(`分类已存在: ${categoryData.name}`);
      }
    }
    
    logger.info('默认分类创建完成');
  } catch (error) {
    logger.error('创建默认分类失败:', error);
    throw error;
  }
}

// 创建默认API密钥
async function seedApiKeys() {
  try {
    logger.info('开始创建默认API密钥...');
    
    for (const apiKeyData of defaultApiKeys) {
      // 检查是否已有同名API密钥
      const existingKeys = await ApiKey.findAll({ name: apiKeyData.name });
      
      if (existingKeys.length === 0) {
        const apiKey = await ApiKey.create(apiKeyData);
        logger.info(`创建API密钥成功: ${apiKey.name} (ID: ${apiKey.id})`);
        logger.info(`API密钥值: ${apiKey.keyValue}`);
      } else {
        logger.info(`API密钥已存在: ${apiKeyData.name}`);
      }
    }
    
    logger.info('默认API密钥创建完成');
  } catch (error) {
    logger.error('创建默认API密钥失败:', error);
    throw error;
  }
}

// 创建默认视频
async function seedVideos() {
  try {
    logger.info('开始创建默认视频...');

    for (const videoData of defaultVideos) {
      // 检查视频是否已存在（通过标题）
      const existingVideos = await Video.findAll({ search: videoData.title, limit: 1 });

      if (existingVideos.length === 0 || (existingVideos.videos && existingVideos.videos.length === 0)) {
        const video = await Video.create(videoData);
        logger.info(`创建视频成功: ${video.title} (ID: ${video.id})`);
      } else {
        logger.info(`视频已存在: ${videoData.title}`);
      }
    }

    logger.info('默认视频创建完成');
  } catch (error) {
    logger.error('创建默认视频失败:', error);
    throw error;
  }
}

// 创建默认设置
async function seedSettings() {
  try {
    logger.info('开始创建默认设置...');
    
    const defaultSettings = [
      {
        key: 'site_name',
        value: JSON.stringify('91JSPG.COM'),
        category: 'basic',
        description: '网站名称'
      },
      {
        key: 'site_description',
        value: JSON.stringify('最优质的成人影片平台'),
        category: 'basic',
        description: '网站描述'
      },
      {
        key: 'site_keywords',
        value: JSON.stringify('JAV,成人影片,高清影片'),
        category: 'basic',
        description: '网站关键词'
      },
      {
        key: 'videos_per_page',
        value: JSON.stringify(20),
        category: 'display',
        description: '每页显示视频数量'
      },
      {
        key: 'enable_registration',
        value: JSON.stringify(false),
        category: 'security',
        description: '是否允许用户注册'
      }
    ];

    for (const setting of defaultSettings) {
      // 检查设置是否已存在
      const query = 'SELECT id FROM settings WHERE `key` = ?';
      const result = await database.query(query, [setting.key]);
      
      if (result.rows.length === 0) {
        const insertQuery = `
          INSERT INTO settings (\`key\`, value, category, description)
          VALUES (?, ?, ?, ?)
        `;
        
        await database.query(insertQuery, [
          setting.key,
          setting.value,
          setting.category,
          setting.description
        ]);
        
        logger.info(`创建设置成功: ${setting.key}`);
      } else {
        logger.info(`设置已存在: ${setting.key}`);
      }
    }
    
    logger.info('默认设置创建完成');
  } catch (error) {
    logger.error('创建默认设置失败:', error);
    throw error;
  }
}

// 执行所有种子数据创建
async function runSeeds() {
  try {
    logger.info('开始执行种子数据创建...');
    
    // 连接数据库
    await database.connect();
    
    // 按顺序执行种子数据创建
    await seedAdmins();
    await seedCategories();
    await seedApiKeys();
    await seedVideos();
    await seedSettings();
    
    logger.info('所有种子数据创建完成');
    return true;
  } catch (error) {
    logger.error('种子数据创建失败:', error);
    throw error;
  }
}

// 清空所有数据（危险操作，仅用于开发环境）
async function clearAllData() {
  if (process.env.NODE_ENV === 'production') {
    throw new Error('生产环境不允许清空数据');
  }
  
  try {
    logger.warn('开始清空所有数据...');
    
    const tables = [
      'api_usage_logs',
      'video_stats', 
      'videos',
      'api_keys',
      'admins',
      'categories',
      'tags',
      'settings',
      'migrations'
    ];
    
    // 禁用外键检查
    await database.query('SET FOREIGN_KEY_CHECKS = 0');
    
    for (const table of tables) {
      await database.query(`TRUNCATE TABLE ${table}`);
      logger.info(`清空表: ${table}`);
    }
    
    // 重新启用外键检查
    await database.query('SET FOREIGN_KEY_CHECKS = 1');
    
    logger.warn('所有数据清空完成');
  } catch (error) {
    logger.error('清空数据失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const command = process.argv[2];
  
  if (command === 'clear') {
    clearAllData()
      .then(() => {
        logger.info('数据清空脚本执行完成');
        process.exit(0);
      })
      .catch((error) => {
        logger.error('数据清空脚本执行失败:', error);
        process.exit(1);
      });
  } else {
    runSeeds()
      .then(() => {
        logger.info('种子数据脚本执行完成');
        process.exit(0);
      })
      .catch((error) => {
        logger.error('种子数据脚本执行失败:', error);
        process.exit(1);
      });
  }
}

module.exports = {
  runSeeds,
  clearAllData,
  seedAdmins,
  seedCategories,
  seedApiKeys,
  seedVideos,
  seedSettings
};

require('dotenv').config();
const database = require('../config/database');
const logger = require('../utils/logger');

async function simpleSeedSettings() {
  try {
    logger.info('开始插入系统设置数据...');
    
    // 连接数据库
    await database.connect();
    
    // 默认设置数据
    const defaultSettings = [
      // 基本设置 - 使用 JSON.stringify 确保正确的 JSON 格式
      {
        key: 'siteName',
        value: JSON.stringify('影视内容管理系统'),
        category: 'basic',
        description: '系统名称',
        type: 'string'
      },
      {
        key: 'siteDescription',
        value: JSON.stringify('高质量影视内容管理系统'),
        category: 'basic',
        description: '系统描述',
        type: 'string'
      },
      {
        key: 'contactEmail',
        value: JSON.stringify('<EMAIL>'),
        category: 'basic',
        description: '联系邮箱',
        type: 'email'
      },
      {
        key: 'siteStatus',
        value: JSON.stringify('online'),
        category: 'basic',
        description: '系统状态',
        type: 'select'
      },
      {
        key: 'copyright',
        value: JSON.stringify('© 2024 影视CMS. All rights reserved.'),
        category: 'basic',
        description: '版权信息',
        type: 'string'
      },

      // 显示设置
      {
        key: 'videosPerPage',
        value: JSON.stringify('24'),
        category: 'display',
        description: '每页显示影片数量',
        type: 'number'
      },
      {
        key: 'themeColor',
        value: JSON.stringify('orange'),
        category: 'display',
        description: '主题色彩',
        type: 'select'
      },
      {
        key: 'showViewCount',
        value: JSON.stringify(true),
        category: 'display',
        description: '显示观看次数',
        type: 'boolean'
      },
      {
        key: 'showDuration',
        value: JSON.stringify(true),
        category: 'display',
        description: '显示影片时长',
        type: 'boolean'
      },
      {
        key: 'showTags',
        value: JSON.stringify(true),
        category: 'display',
        description: '显示标签',
        type: 'boolean'
      }
    ];
    
    // 插入设置数据
    for (const setting of defaultSettings) {
      try {
        const insertSQL = `
          INSERT INTO settings (\`key\`, value, category, description, type)
          VALUES (?, ?, ?, ?, ?)
        `;
        
        console.log(`插入设置: ${setting.key} = ${setting.value} (${setting.category})`);
        console.log('插入参数:', {
          key: setting.key,
          value: setting.value,
          valueType: typeof setting.value,
          category: setting.category,
          description: setting.description,
          type: setting.type
        });

        const result = await database.query(insertSQL, [
          setting.key,
          setting.value,
          setting.category,
          setting.description,
          setting.type
        ]);

        console.log('插入结果:', result);
        
        logger.info(`✓ 成功插入: ${setting.key}`);
      } catch (error) {
        logger.error(`✗ 插入失败: ${setting.key}`, error);
      }
    }
    
    logger.info('设置数据插入完成');
    return true;
  } catch (error) {
    logger.error('设置数据插入失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  simpleSeedSettings()
    .then(() => {
      logger.info('简单设置数据种子脚本执行成功');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('简单设置数据种子脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { simpleSeedSettings };

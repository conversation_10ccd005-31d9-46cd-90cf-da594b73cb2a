const axios = require('axios');
const Video = require('../models/Video');
const CollectLog = require('../models/CollectLog');
const CollectTask = require('../models/CollectTask');
const CollectSource = require('../models/CollectSource');
const logger = require('../utils/logger');

class CollectProcessor {
  constructor() {
    this.runningTasks = new Map(); // 存储正在运行的任务
    this.initialized = false;
  }

  // 初始化处理器 - 同步数据库状态
  async initialize() {
    if (this.initialized) return;

    try {
      logger.info('🔄 初始化 CollectProcessor，同步数据库状态...');

      // 查找所有数据库中状态为 'running' 的任务
      const runningTasksInDB = await CollectTask.findAll({
        status: 'running',
        limit: 100
      });

      logger.info('📋 发现数据库中的运行状态任务:', {
        count: runningTasksInDB.tasks ? runningTasksInDB.tasks.length : 0
      });

      if (runningTasksInDB.tasks && runningTasksInDB.tasks.length > 0) {
        for (const task of runningTasksInDB.tasks) {
          logger.warn('⚠️ 发现僵尸任务，重置状态:', {
            taskId: task.id,
            taskName: task.taskName,
            lastRunTime: task.lastRunTime
          });

          // 将僵尸任务状态重置为 paused
          await CollectTask.update(task.id, {
            status: 'paused',
            lastRunTime: new Date()
          });

          // 同时处理对应的日志记录
          const logs = await CollectLog.findAll({
            sourceId: task.sourceId,
            status: 'running',
            type: 'auto',
            limit: 1
          });

          if (logs.logs && logs.logs.length > 0) {
            const log = logs.logs[0];
            await CollectLog.update(log.id, {
              status: 'stopped',
              endTime: new Date(),
              duration: Math.floor((new Date() - new Date(log.startTime)) / 1000),
              errorMessage: '服务器重启，任务被中断'
            });

            logger.info('✅ 重置僵尸日志状态:', { logId: log.id });
          }
        }

        logger.info('🎯 僵尸任务状态重置完成');
      }

      this.initialized = true;
      logger.info('✅ CollectProcessor 初始化完成');

    } catch (error) {
      logger.error('❌ CollectProcessor 初始化失败:', error);
    }
  }

  // 开始采集任务
  async startCollectTask(taskId, config) {
    try {
      // 确保处理器已初始化
      await this.initialize();

      logger.info('开始采集任务:', { taskId, config });

      // 检查任务是否已在运行
      if (this.runningTasks.has(taskId)) {
        throw new Error('任务已在运行中');
      }

      // 获取任务信息
      const task = await CollectTask.findById(taskId);
      if (!task) {
        throw new Error('任务不存在');
      }

      // 获取采集源信息
      const source = await CollectSource.findById(task.sourceId);
      if (!source) {
        throw new Error('采集源不存在');
      }

      // 标记任务为运行中
      this.runningTasks.set(taskId, {
        status: 'running',
        startTime: new Date(),
        progress: 0,
        currentPage: 1,
        totalPages: config.maxPages || 10
      });

      // 更新任务状态
      await CollectTask.update(taskId, {
        status: 'running',
        lastRunTime: new Date()
      });

      // 创建采集日志记录
      await CollectLog.create({
        sourceId: task.sourceId,
        sourceName: source.name,
        type: 'auto', // 使用 'auto' 代替 'batch'，因为数据库枚举只支持 'manual','auto','test'
        status: 'running',
        startTime: new Date(),
        totalFound: 0,
        totalCollected: 0,
        totalSkipped: 0,
        totalFailed: 0,
        collectParams: config
      });

      // 开始异步采集
      this.processCollectTask(taskId, task, source, config).catch(error => {
        logger.error('采集任务执行失败:', error);
        this.handleTaskError(taskId, error);
      });

      return {
        success: true,
        message: '采集任务已启动',
        taskId: taskId
      };

    } catch (error) {
      logger.error('启动采集任务失败:', error);
      throw error;
    }
  }

  // 处理采集任务
  async processCollectTask(taskId, task, source, config) {
    const startTime = new Date();
    let totalCollected = 0;
    let totalSkipped = 0;
    let totalFailed = 0;

    try {
      logger.info('开始处理采集任务:', { taskId, sourceName: source.name });

      // 获取分类映射
      const categoryMappings = config.categoryMappings || {};
      const sourceCategories = Object.keys(categoryMappings);

      if (sourceCategories.length === 0) {
        throw new Error('未配置分类映射');
      }

      // 按分类采集
      for (const sourceCategoryId of sourceCategories) {
        const targetCategoryId = categoryMappings[sourceCategoryId];
        
        if (!targetCategoryId) {
          logger.warn('跳过未映射的分类:', sourceCategoryId);
          continue;
        }

        logger.info('开始采集分类:', { sourceCategoryId, targetCategoryId });

        // 采集该分类的视频
        const categoryResult = await this.collectCategoryVideos(
          taskId, 
          source, 
          sourceCategoryId, 
          targetCategoryId, 
          config
        );

        totalCollected += categoryResult.collected;
        totalSkipped += categoryResult.skipped;
        totalFailed += categoryResult.failed;

        // 检查是否需要停止
        if (!this.runningTasks.has(taskId)) {
          logger.info('任务被停止:', taskId);
          break;
        }
      }

      // 任务完成
      await this.completeTask(taskId, {
        status: 'success',
        totalCollected,
        totalSkipped,
        totalFailed,
        startTime,
        endTime: new Date()
      });

    } catch (error) {
      logger.error('采集任务处理失败:', error);
      await this.handleTaskError(taskId, error);
    }
  }

  // 采集指定分类的视频
  async collectCategoryVideos(taskId, source, sourceCategoryId, targetCategoryId, config) {
    let collected = 0;
    let skipped = 0;
    let failed = 0;
    const maxPages = config.maxPages || 10;

    try {
      for (let page = 1; page <= maxPages; page++) {
        // 更新进度（包含当前统计数据）
        await this.updateTaskProgress(taskId, page, maxPages, {
          totalCollected: collected,
          totalSkipped: skipped,
          totalFailed: failed
        });

        // 获取视频列表
        const videos = await this.fetchVideosFromSource(source, sourceCategoryId, page);

        if (!videos || videos.length === 0) {
          logger.info('分类无更多视频:', { sourceCategoryId, page });
          break;
        }

        // 处理每个视频
        for (const videoData of videos) {
          try {
            const result = await this.processVideo(videoData, targetCategoryId, config);

            if (result.action === 'created') {
              collected++;
            } else if (result.action === 'skipped') {
              skipped++;
            } else if (result.action === 'updated') {
              collected++;
            }

          } catch (error) {
            logger.error('处理视频失败:', { videoId: videoData.vod_id, error: error.message });
            failed++;
          }

          // 每处理10个视频更新一次进度
          if ((collected + skipped + failed) % 10 === 0) {
            await this.updateTaskProgress(taskId, page, maxPages, {
              totalCollected: collected,
              totalSkipped: skipped,
              totalFailed: failed
            });
          }

          // 添加延迟避免过于频繁的请求
          if (config.interval && config.interval > 0) {
            await this.sleep(config.interval);
          }
        }

        // 检查是否需要停止
        if (!this.runningTasks.has(taskId)) {
          break;
        }
      }

    } catch (error) {
      logger.error('采集分类视频失败:', error);
      throw error;
    }

    return { collected, skipped, failed };
  }

  // 从采集源获取视频列表
  async fetchVideosFromSource(source, categoryId, page) {
    try {
      const url = `${source.url}?ac=detail&t=${categoryId}&pg=${page}`;
      logger.info('请求采集源:', url);

      const response = await axios.get(url, {
        timeout: 30000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      if (response.data && response.data.list) {
        return response.data.list;
      }

      return [];
    } catch (error) {
      logger.error('获取采集源数据失败:', error);
      throw error;
    }
  }

  // 处理单个视频
  async processVideo(videoData, targetCategoryId, config) {
    try {
      // 检查视频是否已存在（使用vod_en作为source_id进行重复检查）
      const sourceId = videoData.vod_en || '';
      const existingVideo = await Video.findBySourceId(sourceId);

      if (existingVideo && !config.updateExisting) {
        return { action: 'skipped', video: existingVideo };
      }

      // 处理视频数据
      const processedData = this.processVideoData(videoData, targetCategoryId, config);

      if (existingVideo && config.updateExisting) {
        // 更新现有视频
        const result = await Video.update(existingVideo.id, processedData);
        return { action: 'updated', video: result };
      } else {
        // 创建新视频
        const result = await Video.create(processedData);
        return { action: 'created', video: result };
      }

    } catch (error) {
      logger.error('处理视频失败:', error);
      throw error;
    }
  }

  // 处理视频数据
  processVideoData(videoData, targetCategoryId, config) {
    // 处理播放地址
    let playUrl = '';
    if (videoData.vod_play_url) {
      const playUrlStr = videoData.vod_play_url.toString();
      const dollarIndex = playUrlStr.indexOf('$');
      if (dollarIndex !== -1) {
        playUrl = playUrlStr.substring(dollarIndex + 1);
      }
    }

    // 生成随机观看次数（100-10000）
    const views = Math.floor(Math.random() * 9901) + 100;

    // 按照正确的字段映射返回视频数据
    return {
      title: videoData.vod_name || '',                    // vod_name → title
      description: this.cleanHtmlContent(videoData.vod_blurb || ''), // vod_blurb → description（清理HTML）
      coverUrl: config.collectImages ? (videoData.vod_pic || '') : '', // vod_pic → cover_url
      videoUrl: playUrl,                                  // vod_play_url → video_url（处理$符号后）
      categoryId: targetCategoryId,
      sourceId: this.generateSourceId(videoData.vod_en || ''), // vod_en → source_id（防重复字段，限制长度）
      tags: this.extractTags(videoData),
      duration: videoData.vod_duration || '',             // vod_duration → duration
      rating: this.parseRating(videoData.vod_score),      // vod_score → rating
      views: views,                                       // views → 随机生成（100-10000）
      status: config.status || 'active',
      featured: config.featured || false
    };
  }

  // 提取标签
  extractTags(videoData) {
    // 从 vod_class 字段获取标签，该字段包含逗号分隔的标签
    if (!videoData.vod_class) {
      return [];
    }

    // 将逗号分隔的字符串转换为数组，并清理空白字符
    return videoData.vod_class
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
  }

  // 解析评分
  parseRating(score) {
    if (!score) return 0;
    const rating = parseFloat(score);
    // 确保评分在0-9.99之间（数据库DECIMAL(3,2)限制）
    return isNaN(rating) ? 0 : Math.max(0, Math.min(9.99, rating));
  }

  // 更新任务进度
  async updateTaskProgress(taskId, currentPage, totalPages, stats = {}) {
    const taskInfo = this.runningTasks.get(taskId);
    if (taskInfo) {
      taskInfo.currentPage = currentPage;
      taskInfo.progress = Math.floor((currentPage / totalPages) * 100);
      taskInfo.totalCollected = stats.totalCollected || taskInfo.totalCollected || 0;
      taskInfo.totalSkipped = stats.totalSkipped || taskInfo.totalSkipped || 0;
      taskInfo.totalFailed = stats.totalFailed || taskInfo.totalFailed || 0;
      this.runningTasks.set(taskId, taskInfo);

      // 实时更新数据库中的任务状态
      try {
        await CollectTask.update(taskId, {
          status: 'running',
          progress: taskInfo.progress,
          currentPage: currentPage,
          totalPages: totalPages,
          lastRunTime: new Date()
        });

        // 同时更新对应的日志记录
        const task = await CollectTask.findById(taskId);
        if (task) {
          const logs = await CollectLog.findAll({
            sourceId: task.sourceId,
            status: 'running',
            limit: 1
          });

          if (logs.logs && logs.logs.length > 0) {
            const log = logs.logs[0];
            await CollectLog.update(log.id, {
              status: 'running',
              totalFound: (stats.totalCollected || 0) + (stats.totalSkipped || 0) + (stats.totalFailed || 0),
              totalCollected: stats.totalCollected || 0,
              totalSkipped: stats.totalSkipped || 0,
              totalFailed: stats.totalFailed || 0,
              progress: taskInfo.progress
            });
          }
        }
      } catch (error) {
        logger.error('更新任务进度失败:', error);
      }
    }
  }

  // 完成任务
  async completeTask(taskId, result) {
    try {
      logger.info('🎯 开始完成任务:', { taskId, result });

      const task = await CollectTask.findById(taskId);
      if (!task) {
        logger.error('❌ 任务不存在:', { taskId });
        return;
      }

      logger.info('✅ 找到任务信息:', {
        taskId,
        taskName: task.taskName,
        sourceId: task.sourceId,
        currentStatus: task.status
      });

      // 更新任务状态（采集完成后设置为active状态）
      const taskUpdateData = {
        status: 'active',
        successCount: (task.successCount || 0) + result.totalCollected,
        failCount: (task.failCount || 0) + result.totalFailed,
        runCount: (task.runCount || 0) + 1,
        lastRunTime: new Date()
      };

      logger.info('🔄 更新任务状态:', { taskId, updateData: taskUpdateData });
      await CollectTask.update(taskId, taskUpdateData);
      logger.info('✅ 任务状态更新成功:', { taskId, newStatus: 'active' });

      // 查找并更新对应的日志记录（查找最近的运行中日志）
      logger.info('🔍 查找运行中的日志记录:', { sourceId: task.sourceId });
      const logs = await CollectLog.findAll({
        sourceId: task.sourceId,
        status: 'running',
        type: 'auto', // 只查找批量采集的日志
        limit: 1
      });

      logger.info('📋 日志查找结果:', {
        sourceId: task.sourceId,
        foundLogs: logs.logs ? logs.logs.length : 0
      });

      if (logs.logs && logs.logs.length > 0) {
        const log = logs.logs[0];
        logger.info('📝 找到对应日志，开始更新:', {
          logId: log.id,
          taskId,
          logStatus: log.status,
          logType: log.type
        });

        const logUpdateData = {
          status: 'success',
          totalFound: result.totalCollected + result.totalSkipped + result.totalFailed,
          totalCollected: result.totalCollected,
          totalSkipped: result.totalSkipped,
          totalFailed: result.totalFailed,
          endTime: new Date(),
          duration: Math.floor((new Date() - result.startTime) / 1000)
        };

        logger.info('🔄 更新日志数据:', { logId: log.id, updateData: logUpdateData });
        await CollectLog.update(log.id, logUpdateData);
        logger.info('✅ 采集日志状态更新成功:', { logId: log.id, status: 'success' });
      } else {
        logger.warn('⚠️ 未找到对应的运行中日志记录:', {
          taskId,
          sourceId: task.sourceId,
          searchCriteria: { status: 'running', type: 'auto' }
        });

        // 尝试查找所有相关日志进行调试
        const allLogs = await CollectLog.findAll({
          sourceId: task.sourceId,
          limit: 5
        });
        logger.warn('🔍 该采集源的所有日志:', {
          sourceId: task.sourceId,
          totalLogs: allLogs.logs ? allLogs.logs.length : 0,
          logs: allLogs.logs ? allLogs.logs.map(l => ({
            id: l.id,
            status: l.status,
            type: l.type,
            startTime: l.startTime
          })) : []
        });
      }

      // 移除运行中的任务
      this.runningTasks.delete(taskId);
      logger.info('🗑️ 任务从运行列表中移除:', { taskId });

      logger.info('🎉 采集任务完成:', { taskId, result });
    } catch (error) {
      logger.error('❌ 完成任务失败:', { taskId, error: error.message, stack: error.stack });
    }
  }

  // 处理任务错误
  async handleTaskError(taskId, error) {
    try {
      logger.error('💥 开始处理任务错误:', { taskId, error: error.message });

      const task = await CollectTask.findById(taskId);
      if (!task) {
        logger.error('❌ 任务不存在:', { taskId });
        return;
      }

      logger.info('✅ 找到任务信息:', {
        taskId,
        taskName: task.taskName,
        sourceId: task.sourceId,
        currentStatus: task.status
      });

      // 更新任务状态（采集失败后设置为inactive状态）
      const taskUpdateData = {
        status: 'inactive',
        failCount: (task.failCount || 0) + 1,
        runCount: (task.runCount || 0) + 1,
        lastRunTime: new Date()
      };

      logger.info('🔄 更新任务状态为失败:', { taskId, updateData: taskUpdateData });
      await CollectTask.update(taskId, taskUpdateData);
      logger.info('✅ 任务状态更新成功:', { taskId, newStatus: 'inactive' });

      // 查找并更新对应的日志记录（查找最近的运行中日志）
      logger.info('🔍 查找运行中的日志记录:', { sourceId: task.sourceId });
      const logs = await CollectLog.findAll({
        sourceId: task.sourceId,
        status: 'running',
        type: 'auto', // 只查找批量采集的日志
        limit: 1
      });

      logger.info('📋 日志查找结果:', {
        sourceId: task.sourceId,
        foundLogs: logs.logs ? logs.logs.length : 0
      });

      if (logs.logs && logs.logs.length > 0) {
        const log = logs.logs[0];
        logger.info('📝 找到对应日志，开始更新为失败:', {
          logId: log.id,
          taskId,
          logStatus: log.status,
          logType: log.type,
          error: error.message
        });

        const logUpdateData = {
          status: 'failed',
          errorMessage: error.message,
          endTime: new Date(),
          duration: Math.floor((new Date() - new Date(log.startTime)) / 1000)
        };

        logger.info('🔄 更新日志数据:', { logId: log.id, updateData: logUpdateData });
        await CollectLog.update(log.id, logUpdateData);
        logger.info('✅ 采集日志状态更新成功:', { logId: log.id, status: 'failed' });
      } else {
        logger.warn('⚠️ 未找到对应的运行中日志记录:', {
          taskId,
          sourceId: task.sourceId,
          searchCriteria: { status: 'running', type: 'auto' }
        });
      }

      // 移除运行中的任务
      this.runningTasks.delete(taskId);
      logger.info('🗑️ 任务从运行列表中移除:', { taskId });

      logger.error('💥 采集任务失败处理完成:', { taskId, error: error.message });
    } catch (updateError) {
      logger.error('❌ 处理任务错误失败:', { taskId, originalError: error.message, handlingError: updateError.message, stack: updateError.stack });
    }
  }

  // 获取任务状态
  getTaskStatus(taskId) {
    return this.runningTasks.get(taskId) || null;
  }

  // 停止任务
  async stopTask(taskId) {
    try {
      // 确保处理器已初始化
      await this.initialize();

      logger.info('🛑 开始停止任务:', { taskId });

      // 获取任务信息
      const task = await CollectTask.findById(taskId);
      if (!task) {
        logger.error('❌ 任务不存在:', { taskId });
        return { success: false, message: '任务不存在' };
      }

      logger.info('✅ 找到任务信息:', {
        taskId,
        taskName: task.taskName,
        dbStatus: task.status,
        inMemory: this.runningTasks.has(taskId)
      });

      // 检查任务状态
      const isInMemory = this.runningTasks.has(taskId);
      const isRunningInDB = task.status === 'running';

      if (!isInMemory && !isRunningInDB) {
        logger.warn('⚠️ 任务未在运行中:', { taskId, dbStatus: task.status });
        return { success: false, message: '任务未在运行中' };
      }

      // 从内存中移除任务（如果存在）
      if (isInMemory) {
        this.runningTasks.delete(taskId);
        logger.info('🗑️ 任务从内存中移除:', { taskId });
      }

      // 更新数据库中的任务状态
      await CollectTask.update(taskId, {
        status: 'paused',
        lastRunTime: new Date()
      });
      logger.info('✅ 任务状态更新为已暂停:', { taskId });

      // 更新对应的日志记录
      const logs = await CollectLog.findAll({
        sourceId: task.sourceId,
        status: 'running',
        type: 'auto',
        limit: 1
      });

      logger.info('🔍 查找运行中的日志:', {
        sourceId: task.sourceId,
        foundLogs: logs.logs ? logs.logs.length : 0
      });

      if (logs.logs && logs.logs.length > 0) {
        const log = logs.logs[0];
        const logUpdateData = {
          status: 'stopped',
          endTime: new Date(),
          duration: Math.floor((new Date() - new Date(log.startTime)) / 1000)
        };

        logger.info('🔄 更新日志状态为已停止:', { logId: log.id, updateData: logUpdateData });
        await CollectLog.update(log.id, logUpdateData);
        logger.info('✅ 日志状态更新成功:', { logId: log.id });
      } else {
        logger.warn('⚠️ 未找到对应的运行中日志记录:', { taskId, sourceId: task.sourceId });
      }

      logger.info('🎯 任务停止完成:', { taskId });
      return { success: true, message: '任务已停止' };

    } catch (error) {
      logger.error('❌ 停止任务失败:', { taskId, error: error.message, stack: error.stack });
      return { success: false, message: '停止任务失败: ' + error.message };
    }
  }

  // 生成符合数据库长度限制的source_id
  generateSourceId(vodEn) {
    if (!vodEn) return '';

    // 如果长度在255字符以内，直接返回
    if (vodEn.length <= 255) {
      return vodEn;
    }

    // 如果超过255字符，截取前245字符并添加哈希后缀
    const crypto = require('crypto');
    const truncated = vodEn.substring(0, 245);
    const hash = crypto.createHash('md5').update(vodEn).digest('hex').substring(0, 8);

    return `${truncated}_${hash}`;
  }

  // 清理HTML内容，只保留纯文本
  cleanHtmlContent(htmlContent) {
    if (!htmlContent) return '';

    return htmlContent
      // 移除HTML标签
      .replace(/<[^>]*>/g, '')
      // 替换HTML实体
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&hellip;/g, '...')
      // 清理多余的空白字符
      .replace(/\s+/g, ' ')
      .trim();
  }

  // 延迟函数
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 创建单例实例
const collectProcessor = new CollectProcessor();

module.exports = collectProcessor;

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>苹果CMS采集接口测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        .url-input { width: 100%; padding: 8px; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>苹果CMS采集接口测试工具</h1>
        
        <div class="test-section info">
            <h3>测试说明</h3>
            <p>此工具用于测试采集接口的JSON格式是否符合苹果CMS要求。</p>
            <p>如果苹果CMS报告"Unexpected token"错误，通常是JSON格式问题。</p>
        </div>

        <div class="test-section">
            <h3>接口地址配置</h3>
            <input type="text" id="apiUrl" class="url-input" 
                   value="https://91jspg.com/api/collect/vod?ac=list&pg=1" 
                   placeholder="输入API接口地址">
            <button onclick="testApi()">测试接口</button>
            <button onclick="testDetailApi()">测试详情接口</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        function showResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-section ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${content}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testApi() {
            clearResults();
            const url = document.getElementById('apiUrl').value;
            
            try {
                showResult('开始测试', `请求地址: ${url}`, 'info');
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                showResult('HTTP响应状态', `状态码: ${response.status}\n状态文本: ${response.statusText}`, 
                          response.ok ? 'success' : 'error');

                const responseHeaders = {};
                response.headers.forEach((value, key) => {
                    responseHeaders[key] = value;
                });
                showResult('响应头信息', JSON.stringify(responseHeaders, null, 2), 'info');

                const responseText = await response.text();
                showResult('原始响应内容', responseText.substring(0, 1000) + (responseText.length > 1000 ? '...' : ''), 'info');

                // 尝试解析JSON
                try {
                    const jsonData = JSON.parse(responseText);
                    showResult('JSON解析结果', '✅ JSON格式正确，可以正常解析', 'success');
                    showResult('JSON结构验证', `
代码: ${jsonData.code} (${typeof jsonData.code})
消息: ${jsonData.msg} (${typeof jsonData.msg})
页码: ${jsonData.page} (${typeof jsonData.page})
总页数: ${jsonData.pagecount} (${typeof jsonData.pagecount})
每页数量: ${jsonData.limit} (${typeof jsonData.limit})
总数: ${jsonData.total} (${typeof jsonData.total})
视频列表长度: ${jsonData.list ? jsonData.list.length : 0}
分类列表长度: ${jsonData.class ? jsonData.class.length : 0}
                    `, 'success');

                    if (jsonData.list && jsonData.list.length > 0) {
                        const firstVideo = jsonData.list[0];
                        showResult('第一个视频数据', JSON.stringify(firstVideo, null, 2), 'info');
                    }

                } catch (parseError) {
                    showResult('JSON解析错误', `❌ JSON格式错误: ${parseError.message}`, 'error');
                    
                    // 尝试找出问题位置
                    const lines = responseText.split('\n');
                    showResult('可能的问题行', lines.slice(0, 10).map((line, index) => 
                        `${index + 1}: ${line}`).join('\n'), 'error');
                }

            } catch (error) {
                showResult('请求错误', `❌ 请求失败: ${error.message}`, 'error');
            }
        }

        async function testDetailApi() {
            const baseUrl = document.getElementById('apiUrl').value.split('?')[0];
            const detailUrl = `${baseUrl}?ac=detail&ids=1,2,3`;
            document.getElementById('apiUrl').value = detailUrl;
            await testApi();
        }

        // 页面加载时自动测试
        window.onload = function() {
            // 可以在这里添加自动测试逻辑
        };
    </script>
</body>
</html>

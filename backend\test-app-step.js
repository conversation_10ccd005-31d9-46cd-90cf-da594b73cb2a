// 逐步测试应用启动
require('dotenv').config();

console.log('=== 逐步测试应用启动 ===');

try {
  console.log('1. 加载基础模块...');
  const express = require('express');
  const cors = require('cors');
  const helmet = require('helmet');
  const morgan = require('morgan');
  const compression = require('compression');
  const rateLimit = require('express-rate-limit');
  const cookieParser = require('cookie-parser');
  console.log('✅ 基础模块加载成功');

  console.log('2. 加载工具模块...');
  const logger = require('./src/utils/logger');
  console.log('✅ 工具模块加载成功');

  console.log('3. 创建 Express 应用...');
  const app = express();
  console.log('✅ Express 应用创建成功');

  console.log('4. 配置基础中间件...');
  app.use(helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" }
  }));
  app.use(compression());
  console.log('✅ 基础中间件配置成功');

  console.log('5. 配置 CORS...');
  const corsOptions = {
    origin: true,
    credentials: true,
    optionsSuccessStatus: 200,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  };
  app.use(cors(corsOptions));
  console.log('✅ CORS 配置成功');

  console.log('6. 配置请求解析...');
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));
  app.use(cookieParser());
  console.log('✅ 请求解析配置成功');

  console.log('7. 测试启动服务器...');
  const server = app.listen(3002, () => {
    console.log('✅ 服务器启动成功，端口: 3002');
    
    setTimeout(() => {
      console.log('🛑 关闭测试服务器');
      server.close();
      process.exit(0);
    }, 3000);
  });

  server.on('error', (error) => {
    console.error('❌ 服务器启动失败:', error.message);
    process.exit(1);
  });

} catch (error) {
  console.error('❌ 应用启动失败:', error.message);
  console.error('错误堆栈:', error.stack);
  process.exit(1);
}

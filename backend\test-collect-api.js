const express = require('express');
const collectRouter = require('./src/routes/api/collect');

// 创建测试应用
const app = express();
app.use('/api/collect', collectRouter);

// 启动测试服务器
const port = 3002;
app.listen(port, () => {
  console.log(`测试服务器运行在 http://localhost:${port}`);
  console.log('测试接口:');
  console.log(`- 列表: http://localhost:${port}/api/collect/vod?ac=list`);
  console.log(`- 详情: http://localhost:${port}/api/collect/vod?ac=detail&ids=1`);
  console.log(`- 文档: http://localhost:${port}/api/collect/`);
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({
    code: 0,
    msg: '服务器内部错误',
    error: err.message
  });
});

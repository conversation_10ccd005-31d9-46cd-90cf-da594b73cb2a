const collectProcessor = require('./src/services/CollectProcessor');
const CollectTask = require('./src/models/CollectTask');
const CollectSource = require('./src/models/CollectSource');

async function testCollectProcessor() {
  try {
    console.log('开始测试采集处理器...');

    // 测试配置
    const testConfig = {
      categoryMappings: {
        '1': 1, // 源分类1映射到本地分类1
        '2': 2  // 源分类2映射到本地分类2
      },
      timeRange: { timeType: 'all' },
      collectImages: true,
      updateExisting: false,
      featured: false,
      status: 'active',
      maxPages: 2,
      interval: 1000,
      timeout: 30000
    };

    // 创建测试任务
    const testTask = await CollectTask.create({
      sourceId: 1, // 假设存在ID为1的采集源
      taskName: '测试采集任务',
      taskType: 'once',
      status: 'pending',
      taskConfig: testConfig,
      maxPages: testConfig.maxPages,
      collectImages: testConfig.collectImages ? 1 : 0,
      lastRunTime: new Date(),
      runCount: 1,
      createdBy: 1
    });

    console.log('测试任务创建成功:', testTask.id);

    // 启动采集
    const result = await collectProcessor.startCollectTask(testTask.id, testConfig);
    console.log('采集启动结果:', result);

    // 监控任务状态
    const checkStatus = () => {
      const status = collectProcessor.getTaskStatus(testTask.id);
      if (status) {
        console.log('任务状态:', status);
        setTimeout(checkStatus, 2000);
      } else {
        console.log('任务已完成或停止');
      }
    };

    setTimeout(checkStatus, 1000);

  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  testCollectProcessor();
}

module.exports = testCollectProcessor;

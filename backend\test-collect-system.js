const CollectTask = require('./src/models/CollectTask');
const CollectLog = require('./src/models/CollectLog');
const CollectSource = require('./src/models/CollectSource');

async function testCollectSystem() {
  try {
    console.log('=== 测试采集系统 ===');

    // 1. 测试获取采集任务列表
    console.log('\n1. 测试获取采集任务列表...');
    const tasks = await CollectTask.findAll({ limit: 5 });
    console.log('任务数量:', tasks.tasks.length);
    if (tasks.tasks.length > 0) {
      console.log('最新任务:', {
        id: tasks.tasks[0].id,
        taskName: tasks.tasks[0].taskName,
        status: tasks.tasks[0].status,
        sourceId: tasks.tasks[0].sourceId
      });
    }

    // 2. 测试获取采集日志列表
    console.log('\n2. 测试获取采集日志列表...');
    const logs = await CollectLog.findAll({ limit: 5 });
    console.log('日志数量:', logs.logs.length);
    if (logs.logs.length > 0) {
      console.log('最新日志:', {
        id: logs.logs[0].id,
        sourceName: logs.logs[0].sourceName,
        status: logs.logs[0].status,
        totalCollected: logs.logs[0].totalCollected,
        startTime: logs.logs[0].startTime
      });
    }

    // 3. 测试获取采集源列表
    console.log('\n3. 测试获取采集源列表...');
    const sources = await CollectSource.findAll({ limit: 5 });
    console.log('采集源数量:', sources.sources.length);
    if (sources.sources.length > 0) {
      console.log('第一个采集源:', {
        id: sources.sources[0].id,
        name: sources.sources[0].name,
        url: sources.sources[0].url,
        status: sources.sources[0].status
      });
    }

    // 4. 检查数据库连接
    console.log('\n4. 检查数据库连接...');
    const database = require('./src/config/database');
    const testQuery = await database.query('SELECT COUNT(*) as count FROM videos');
    console.log('视频总数:', testQuery.rows[0].count);

    console.log('\n=== 测试完成 ===');
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  testCollectSystem().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('测试执行失败:', error);
    process.exit(1);
  });
}

module.exports = testCollectSystem;

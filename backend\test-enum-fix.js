// 加载环境变量
require('dotenv').config();

const CollectTask = require('./src/models/CollectTask');
const CollectLog = require('./src/models/CollectLog');
const db = require('./src/config/database');

async function testEnumFix() {
  console.log('=== 测试枚举值修复 ===\n');

  try {
    // 初始化数据库连接
    console.log('初始化数据库连接...');
    await db.connect();
    console.log('✅ 数据库连接成功\n');
    // 1. 测试 collect_tasks 表的状态枚举
    console.log('1. 测试 collect_tasks 状态枚举...');
    
    const validTaskStatuses = ['active', 'inactive', 'running', 'paused'];
    console.log('有效的任务状态:', validTaskStatuses.join(', '));
    
    // 创建测试任务
    const testTask = await CollectTask.create({
      sourceId: 1,
      taskName: '测试枚举修复',
      taskType: 'once',
      status: 'active'
    });
    
    console.log(`✅ 创建测试任务成功: ${testTask.id}`);
    
    // 测试状态更新
    for (const status of validTaskStatuses) {
      try {
        await CollectTask.update(testTask.id, { status });
        console.log(`✅ 更新任务状态为 '${status}' 成功`);
      } catch (error) {
        console.log(`❌ 更新任务状态为 '${status}' 失败:`, error.message);
      }
    }
    
    // 测试无效状态
    try {
      await CollectTask.update(testTask.id, { status: 'failed' });
      console.log(`❌ 意外成功：'failed' 状态不应该被允许`);
    } catch (error) {
      console.log(`✅ 正确拒绝无效状态 'failed':`, error.message);
    }

    // 2. 测试 collect_logs 表的类型和状态枚举
    console.log('\n2. 测试 collect_logs 类型和状态枚举...');
    
    const validLogTypes = ['manual', 'auto', 'test'];
    const validLogStatuses = ['running', 'success', 'failed', 'stopped'];
    
    console.log('有效的日志类型:', validLogTypes.join(', '));
    console.log('有效的日志状态:', validLogStatuses.join(', '));
    
    // 测试日志类型
    for (const type of validLogTypes) {
      try {
        const testLog = await CollectLog.create({
          sourceId: 1,
          sourceName: '测试源',
          type: type,
          status: 'running',
          startTime: new Date()
        });
        console.log(`✅ 创建 '${type}' 类型日志成功: ${testLog.id}`);
        
        // 测试状态更新
        for (const status of validLogStatuses) {
          try {
            await CollectLog.update(testLog.id, { status });
            console.log(`  ✅ 更新日志状态为 '${status}' 成功`);
          } catch (error) {
            console.log(`  ❌ 更新日志状态为 '${status}' 失败:`, error.message);
          }
        }
        
      } catch (error) {
        console.log(`❌ 创建 '${type}' 类型日志失败:`, error.message);
      }
    }
    
    // 测试无效类型
    try {
      await CollectLog.create({
        sourceId: 1,
        sourceName: '测试源',
        type: 'batch', // 这个应该失败
        status: 'running',
        startTime: new Date()
      });
      console.log(`❌ 意外成功：'batch' 类型不应该被允许`);
    } catch (error) {
      console.log(`✅ 正确拒绝无效类型 'batch':`, error.message);
    }

    // 3. 清理测试数据
    console.log('\n3. 清理测试数据...');
    
    // 删除测试任务
    try {
      await CollectTask.delete(testTask.id);
      console.log(`✅ 删除测试任务 ${testTask.id} 成功`);
    } catch (error) {
      console.log(`⚠️ 删除测试任务失败:`, error.message);
    }
    
    // 删除测试日志（查找并删除所有测试日志）
    try {
      const testLogs = await CollectLog.findAll({ 
        sourceName: '测试源',
        limit: 100 
      });
      
      for (const log of testLogs.logs) {
        try {
          await CollectLog.delete(log.id);
          console.log(`✅ 删除测试日志 ${log.id} 成功`);
        } catch (error) {
          console.log(`⚠️ 删除测试日志 ${log.id} 失败:`, error.message);
        }
      }
    } catch (error) {
      console.log(`⚠️ 查找测试日志失败:`, error.message);
    }

    console.log('\n=== 修复验证总结 ===');
    console.log('✅ collect_tasks 表状态枚举: active, inactive, running, paused');
    console.log('✅ collect_logs 表类型枚举: manual, auto, test');
    console.log('✅ collect_logs 表状态枚举: running, success, failed, stopped');
    console.log('✅ 代码已修复为使用正确的枚举值');
    console.log('\n🎯 现在可以重新测试批量采集功能！');

  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  testEnumFix();
}

module.exports = testEnumFix;

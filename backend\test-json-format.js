// 测试JSON格式化函数
function sanitizeString(str) {
  if (!str) return '';
  
  return String(str)
    .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // 移除控制字符
    .replace(/[\u2028\u2029]/g, '') // 移除行分隔符和段分隔符
    .replace(/\\/g, '\\\\') // 转义反斜杠
    .replace(/"/g, '\\"') // 转义双引号
    .trim();
}

function sanitizeTags(tags) {
  if (!tags) return '';
  
  try {
    if (Array.isArray(tags)) {
      return tags.map(tag => sanitizeString(tag)).join(',');
    } else if (typeof tags === 'string') {
      const parsed = JSON.parse(tags);
      if (Array.isArray(parsed)) {
        return parsed.map(tag => sanitizeString(tag)).join(',');
      }
      return sanitizeString(tags);
    }
    return '';
  } catch (e) {
    return sanitizeString(String(tags || ''));
  }
}

function formatDateTime(date) {
  if (!date) return '';
  const d = new Date(date);
  return d.toISOString().slice(0, 19).replace('T', ' ');
}

function getVideoYear(createdAt) {
  if (!createdAt) return new Date().getFullYear().toString();
  
  try {
    const year = new Date(createdAt).getFullYear();
    return isNaN(year) ? new Date().getFullYear().toString() : year.toString();
  } catch (e) {
    return new Date().getFullYear().toString();
  }
}

// 测试数据
const testVideo = {
  id: 1,
  title: 'Test Video "Title" with special chars',
  categoryId: 1,
  categoryName: 'Test Category',
  createdAt: new Date(),
  status: 'active',
  coverUrl: 'https://example.com/cover.jpg',
  views: 100,
  rating: 8.5,
  description: 'Test description with "quotes" and \\ backslashes',
  videoUrl: 'https://example.com/video.mp4',
  duration: '120',
  tags: '["tag1", "tag2", "tag3"]'
};

// 创建苹果CMS格式的响应
const response = {
  code: 1,
  msg: '数据列表',
  page: 1,
  pagecount: 1,
  limit: '1',
  total: 1,
  list: [{
    vod_id: parseInt(testVideo.id) || 0,
    vod_name: sanitizeString(testVideo.title) || '',
    type_id: parseInt(testVideo.categoryId) || 1,
    type_name: sanitizeString(testVideo.categoryName) || '其他',
    vod_en: 'test-video',
    vod_time: formatDateTime(testVideo.createdAt) || '',
    vod_remarks: '高清',
    vod_play_from: 'default',
    vod_pic: sanitizeString(testVideo.coverUrl) || '',
    vod_area: '大陆',
    vod_lang: '国语',
    vod_year: getVideoYear(testVideo.createdAt),
    vod_serial: '0',
    vod_actor: '',
    vod_director: '',
    vod_content: sanitizeString(testVideo.description) || '',
    vod_play_url: testVideo.videoUrl ? `正片$${sanitizeString(testVideo.videoUrl)}` : '',
    vod_status: testVideo.status === 'active' ? 1 : 0,
    vod_hits: parseInt(testVideo.views) || 0,
    vod_score: parseFloat(testVideo.rating) || 0,
    vod_duration: sanitizeString(testVideo.duration) || '',
    vod_tag: sanitizeTags(testVideo.tags),
    vod_class: sanitizeString(testVideo.categoryName) || '其他'
  }],
  class: [{
    type_id: 1,
    type_pid: 0,
    type_name: '测试分类'
  }]
};

// 创建严格的清理版本（模拟新的处理逻辑）
const cleanResponse = {
  code: Number(response.code),
  msg: String(response.msg),
  page: Number(response.page),
  pagecount: Number(response.pagecount),
  limit: String(response.limit),
  total: Number(response.total),
  list: response.list.map(item => ({
    vod_id: Number(item.vod_id),
    vod_name: String(item.vod_name || ''),
    type_id: Number(item.type_id),
    type_name: String(item.type_name || ''),
    vod_en: String(item.vod_en || ''),
    vod_time: String(item.vod_time || ''),
    vod_remarks: String(item.vod_remarks || ''),
    vod_play_from: String(item.vod_play_from || 'default'),
    vod_pic: String(item.vod_pic || ''),
    vod_area: String(item.vod_area || ''),
    vod_lang: String(item.vod_lang || ''),
    vod_year: String(item.vod_year || ''),
    vod_serial: String(item.vod_serial || ''),
    vod_actor: String(item.vod_actor || ''),
    vod_director: String(item.vod_director || ''),
    vod_content: String(item.vod_content || ''),
    vod_play_url: String(item.vod_play_url || ''),
    vod_status: Number(item.vod_status),
    vod_hits: Number(item.vod_hits),
    vod_score: Number(item.vod_score),
    vod_duration: String(item.vod_duration || ''),
    vod_tag: String(item.vod_tag || ''),
    vod_class: String(item.vod_class || '')
  })),
  class: response.class.map(cat => ({
    type_id: Number(cat.type_id),
    type_pid: Number(cat.type_pid),
    type_name: String(cat.type_name || '')
  }))
};

console.log('=== 测试原始JSON格式 ===');
try {
  const jsonString = JSON.stringify(response, null, 2);
  console.log('✅ 原始JSON序列化成功');
  console.log('JSON长度:', jsonString.length);

  // 验证JSON是否可以解析
  const parsed = JSON.parse(jsonString);
  console.log('✅ 原始JSON解析成功');
  console.log('视频数量:', parsed.list.length);
  console.log('分类数量:', parsed.class.length);

} catch (error) {
  console.error('❌ 原始JSON处理失败:', error.message);
}

console.log('\n=== 测试清理后的JSON格式 ===');
try {
  const cleanJsonString = JSON.stringify(cleanResponse);
  console.log('✅ 清理后JSON序列化成功');
  console.log('JSON长度:', cleanJsonString.length);

  // 验证JSON是否可以解析
  const cleanParsed = JSON.parse(cleanJsonString);
  console.log('✅ 清理后JSON解析成功');
  console.log('视频数量:', cleanParsed.list.length);
  console.log('分类数量:', cleanParsed.class.length);

  // 检查数据类型
  console.log('\n=== 数据类型检查 ===');
  const firstVideo = cleanParsed.list[0];
  console.log('vod_id类型:', typeof firstVideo.vod_id, '值:', firstVideo.vod_id);
  console.log('vod_name类型:', typeof firstVideo.vod_name, '值:', firstVideo.vod_name);
  console.log('type_id类型:', typeof firstVideo.type_id, '值:', firstVideo.type_id);
  console.log('vod_status类型:', typeof firstVideo.vod_status, '值:', firstVideo.vod_status);
  console.log('vod_hits类型:', typeof firstVideo.vod_hits, '值:', firstVideo.vod_hits);
  console.log('vod_score类型:', typeof firstVideo.vod_score, '值:', firstVideo.vod_score);

  // 输出紧凑的JSON（模拟实际API响应）
  console.log('\n=== 紧凑JSON格式（实际API输出） ===');
  console.log(cleanJsonString.substring(0, 300) + '...');

} catch (error) {
  console.error('❌ 清理后JSON处理失败:', error.message);
  console.error('错误详情:', error);
}

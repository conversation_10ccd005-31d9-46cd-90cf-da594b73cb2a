// 测试简单查询
require('dotenv').config();

console.log('=== 测试简单查询 ===');

async function testSimpleQueries() {
  try {
    const database = require('./src/config/database');
    await database.connect();
    
    console.log('🔄 测试1: 简单的 SELECT 查询...');
    const result1 = await database.query('SELECT * FROM categories WHERE status = ? LIMIT ? OFFSET ?', ['active', 12, 0]);
    console.log('✅ 简单查询成功，返回行数:', result1.rows.length);
    
    console.log('🔄 测试2: 带 COUNT 的查询...');
    const result2 = await database.query('SELECT c.*, COUNT(v.id) as video_count FROM categories c LEFT JOIN videos v ON c.id = v.category_id WHERE c.status = ? GROUP BY c.id LIMIT ? OFFSET ?', ['active', 12, 0]);
    console.log('✅ COUNT 查询成功，返回行数:', result2.rows.length);
    
    console.log('🔄 测试3: 完整的查询（不带 v.status 条件）...');
    const result3 = await database.query(`
      SELECT c.*, COUNT(v.id) as video_count
      FROM categories c
      LEFT JOIN videos v ON c.id = v.category_id
      WHERE c.status = ?
      GROUP BY c.id
      ORDER BY c.sort_order ASC, c.created_at DESC
      LIMIT ? OFFSET ?
    `, ['active', 12, 0]);
    console.log('✅ 完整查询（无 v.status）成功，返回行数:', result3.rows.length);
    
    console.log('🔄 测试4: 完整的查询（带 v.status 条件）...');
    const result4 = await database.query(`
      SELECT c.*, COUNT(v.id) as video_count
      FROM categories c
      LEFT JOIN videos v ON c.id = v.category_id AND v.status = 'active'
      WHERE c.status = ?
      GROUP BY c.id
      ORDER BY c.sort_order ASC, c.created_at DESC
      LIMIT ? OFFSET ?
    `, ['active', 12, 0]);
    console.log('✅ 完整查询（带 v.status）成功，返回行数:', result4.rows.length);
    
  } catch (error) {
    console.error('❌ 查询失败:', {
      message: error.message,
      code: error.code,
      errno: error.errno,
      sqlState: error.sqlState
    });
  }
}

testSimpleQueries().then(() => {
  console.log('🎉 测试完成');
  process.exit(0);
}).catch(error => {
  console.error('❌ 测试过程出错:', error);
  process.exit(1);
});

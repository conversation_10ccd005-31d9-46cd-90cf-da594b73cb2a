const request = require('supertest');
const app = require('./src/app');

describe('单个视频采集功能测试', () => {
  test('应该成功采集单个视频并记录日志', async () => {
    const testVideoData = {
      sourceId: 1,
      videoId: 'test123',
      targetCategoryId: 1,
      updateExisting: false,
      collectImages: true,
      featured: false,
      videoData: {
        vod_id: 'test123',
        vod_name: '测试视频',
        vod_pic: 'https://example.com/pic.jpg',
        vod_remarks: '测试备注',
        vod_time: '2024-01-01',
        vod_year: '2024',
        vod_area: '中国',
        vod_lang: '中文',
        vod_actor: '测试演员',
        vod_director: '测试导演',
        vod_content: '这是一个测试视频的描述内容',
        vod_play_url: 'HD$https://example.com/video.mp4',
        vod_score: '8.5',
        type_name: '电影'
      }
    };

    const response = await request(app)
      .post('/api/admin/collect/single-video')
      .send(testVideoData)
      .expect(201);

    expect(response.body.success).toBe(true);
    expect(response.body.message).toBe('视频采集成功');
    expect(response.body.data).toHaveProperty('id');
    expect(response.body.data).toHaveProperty('title');
    expect(response.body.data.action).toBe('created');
  });

  test('应该在缺少必需参数时返回错误', async () => {
    const invalidData = {
      sourceId: 1,
      videoId: 'test123'
      // 缺少 targetCategoryId 和 videoData
    };

    const response = await request(app)
      .post('/api/admin/collect/single-video')
      .send(invalidData)
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.message).toContain('缺少必需参数');
  });

  test('应该在目标分类不存在时返回错误', async () => {
    const testVideoData = {
      sourceId: 1,
      videoId: 'test123',
      targetCategoryId: 99999, // 不存在的分类ID
      videoData: {
        vod_id: 'test123',
        vod_name: '测试视频'
      }
    };

    const response = await request(app)
      .post('/api/admin/collect/single-video')
      .send(testVideoData)
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.message).toContain('指定的目标分类不存在');
  });
});

// 加载环境变量
require('dotenv').config();

const CollectProcessor = require('./src/services/CollectProcessor');
const CollectTask = require('./src/models/CollectTask');
const CollectLog = require('./src/models/CollectLog');
const db = require('./src/config/database');

async function testStopTask() {
  console.log('=== 测试停止任务功能 ===\n');

  try {
    // 初始化数据库连接
    console.log('初始化数据库连接...');
    await db.connect();
    console.log('✅ 数据库连接成功\n');

    // 创建 CollectProcessor 实例
    const processor = new CollectProcessor();

    // 1. 查看当前所有任务状态
    console.log('1. 查看当前所有任务状态...');
    const allTasks = await CollectTask.findAll({ limit: 10 });
    
    console.log(`总任务数: ${allTasks.tasks.length}`);
    
    if (allTasks.tasks.length > 0) {
      console.log('\n任务列表:');
      allTasks.tasks.forEach((task, index) => {
        console.log(`\n任务 ${index + 1}:`);
        console.log(`- ID: ${task.id}`);
        console.log(`- 名称: ${task.taskName}`);
        console.log(`- 状态: ${task.status}`);
        console.log(`- 采集源: ${task.sourceId}`);
        console.log(`- 最后运行: ${task.lastRunTime || 'N/A'}`);
        console.log(`- 运行次数: ${task.runCount || 0}`);
      });
    }

    // 2. 查找运行中的任务
    console.log('\n2. 查找运行中的任务...');
    const runningTasks = await CollectTask.findAll({
      status: 'running',
      limit: 10
    });
    
    console.log(`运行中的任务数: ${runningTasks.tasks.length}`);
    
    if (runningTasks.tasks.length > 0) {
      console.log('\n运行中的任务:');
      runningTasks.tasks.forEach((task, index) => {
        console.log(`\n运行中任务 ${index + 1}:`);
        console.log(`- ID: ${task.id}`);
        console.log(`- 名称: ${task.taskName}`);
        console.log(`- 采集源: ${task.sourceId}`);
        console.log(`- 开始时间: ${task.lastRunTime}`);
        
        // 计算运行时长
        if (task.lastRunTime) {
          const startTime = new Date(task.lastRunTime);
          const now = new Date();
          const runningMinutes = Math.floor((now - startTime) / (1000 * 60));
          console.log(`- 已运行: ${runningMinutes}分钟`);
        }
      });

      // 3. 测试停止第一个运行中的任务
      const testTask = runningTasks.tasks[0];
      console.log(`\n3. 测试停止任务 ID: ${testTask.id}...`);
      
      // 检查内存状态
      const memoryStatus = processor.getTaskStatus(testTask.id);
      console.log(`任务在内存中的状态: ${memoryStatus ? '存在' : '不存在'}`);
      
      // 调用停止方法
      console.log('调用 stopTask 方法...');
      const result = await processor.stopTask(testTask.id);
      
      console.log('停止结果:', result);
      
      // 4. 验证停止后的状态
      console.log('\n4. 验证停止后的状态...');
      const updatedTask = await CollectTask.findById(testTask.id);
      if (updatedTask) {
        console.log('任务状态更新结果:');
        console.log(`- 原状态: ${testTask.status}`);
        console.log(`- 新状态: ${updatedTask.status}`);
        console.log(`- 最后运行时间: ${updatedTask.lastRunTime}`);
      }
      
      // 检查对应的日志状态
      const logs = await CollectLog.findAll({
        sourceId: testTask.sourceId,
        limit: 3
      });
      
      console.log(`\n采集源 ${testTask.sourceId} 的日志状态:`);
      if (logs.logs && logs.logs.length > 0) {
        logs.logs.forEach((log, index) => {
          console.log(`\n日志 ${index + 1}:`);
          console.log(`- ID: ${log.id}`);
          console.log(`- 状态: ${log.status}`);
          console.log(`- 类型: ${log.type}`);
          console.log(`- 开始时间: ${log.startTime}`);
          console.log(`- 结束时间: ${log.endTime || '未结束'}`);
          console.log(`- 持续时间: ${log.duration || 'N/A'}秒`);
          if (log.errorMessage) {
            console.log(`- 错误信息: ${log.errorMessage}`);
          }
        });
      }
      
    } else {
      console.log('没有运行中的任务可供测试');
      
      // 如果没有运行中的任务，我们可以创建一个测试场景
      console.log('\n创建测试场景...');
      
      // 找一个已存在的任务，手动设置为运行状态
      if (allTasks.tasks.length > 0) {
        const testTask = allTasks.tasks[0];
        console.log(`使用任务 ${testTask.id} 进行测试...`);
        
        // 手动设置为运行状态
        await CollectTask.update(testTask.id, {
          status: 'running',
          lastRunTime: new Date()
        });
        
        console.log('✅ 任务状态已设置为运行中');
        
        // 现在测试停止
        console.log('测试停止功能...');
        const result = await processor.stopTask(testTask.id);
        console.log('停止结果:', result);
        
        // 验证结果
        const updatedTask = await CollectTask.findById(testTask.id);
        console.log('更新后的任务状态:', updatedTask.status);
      }
    }

    // 5. 测试处理器初始化功能
    console.log('\n5. 测试处理器初始化功能...');
    
    // 创建新的处理器实例来测试初始化
    const newProcessor = new CollectProcessor();
    console.log('创建新的处理器实例...');
    
    // 手动调用初始化
    await newProcessor.initialize();
    console.log('✅ 处理器初始化完成');

    console.log('\n=== 测试完成 ===');
    console.log('🎯 现在可以在前端测试停止任务功能！');
    console.log('📍 前端URL: http://localhost:3000/admin/collect/tasks');

  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  testStopTask();
}

module.exports = testStopTask;

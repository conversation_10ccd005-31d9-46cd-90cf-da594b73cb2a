const CollectProcessor = require('./src/services/CollectProcessor');
const AdminCollectController = require('./src/controllers/AdminCollectController');

function testTagsFormat() {
  console.log('=== 测试Tags格式修复 ===\n');

  // 模拟视频数据
  const mockVideoData = {
    vod_name: '测试视频标题',
    type_name: '动作片',
    vod_year: '2024',
    vod_area: '中国大陆',
    vod_lang: '国语',
    vod_remarks: '高清',
    vod_blurb: '这是视频描述',
    vod_pic: 'https://example.com/image.jpg',
    vod_en: 'test-video-slug',
    vod_duration: '120分钟',
    vod_score: '8.5',
    vod_class: '美尻,肛交,脚交,美腿'  // 新增：从这个字段获取tags
  };

  console.log('模拟视频数据:');
  console.log('- vod_class:', mockVideoData.vod_class);
  console.log('- type_name:', mockVideoData.type_name);
  console.log('- vod_year:', mockVideoData.vod_year);
  console.log('- vod_area:', mockVideoData.vod_area);
  console.log('- vod_lang:', mockVideoData.vod_lang);
  console.log('- vod_remarks:', mockVideoData.vod_remarks);

  console.log('\n1. 测试 CollectProcessor.extractTags():');
  const processor = new CollectProcessor();
  const processorTags = processor.extractTags(mockVideoData);
  console.log('返回类型:', typeof processorTags);
  console.log('返回值:', processorTags);
  console.log('是否为数组:', Array.isArray(processorTags));
  console.log('JSON.stringify结果:', JSON.stringify(processorTags));

  console.log('\n2. 测试 AdminCollectController.extractTags():');
  const controllerTags = AdminCollectController.extractTags(mockVideoData);
  console.log('返回类型:', typeof controllerTags);
  console.log('返回值:', controllerTags);
  console.log('是否为数组:', Array.isArray(controllerTags));
  console.log('JSON.stringify结果:', JSON.stringify(controllerTags));

  console.log('\n3. 一致性检查:');
  const isConsistent = JSON.stringify(processorTags) === JSON.stringify(controllerTags);
  console.log('两个方法结果一致:', isConsistent ? '✅ 是' : '❌ 否');

  if (!isConsistent) {
    console.log('差异分析:');
    console.log('- CollectProcessor:', JSON.stringify(processorTags));
    console.log('- AdminCollectController:', JSON.stringify(controllerTags));
  }

  console.log('\n4. 数据库存储格式测试:');
  console.log('期望的数据库存储格式:', JSON.stringify(processorTags));
  console.log('实际会存储为:', JSON.stringify(processorTags));

  // 测试边界情况
  console.log('\n5. 边界情况测试:');
  
  const emptyVideoData = {};
  const emptyProcessorTags = processor.extractTags(emptyVideoData);
  const emptyControllerTags = AdminCollectController.extractTags(emptyVideoData);
  
  console.log('空数据 - CollectProcessor:', JSON.stringify(emptyProcessorTags));
  console.log('空数据 - AdminCollectController:', JSON.stringify(emptyControllerTags));
  console.log('空数据一致性:', JSON.stringify(emptyProcessorTags) === JSON.stringify(emptyControllerTags) ? '✅ 是' : '❌ 否');

  // 测试部分数据
  const partialVideoData = {
    type_name: '喜剧片',
    vod_year: '2023'
  };
  const partialProcessorTags = processor.extractTags(partialVideoData);
  const partialControllerTags = AdminCollectController.extractTags(partialVideoData);
  
  console.log('部分数据 - CollectProcessor:', JSON.stringify(partialProcessorTags));
  console.log('部分数据 - AdminCollectController:', JSON.stringify(partialControllerTags));
  console.log('部分数据一致性:', JSON.stringify(partialProcessorTags) === JSON.stringify(partialControllerTags) ? '✅ 是' : '❌ 否');

  console.log('\n=== 测试完成 ===');
  
  // 总结
  console.log('\n📋 修复总结:');
  console.log('✅ CollectProcessor.extractTags() 现在返回数组格式');
  console.log('✅ AdminCollectController.extractTags() 已经返回数组格式');
  console.log('✅ 两个方法现在保持一致');
  console.log('✅ 数据库将正确存储为 JSON 数组格式');
  console.log('\n预期数据库存储示例:');
  console.log('- 修复前: "[\\\"动作片\\\", \\\"2024年\\\", \\\"中国大陆\\\", \\\"国语\\\", \\\"高清\\\"]"');
  console.log('- 修复后: ["动作片", "2024年", "中国大陆", "国语", "高清"]');
}

// 如果直接运行此文件
if (require.main === module) {
  testTagsFormat();
}

module.exports = testTagsFormat;

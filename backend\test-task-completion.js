// 加载环境变量
require('dotenv').config();

const CollectProcessor = require('./src/services/CollectProcessor');
const CollectTask = require('./src/models/CollectTask');
const CollectLog = require('./src/models/CollectLog');
const db = require('./src/config/database');

async function testTaskCompletion() {
  console.log('=== 测试任务完成逻辑 ===\n');

  try {
    // 初始化数据库连接
    console.log('初始化数据库连接...');
    await db.connect();
    console.log('✅ 数据库连接成功\n');

    // 创建 CollectProcessor 实例
    const processor = new CollectProcessor();

    // 1. 测试 completeTask 方法
    console.log('1. 测试 completeTask 方法...');
    
    // 模拟任务完成结果
    const mockResult = {
      status: 'success',
      totalCollected: 30,
      totalSkipped: 5,
      totalFailed: 2,
      startTime: new Date(Date.now() - 10 * 60 * 1000), // 10分钟前开始
      endTime: new Date()
    };

    console.log('模拟结果:', mockResult);
    
    // 调用 completeTask 方法
    await processor.completeTask(13, mockResult);
    
    console.log('✅ completeTask 方法调用完成\n');

    // 2. 验证任务状态
    console.log('2. 验证任务状态...');
    const task = await CollectTask.findById(13);
    if (task) {
      console.log('任务状态:');
      console.log(`- ID: ${task.id}`);
      console.log(`- 状态: ${task.status}`);
      console.log(`- 运行次数: ${task.runCount}`);
      console.log(`- 成功次数: ${task.successCount}`);
      console.log(`- 失败次数: ${task.failCount}`);
      console.log(`- 最后运行时间: ${task.lastRunTime}`);
    } else {
      console.log('❌ 任务不存在');
    }

    // 3. 验证日志状态
    console.log('\n3. 验证日志状态...');
    const logs = await CollectLog.findAll({
      sourceId: 8, // 任务13对应的采集源ID
      limit: 5
    });
    
    console.log(`采集源8的日志总数: ${logs.logs.length}`);
    
    if (logs.logs.length > 0) {
      console.log('\n最近的日志记录:');
      logs.logs.forEach((log, index) => {
        console.log(`\n日志 ${index + 1}:`);
        console.log(`- ID: ${log.id}`);
        console.log(`- 状态: ${log.status}`);
        console.log(`- 类型: ${log.type}`);
        console.log(`- 开始时间: ${log.startTime}`);
        console.log(`- 结束时间: ${log.endTime || '未结束'}`);
        console.log(`- 采集统计: 发现${log.totalFound || 0}, 采集${log.totalCollected || 0}, 跳过${log.totalSkipped || 0}, 失败${log.totalFailed || 0}`);
        console.log(`- 持续时间: ${log.duration || 'N/A'}秒`);
        if (log.errorMessage) {
          console.log(`- 错误信息: ${log.errorMessage}`);
        }
      });
    }

    // 4. 检查运行中的日志
    console.log('\n4. 检查运行中的日志...');
    const runningLogs = await CollectLog.findAll({
      status: 'running',
      limit: 10
    });
    
    console.log(`全系统运行中的日志数: ${runningLogs.logs.length}`);
    
    if (runningLogs.logs.length > 0) {
      console.log('\n运行中的日志:');
      runningLogs.logs.forEach((log, index) => {
        const startTime = new Date(log.startTime);
        const now = new Date();
        const runningMinutes = Math.floor((now - startTime) / (1000 * 60));
        
        console.log(`\n运行中日志 ${index + 1}:`);
        console.log(`- ID: ${log.id}, 采集源: ${log.sourceId} (${log.sourceName})`);
        console.log(`- 类型: ${log.type}`);
        console.log(`- 开始时间: ${log.startTime}`);
        console.log(`- 已运行: ${runningMinutes}分钟`);
        console.log(`- 采集统计: 发现${log.totalFound || 0}, 采集${log.totalCollected || 0}, 跳过${log.totalSkipped || 0}, 失败${log.totalFailed || 0}`);
      });
    } else {
      console.log('✅ 没有运行中的日志');
    }

    // 5. 测试 handleTaskError 方法
    console.log('\n5. 测试 handleTaskError 方法...');
    
    // 创建一个测试错误
    const testError = new Error('测试错误：网络连接超时');
    
    // 如果还有运行中的日志，可以测试错误处理
    if (runningLogs.logs.length > 0) {
      const testLogId = runningLogs.logs[0].id;
      console.log(`使用日志ID ${testLogId} 测试错误处理...`);
      
      // 注意：这里不实际调用，只是演示
      console.log('模拟错误处理调用: processor.handleTaskError(taskId, testError)');
      console.log('错误信息:', testError.message);
    } else {
      console.log('没有运行中的日志可供测试错误处理');
    }

    console.log('\n=== 测试完成 ===');
    console.log('🎯 现在可以重新测试批量采集功能，查看状态更新是否正常！');
    console.log('📍 前端URL: http://localhost:3000/admin/collect/logs?taskId=13');

  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  testTaskCompletion();
}

module.exports = testTaskCompletion;

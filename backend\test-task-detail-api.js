// 测试任务详情API
require('dotenv').config();

const axios = require('axios');

async function testTaskDetailAPI() {
  console.log('=== 测试任务详情API ===\n');

  try {
    // 1. 先获取任务列表，找到一个任务ID
    console.log('1. 获取任务列表...');
    const tasksResponse = await axios.get('http://localhost:3001/api/collect/tasks');
    
    console.log('任务列表响应状态:', tasksResponse.status);
    console.log('任务列表响应数据:', JSON.stringify(tasksResponse.data, null, 2));
    
    if (tasksResponse.data.success && tasksResponse.data.data.list.length > 0) {
      const firstTask = tasksResponse.data.data.list[0];
      const taskId = firstTask.id;
      
      console.log(`\n2. 测试任务详情API，任务ID: ${taskId}`);
      
      // 2. 测试任务详情API
      const detailResponse = await axios.get(`http://localhost:3001/api/collect/tasks/${taskId}`);
      
      console.log('任务详情响应状态:', detailResponse.status);
      console.log('任务详情响应数据:', JSON.stringify(detailResponse.data, null, 2));
      
      // 3. 验证响应格式
      if (detailResponse.data.success) {
        console.log('\n✅ API响应格式正确');
        console.log('任务详情:');
        console.log(`- ID: ${detailResponse.data.data.id}`);
        console.log(`- 名称: ${detailResponse.data.data.taskName}`);
        console.log(`- 状态: ${detailResponse.data.data.status}`);
        console.log(`- 类型: ${detailResponse.data.data.taskType}`);
        console.log(`- 采集源: ${detailResponse.data.data.sourceId}`);
        console.log(`- 创建时间: ${detailResponse.data.data.createdAt}`);
        console.log(`- 最后运行: ${detailResponse.data.data.lastRunTime || 'N/A'}`);
      } else {
        console.log('❌ API返回失败:', detailResponse.data.message);
      }
      
    } else {
      console.log('❌ 没有找到任务，无法测试详情API');
    }

  } catch (error) {
    console.error('测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 如果直接运行此文件
if (require.main === module) {
  testTaskDetailAPI();
}

module.exports = testTaskDetailAPI;

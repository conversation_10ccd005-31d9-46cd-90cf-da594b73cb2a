const CollectTask = require('./src/models/CollectTask');
const CollectLog = require('./src/models/CollectLog');
const CollectProcessor = require('./src/services/CollectProcessor');

async function testTaskStatus() {
  try {
    console.log('=== 测试任务状态管理 ===');

    // 1. 查看当前任务状态
    console.log('\n1. 查看当前任务状态...');
    const tasks = await CollectTask.findAll({ limit: 10 });
    console.log('任务总数:', tasks.tasks.length);
    
    tasks.tasks.forEach(task => {
      console.log(`任务 ${task.id}: ${task.taskName} - 状态: ${task.status}`);
    });

    // 2. 查看运行中的任务
    console.log('\n2. 查看运行中的任务...');
    const runningTasks = await CollectTask.findAll({ status: 'running', limit: 5 });
    console.log('运行中任务数:', runningTasks.tasks.length);
    
    if (runningTasks.tasks.length > 0) {
      console.log('运行中的任务:');
      runningTasks.tasks.forEach(task => {
        console.log(`- 任务 ${task.id}: ${task.taskName}`);
        console.log(`  状态: ${task.status}, 运行次数: ${task.runCount}`);
        console.log(`  成功: ${task.successCount}, 失败: ${task.failCount}`);
      });
    }

    // 3. 检查内存中的任务状态
    console.log('\n3. 检查内存中的任务状态...');
    runningTasks.tasks.forEach(task => {
      const memoryStatus = CollectProcessor.getTaskStatus(task.id);
      console.log(`任务 ${task.id} 内存状态:`, memoryStatus ? '运行中' : '未运行');
    });

    // 4. 查看最近的日志
    console.log('\n4. 查看最近的日志...');
    const logs = await CollectLog.findAll({ limit: 5 });
    console.log('日志总数:', logs.logs.length);
    
    if (logs.logs.length > 0) {
      console.log('最近的日志:');
      logs.logs.forEach(log => {
        console.log(`- 日志 ${log.id}: ${log.sourceName}`);
        console.log(`  状态: ${log.status}, 采集数: ${log.totalCollected || 0}`);
        console.log(`  开始: ${log.startTime}, 结束: ${log.endTime || '未结束'}`);
      });
    }

    // 5. 测试状态枚举值
    console.log('\n5. 测试状态枚举值...');
    const validStatuses = ['active', 'inactive', 'running', 'paused'];
    console.log('有效的任务状态:', validStatuses.join(', '));
    
    const validLogStatuses = ['running', 'success', 'failed', 'stopped'];
    console.log('有效的日志状态:', validLogStatuses.join(', '));

    console.log('\n=== 测试完成 ===');
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  testTaskStatus().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('测试执行失败:', error);
    process.exit(1);
  });
}

module.exports = testTaskStatus;

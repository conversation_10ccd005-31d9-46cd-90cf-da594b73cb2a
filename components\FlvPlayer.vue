<template>
  <div class="flv-player-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="w-full h-full bg-gray-800 flex items-center justify-center">
      <div class="text-center">
        <div class="w-12 h-12 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mb-4"></div>
        <p class="text-white">加载播放器中...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="w-full h-full bg-gray-800 flex items-center justify-center">
      <div class="text-center">
        <div class="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mb-4">
          <svg class="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
          </svg>
        </div>
        <p class="text-white text-lg mb-2">播放失败</p>
        <p class="text-gray-400 text-sm mb-4">{{ error }}</p>
        <button
          @click="initPlayer"
          class="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
        >
          重新加载
        </button>
      </div>
    </div>

    <!-- 视频播放器 -->
    <video
      v-show="!loading && !error"
      ref="videoElement"
      class="w-full h-full"
      :poster="poster"
      controls
      muted
      playsinline
    >
      <p class="text-white p-4">您的浏览器不支持视频播放</p>
    </video>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'

// Props
const props = defineProps({
  src: {
    type: String,
    required: true
  },
  poster: {
    type: String,
    default: ''
  },
  autoplay: {
    type: Boolean,
    default: true
  },
  muted: {
    type: Boolean,
    default: true
  },
  playerId: {
    type: String,
    default: () => `flv-player-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
})

// Emits
const emit = defineEmits(['ready', 'error', 'play', 'pause'])

// 播放器管理器
const playerManager = usePlayer(props.playerId)

// 响应式数据
const videoElement = ref(null)
const loading = ref(true)
const error = ref(null)

// 初始化播放器
const initPlayer = async () => {
  try {
    loading.value = true
    error.value = null

    console.log('🎬 初始化 FLV 播放器:', { playerId: props.playerId, src: props.src })

    // 等待 DOM 更新
    await nextTick()

    if (!videoElement.value) {
      throw new Error('视频元素未找到')
    }

    // 使用播放器管理器安全创建播放器
    const playerInstances = await playerManager.safeCreate(async () => {
      // 动态加载 Plyr CSS
      if (typeof document !== 'undefined' && !document.querySelector('link[href*="plyr"]')) {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = '/lib/plyr/plyr.css'
        document.head.appendChild(link)
        console.log('✅ Plyr CSS 加载完成')

        // 等待 CSS 加载完成
        await new Promise((resolve) => {
          link.onload = resolve
          link.onerror = resolve
          setTimeout(resolve, 1000) // 超时保护
        })
      }

      // 动态导入 flv.js 和 Plyr
      const [flvjs, Plyr] = await Promise.all([
        import('flv.js'),
        import('plyr')
      ])

      // 检查 FLV 支持
      if (!flvjs.default.isSupported()) {
        throw new Error('您的浏览器不支持 FLV 播放')
      }

      // 创建 FLV 播放器
      const flvPlayer = flvjs.default.createPlayer({
        type: 'flv',
        url: props.src,
        isLive: true,
        hasAudio: true,
        hasVideo: true
      }, {
        enableWorker: false,
        enableStashBuffer: false,
        stashInitialSize: 128,
        lazyLoad: true,
        lazyLoadMaxDuration: 3 * 60,
        lazyLoadRecoverDuration: 30,
        deferLoadAfterSourceOpen: false,
        autoCleanupSourceBuffer: true,
        autoCleanupMaxBackwardDuration: 3 * 60,
        autoCleanupMinBackwardDuration: 2 * 60,
        statisticsInfoReportInterval: 600,
        fixAudioTimestampGap: true,
        accurateSeek: false,
        seekType: 'range',
        seekParamStart: 'bstart',
        seekParamEnd: 'bend',
        rangeLoadZeroStart: false,
        lazyLoadStartTime: 0,
        headers: {
          'Referer': typeof window !== 'undefined' ? window.location.origin : ''
        }
      })

      // 绑定到视频元素
      flvPlayer.attachMediaElement(videoElement.value)

      // 监听 FLV 播放器事件
      flvPlayer.on('error', (errorType, errorDetail, errorInfo) => {
        console.error('❌ FLV 播放器错误:', { errorType, errorDetail, errorInfo })
        error.value = `播放错误: ${errorDetail}`
        loading.value = false
      })

      flvPlayer.on('loading_complete', () => {
        console.log('✅ FLV 流加载完成')
      })

      flvPlayer.on('recovered_early_eof', () => {
        console.log('🔄 FLV 流恢复')
      })

      // 加载流
      await flvPlayer.load()

      // 初始化 Plyr（直播模式）
      const plyrInstance = new Plyr.default(videoElement.value, {
        iconUrl: '/plyr.svg',
        controls: [
          'play-large',
          'play',
          'mute',
          'volume',
          'pip',
          'fullscreen'
        ],
        // 直播流不需要设置菜单
        settings: [],
        autoplay: props.autoplay,
        muted: props.muted,
        clickToPlay: true,
        hideControls: false,
        resetOnEnd: false,
        disableContextMenu: true,
        // 直播特定配置
        seekTime: 0,  // 禁用快进/快退
        displayDuration: false,  // 不显示总时长
        invertTime: false,  // 不显示倒计时
        tooltips: {
          controls: true,
          seek: false  // 禁用进度条提示
        }
      })

      // 监听 Plyr 事件
      plyrInstance.on('ready', () => {
        console.log('✅ Plyr 播放器就绪')
        loading.value = false
        emit('ready')
      })

      plyrInstance.on('play', () => {
        console.log('▶️ 开始播放')
        emit('play')
      })

      plyrInstance.on('pause', () => {
        console.log('⏸️ 暂停播放')
        emit('pause')
      })

      plyrInstance.on('error', (event) => {
        console.error('❌ Plyr 播放器错误:', event)
        error.value = '播放器初始化失败'
        loading.value = false
        emit('error', event)
      })

      // PIP 事件监听
      plyrInstance.on('enterpictureinpicture', () => {
        console.log('📺 进入画中画模式')
      })

      plyrInstance.on('leavepictureinpicture', () => {
        console.log('📺 退出画中画模式')
      })

      // 自动播放
      if (props.autoplay) {
        setTimeout(() => {
          if (plyrInstance && !plyrInstance.playing) {
            plyrInstance.play().catch(err => {
              console.warn('⚠️ 自动播放失败:', err)
            })
          }
        }, 1000)
      }

      // 返回播放器实例供管理器管理
      return {
        flvPlayer,
        plyrInstance,
        type: 'flv-plyr'
      }
    }, 'flv-plyr')

    if (!playerInstances) {
      throw new Error('播放器创建失败')
    }

    console.log('✅ FLV + Plyr 播放器创建成功')

  } catch (err) {
    console.error('❌ 播放器初始化失败:', err)
    error.value = err.message || '播放器初始化失败'
    loading.value = false
    emit('error', err)
  }
}

// 清理播放器
const destroyPlayer = () => {
  console.log('🧹 清理播放器资源:', props.playerId)
  playerManager.destroy()
}

// 监听 src 变化
watch(() => props.src, (newSrc) => {
  if (newSrc) {
    destroyPlayer()
    initPlayer()
  }
}, { immediate: false })

// 生命周期
onMounted(() => {
  if (props.src) {
    initPlayer()
  }
})

onUnmounted(() => {
  destroyPlayer()
})

// 暴露方法
defineExpose({
  play: () => {
    const player = playerManager.get()
    return player?.plyrInstance?.play()
  },
  pause: () => {
    const player = playerManager.get()
    return player?.plyrInstance?.pause()
  },
  pip: () => {
    const player = playerManager.get()
    return player?.plyrInstance?.pip()
  },
  destroy: destroyPlayer,
  getPlayer: () => playerManager.get()
})
</script>

<style scoped>
.flv-player-container {
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: inherit;
  overflow: hidden;
}

/* Plyr 直播样式覆盖 */
:deep(.plyr) {
  width: 100%;
  height: 100%;
}

:deep(.plyr__video-wrapper) {
  background: #000;
}

:deep(.plyr__controls) {
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
}

:deep(.plyr__control--overlaid) {
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid #f97316;
}

:deep(.plyr__control--overlaid:hover) {
  background: rgba(249, 115, 22, 0.9);
}

/* 隐藏直播不需要的元素 */
:deep(.plyr__progress),
:deep(.plyr__time),
:deep(.plyr__duration) {
  display: none !important;
}

/* 直播标识 */
:deep(.plyr__controls)::before {
  content: "🔴 LIVE";
  color: #ef4444;
  font-size: 12px;
  font-weight: bold;
  margin-right: 10px;
  padding: 2px 6px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 4px;
}

/* PIP 按钮样式 */
:deep(.plyr__control[data-plyr="pip"]) {
  color: #f97316;
}

:deep(.plyr__control[data-plyr="pip"]:hover) {
  background: rgba(249, 115, 22, 0.2);
}

:deep(.plyr__control[data-plyr="pip"]:focus) {
  box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.5);
}
</style>

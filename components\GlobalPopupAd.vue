<template>
  <Teleport to="body">
    <div
      v-if="visible"
      class="fixed inset-0 z-[9999] flex items-center justify-center p-4"
      @click="handleAnyClick"
    >
      <!-- 背景遮罩 -->
      <div class="absolute inset-0 bg-black/70 backdrop-blur-sm"></div>

      <!-- 弹窗内容 -->
      <div
        class="relative bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden transform transition-all duration-300 scale-100 cursor-pointer"
        :class="{ 'animate-popup-in': visible }"
        @click="handleAnyClick"
      >


        <!-- 内容区域 -->
        <div class="p-6">
          <!-- 图片区域 -->
          <div v-if="adConfig.imageUrl" class="relative mb-6 -mx-6 -mt-6">
            <img
              :src="adConfig.imageUrl"
              :alt="adConfig.title"
              class="w-full h-48 object-cover rounded-t-2xl"
              @error="handleImageError"
              @load="handleImageLoad"
            />
            <div class="absolute inset-0 bg-gradient-to-t from-gray-800/60 to-transparent rounded-t-2xl"></div>
            <!-- 关闭按钮在图片上方 -->
            <button
              @click="handleAnyClick"
              class="absolute top-4 right-4 z-20 w-8 h-8 rounded-full bg-black/50 hover:bg-black/70 text-white transition-all duration-200 flex items-center justify-center backdrop-blur-sm"
              title="访问链接"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          <!-- 没有图片时的关闭按钮 -->
          <button
            v-if="!adConfig.imageUrl"
            @click="handleAnyClick"
            class="absolute top-4 right-4 z-10 w-8 h-8 rounded-full bg-gray-700/80 hover:bg-gray-600 text-gray-300 hover:text-white transition-all duration-200 flex items-center justify-center"
            title="访问链接"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>

          <h3 v-if="adConfig.title" class="text-xl font-bold text-white mb-3">
            {{ adConfig.title }}
          </h3>

          <p v-if="adConfig.content" class="text-gray-300 mb-6 leading-relaxed">
            {{ adConfig.content }}
          </p>

          <!-- 点击提示 -->
          <div class="text-center">
            <div class="inline-flex items-center space-x-2 bg-gradient-to-r from-orange-500 to-red-500 text-white font-medium py-3 px-6 rounded-xl">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
              </svg>
              <span>点击任意位置或按ESC键访问</span>
            </div>
          </div>
        </div>

        <!-- 装饰性元素 -->
        <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-orange-500 to-red-500"></div>

        <!-- 悬浮效果提示 -->
        <div class="absolute inset-0 bg-gradient-to-r from-orange-500/10 to-red-500/10 opacity-0 hover:opacity-100 transition-opacity duration-200 rounded-2xl"></div>
      </div>
    </div>
  </Teleport>
</template>

<script setup>
const props = defineProps({
  adConfig: {
    type: Object,
    required: true,
    default: () => ({
      title: '',
      content: '',
      imageUrl: '',
      targetUrl: '',
      openInNewWindow: true,
      autoCloseDelay: 0
    })
  }
})

const emit = defineEmits(['close', 'click'])

const visible = ref(true)

// 处理图片加载错误
const handleImageError = (event) => {
  console.error('弹窗图片加载失败:', event.target.src)
  event.target.style.display = 'none'
}

// 处理图片加载成功
const handleImageLoad = (event) => {
  console.log('弹窗图片加载成功:', event.target.src)
}

// 处理任意位置点击（包括关闭按钮）
const handleAnyClick = () => {
  // 发出点击事件
  emit('click', props.adConfig.targetUrl)

  // 打开目标链接
  if (props.adConfig.targetUrl) {
    if (props.adConfig.openInNewWindow) {
      window.open(props.adConfig.targetUrl, '_blank', 'noopener,noreferrer')
    } else {
      window.location.href = props.adConfig.targetUrl
    }
  }

  // 关闭弹窗
  closePopup()
}

// 关闭弹窗（仅关闭，不跳转）
const closePopup = () => {
  visible.value = false
  setTimeout(() => {
    emit('close')
  }, 300) // 等待动画完成
}

// 自动关闭
onMounted(() => {
  if (props.adConfig.autoCloseDelay > 0) {
    setTimeout(() => {
      closePopup()
    }, props.adConfig.autoCloseDelay)
  }

  // 添加ESC键处理（也跳转）
  const handleKeyDown = (event) => {
    if (event.key === 'Escape') {
      handleAnyClick()
    }
  }

  document.addEventListener('keydown', handleKeyDown)

  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyDown)
  })
})
</script>

<style scoped>
@keyframes popup-in {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.animate-popup-in {
  animation: popup-in 0.3s ease-out;
}

/* 防止页面滚动 */
:global(body.popup-open) {
  overflow: hidden;
}
</style>

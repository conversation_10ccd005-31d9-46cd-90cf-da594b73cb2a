<template>
  <div class="relative">
    <button
      @click="toggleDropdown"
      class="flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-2 text-gray-300 hover:text-orange-400 transition-colors"
    >
      <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
      </svg>
      <span class="text-xs sm:text-sm hidden xs:inline">{{ getCurrentLanguageName() }}</span>
      <span class="text-xs sm:text-sm xs:hidden">{{ getCurrentLanguageShort() }}</span>
      <svg class="w-3 h-3 sm:w-4 sm:h-4 transition-transform" :class="{ 'rotate-180': isOpen }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
      </svg>
    </button>

    <!-- 下拉菜单 -->
    <div
      v-show="isOpen"
      class="absolute right-0 mt-2 w-40 bg-gray-800 border border-gray-700 rounded-lg shadow-lg z-50"
    >
      <div class="py-1">
        <button
          v-for="localeCode in availableLocales"
          :key="localeCode"
          @click="switchLanguage(localeCode)"
          class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-orange-400 transition-colors flex items-center justify-between"
          :class="{ 'text-orange-400 bg-gray-700': localeCode === currentLocale }"
        >
          <span>{{ getLanguageName(localeCode) }}</span>
          <span v-if="localeCode === currentLocale" class="text-orange-400">✓</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
const { locale, locales } = useI18n()
const switchLocalePath = useSwitchLocalePath()

const isOpen = ref(false)
const currentLocale = computed(() => locale.value)

// 获取可用的语言代码列表，过滤掉别名
const availableLocales = computed(() => {
  const codes = locales.value.map(locale => locale.code)
  // 过滤掉zh别名，只保留主要语言代码
  return codes.filter(code => code !== 'zh')
})

// 语言名称映射
const languageNames = {
  'zh-CN': '简体中文',
  'zh-TW': '繁體中文',
  'ja-JP': '日本語',
  'en-US': 'English'
}

// 语言简称映射
const languageShorts = {
  'zh-CN': '简',
  'zh-TW': '繁',
  'ja-JP': '日',
  'en-US': 'EN'
}

// 获取语言名称
const getLanguageName = (localeCode) => {
  return languageNames[localeCode] || localeCode
}

// 获取语言简称
const getLanguageShort = (localeCode) => {
  return languageShorts[localeCode] || localeCode.split('-')[0].toUpperCase()
}

// 获取当前语言名称
const getCurrentLanguageName = () => {
  return getLanguageName(currentLocale.value)
}

// 获取当前语言简称
const getCurrentLanguageShort = () => {
  return getLanguageShort(currentLocale.value)
}

// 切换语言
const switchLanguage = async (newLocale) => {
  if (newLocale !== currentLocale.value) {
    await navigateTo(switchLocalePath(newLocale))
  }
  isOpen.value = false
}

// 切换下拉菜单
const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

// 点击外部关闭下拉菜单
const closeDropdown = (event) => {
  if (!event.target.closest('.relative')) {
    isOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', closeDropdown)
})

onUnmounted(() => {
  document.removeEventListener('click', closeDropdown)
})
</script>

<style scoped>
/* 确保下拉菜单在最上层 */
.relative {
  z-index: 50;
}
</style>

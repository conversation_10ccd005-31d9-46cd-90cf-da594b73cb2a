<template>
  <div class="video-player-container">
    <video
      ref="videoElement"
      class="plyr"
      controls
      playsinline
      crossorigin
      :poster="poster"
    ></video>
  </div>
</template>

<script setup>
import { onMounted, ref, onUnmounted, watch } from 'vue'

const videoElement = ref(null)
let player = null
let hls = null

const props = defineProps({
  src: {
    type: String,
    required: true
  },
  poster: {
    type: String,
    default: ''
  },
  autoplay: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['ready', 'play', 'pause', 'ended', 'error'])

const initializePlayer = async () => {
  if (!process.client || !videoElement.value || !props.src) {
    return
  }

  try {


    // 动态导入播放器库
    const [{ default: Plyr }, { default: Hls }] = await Promise.all([
      import('plyr'),
      import('hls.js')
    ])

    // 清理现有播放器
    destroyPlayer()

    console.log('初始化视频播放器:', props.src)

    // 检查是否为 HLS 视频
    const isHLS = props.src.includes('.m3u8')

    if (isHLS && Hls.isSupported()) {
      // 使用 HLS.js 并优化缓冲配置
      hls = new Hls({
        // 缓冲配置优化
        maxBufferLength: 30,           // 最大缓冲长度（秒）
        maxMaxBufferLength: 600,       // 最大缓冲长度上限
        maxBufferSize: 60 * 1000 * 1000, // 最大缓冲大小（60MB）
        maxBufferHole: 0.5,            // 最大缓冲洞大小

        // 网络配置
        manifestLoadingTimeOut: 10000,  // manifest 加载超时
        manifestLoadingMaxRetry: 4,     // manifest 最大重试次数
        manifestLoadingRetryDelay: 500, // manifest 重试延迟

        // 分片配置
        fragLoadingTimeOut: 20000,      // 分片加载超时
        fragLoadingMaxRetry: 6,         // 分片最大重试次数
        fragLoadingRetryDelay: 1000,    // 分片重试延迟

        // 其他优化
        enableWorker: false,            // 禁用 worker（避免某些兼容性问题）
        lowLatencyMode: false,          // 禁用低延迟模式
        backBufferLength: 90,           // 后向缓冲长度

        // 启动位置配置
        startPosition: -1,              // 从直播点开始
        liveSyncDurationCount: 3,       // 直播同步持续时间计数
        liveMaxLatencyDurationCount: 10, // 直播最大延迟持续时间计数

        // 修复XMLHttpRequest responseType冲突
        xhrSetup: function(xhr, url) {
          // 确保XMLHttpRequest配置正确，避免responseType冲突
          xhr.responseType = 'arraybuffer'
        }
      })

      hls.loadSource(props.src)
      hls.attachMedia(videoElement.value)

      // 增强的错误处理
      hls.on(Hls.Events.ERROR, (event, data) => {
        console.error('HLS 错误:', data)

        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.log('网络错误，尝试恢复...')
              hls.startLoad()
              break
            case Hls.ErrorTypes.MEDIA_ERROR:
              console.log('媒体错误，尝试恢复...')
              hls.recoverMediaError()
              break
            default:
              console.log('无法恢复的错误')
              emit('error', data)
              break
          }
        } else {
          // 非致命错误，记录但继续播放
          console.warn('HLS 非致命错误:', data.details)
        }
      })

      // 添加更多事件监听
      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log('HLS manifest 解析完成')
      })

      hls.on(Hls.Events.FRAG_BUFFERED, () => {
        console.log('分片缓冲完成')
      })

      console.log('HLS 播放器初始化完成')
    } else if (isHLS && videoElement.value.canPlayType('application/vnd.apple.mpegurl')) {
      // 浏览器原生支持 HLS
      videoElement.value.src = props.src
      console.log('使用浏览器原生 HLS 支持')
    } else {
      // 普通视频
      videoElement.value.src = props.src
      console.log('使用标准视频播放')
    }

    // 初始化 Plyr
    player = new Plyr(videoElement.value, {
      iconUrl: '/plyr.svg',
      autoplay: props.autoplay,
      controls: [
        'play-large',
        'play',
        'progress',
        'current-time',
        'duration',
        'mute',
        'volume',
        'settings',
        'pip',
        'fullscreen'
      ]
    })

    // 事件监听
    player.on('ready', () => {
      console.log('播放器准备就绪')
      emit('ready', player)
    })

    player.on('play', () => {
      emit('play')
    })

    player.on('pause', () => {
      emit('pause')
    })

    player.on('ended', () => {
      emit('ended')
    })

    player.on('error', (event) => {
      console.error('播放器错误:', event)
      emit('error', event)
    })

    console.log('播放器初始化完成')

  } catch (error) {
    console.error('播放器初始化失败:', error)
    emit('error', error)
  }
}

const destroyPlayer = () => {
  try {
    if (hls) {
      hls.destroy()
      hls = null
    }
    if (player) {
      player.destroy()
      player = null
    }
  } catch (error) {
    console.warn('清理播放器时出错:', error)
  }
}

// 监听 src 变化
watch(() => props.src, (newSrc) => {
  if (newSrc && process.client) {
    initializePlayer()
  }
})

onMounted(() => {
  if (process.client && props.src) {
    // 延迟初始化确保 DOM 完全渲染
    setTimeout(() => {
      initializePlayer()
    }, 100)
  }
})

onUnmounted(() => {
  destroyPlayer()
})
</script>

<style scoped>
.video-player-container {
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 12px;
  overflow: hidden;
}

:deep(.plyr) {
  width: 100%;
  height: 100%;
}

:deep(.plyr--video) {
  background: #000;
}
</style>

/**
 * 统计分析组合函数
 * 统一管理各种统计工具的集成
 */

import { siteConfig } from '~/config/site.js'

export const useAnalytics = () => {
  const analyticsConfig = siteConfig.analytics
  
  // 初始化Google Analytics
  const initGoogleAnalytics = () => {
    if (!analyticsConfig.googleAnalytics.enabled || !analyticsConfig.googleAnalytics.measurementId) {
      return
    }
    
    const measurementId = analyticsConfig.googleAnalytics.measurementId
    const config = analyticsConfig.googleAnalytics.config
    
    // 加载gtag脚本
    useHead({
      script: [
        {
          src: `https://www.googletagmanager.com/gtag/js?id=${measurementId}`,
          async: true
        },
        {
          innerHTML: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${measurementId}', ${JSON.stringify(config)});
          `
        }
      ]
    })
  }
  
  // 初始化百度统计
  const initBaiduAnalytics = () => {
    if (!analyticsConfig.baiduAnalytics.enabled || !analyticsConfig.baiduAnalytics.siteId) {
      return
    }
    
    const siteId = analyticsConfig.baiduAnalytics.siteId
    
    useHead({
      script: [
        {
          innerHTML: `
            var _hmt = _hmt || [];
            (function() {
              var hm = document.createElement("script");
              hm.src = "https://hm.baidu.com/hm.js?${siteId}";
              var s = document.getElementsByTagName("script")[0]; 
              s.parentNode.insertBefore(hm, s);
            })();
          `
        }
      ]
    })
  }
  
  // 初始化友盟统计
  const initUmengAnalytics = () => {
    if (!analyticsConfig.umengAnalytics.enabled || !analyticsConfig.umengAnalytics.siteId) {
      return
    }
    
    const siteId = analyticsConfig.umengAnalytics.siteId
    const autoPageview = analyticsConfig.umengAnalytics.autoPageview
    
    useHead({
      script: [
        {
          innerHTML: `
            var cnzz_protocol = (("https:" == document.location.protocol) ? "https://" : "http://");
            document.write(unescape("%3Cspan id='cnzz_stat_icon_${siteId}'%3E%3C/span%3E%3Cscript src='" + cnzz_protocol + "s4.cnzz.com/z_stat.php%3Fid%3D${siteId}${autoPageview ? '' : '%26show%3Dpic'}" + "' type='text/javascript'%3E%3C/script%3E"));
          `
        }
      ]
    })
  }
  
  // 初始化51LA统计
  const init51LAAnalytics = () => {
    if (!analyticsConfig.la51Analytics.enabled || !analyticsConfig.la51Analytics.siteId) {
      return
    }
    
    const siteId = analyticsConfig.la51Analytics.siteId
    
    useHead({
      script: [
        {
          innerHTML: `
            !function(p){"use strict";!function(t){var s=window,e=document,i=p,c="".concat("https:"===e.location.protocol?"https://":"http://","sdk.51.la/js-sdk-pro.min.js"),n=e.createElement("script"),r=e.getElementsByTagName("script")[0];n.type="text/javascript",n.setAttribute("charset","UTF-8"),n.async=!0,n.src=c,n.id="LA_COLLECT",i.d=n;var o=function(){s.LA.ids.push(i)};s.LA?s.LA.ids&&o():(s.LA=p,s.LA.ids=[],o()),r.parentNode.insertBefore(n,r)}()}({id:"${siteId}",ck:"${siteId}"});
          `
        }
      ]
    })
  }
  
  // 初始化自定义统计代码
  const initCustomAnalytics = () => {
    if (!analyticsConfig.customAnalytics.enabled) {
      return
    }
    
    const { headScript, bodyScript, onPageLoad } = analyticsConfig.customAnalytics
    
    // 头部脚本
    if (headScript) {
      useHead({
        script: [
          {
            innerHTML: headScript
          }
        ]
      })
    }
    
    // 页面底部脚本
    if (bodyScript) {
      useHead({
        script: [
          {
            innerHTML: bodyScript,
            body: true
          }
        ]
      })
    }
    
    // 页面加载完成后执行
    if (onPageLoad && import.meta.client) {
      onMounted(() => {
        try {
          eval(onPageLoad)
        } catch (error) {
          console.error('自定义统计代码执行错误:', error)
        }
      })
    }
  }
  
  // 统计页面浏览
  const trackPageView = (path, title) => {
    if (!analyticsConfig.enabled) return
    
    // Google Analytics
    if (analyticsConfig.googleAnalytics.enabled && window.gtag) {
      window.gtag('config', analyticsConfig.googleAnalytics.measurementId, {
        page_path: path,
        page_title: title
      })
    }
    
    // 百度统计
    if (analyticsConfig.baiduAnalytics.enabled && window._hmt) {
      window._hmt.push(['_trackPageview', path])
    }
  }
  
  // 统计事件
  const trackEvent = (eventName, parameters = {}) => {
    if (!analyticsConfig.enabled || !analyticsConfig.events[`track${eventName}`]) return
    
    // Google Analytics
    if (analyticsConfig.googleAnalytics.enabled && window.gtag) {
      window.gtag('event', eventName.toLowerCase(), parameters)
    }
    
    // 百度统计
    if (analyticsConfig.baiduAnalytics.enabled && window._hmt) {
      window._hmt.push(['_trackEvent', eventName, parameters.action || '', parameters.label || ''])
    }
  }
  
  // 统计视频播放
  const trackVideoPlay = (videoId, videoTitle) => {
    trackEvent('VideoPlay', {
      video_id: videoId,
      video_title: videoTitle,
      action: 'play'
    })
  }
  
  // 统计搜索
  const trackSearch = (searchTerm, resultCount = 0) => {
    trackEvent('Search', {
      search_term: searchTerm,
      result_count: resultCount,
      action: 'search'
    })
  }
  
  // 统计下载
  const trackDownload = (fileUrl, fileName) => {
    trackEvent('Download', {
      file_url: fileUrl,
      file_name: fileName,
      action: 'download'
    })
  }
  
  // 统计分享
  const trackShare = (contentType, contentId, platform) => {
    trackEvent('Share', {
      content_type: contentType,
      content_id: contentId,
      platform: platform,
      action: 'share'
    })
  }
  
  // 初始化所有统计工具
  const initAllAnalytics = () => {
    if (!analyticsConfig.enabled) return
    
    initGoogleAnalytics()
    initBaiduAnalytics()
    initUmengAnalytics()
    init51LAAnalytics()
    initCustomAnalytics()
  }
  
  return {
    initAllAnalytics,
    trackPageView,
    trackEvent,
    trackVideoPlay,
    trackSearch,
    trackDownload,
    trackShare,
    analyticsConfig
  }
}

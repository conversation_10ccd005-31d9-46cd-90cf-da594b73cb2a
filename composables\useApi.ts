/**
 * API 请求工具组合函数
 * 统一管理 API 请求配置和方法
 */

import { createApiConfig } from '~/config/site.js'

export const useApi = () => {
  // 创建用户前端 API 请求函数
  const apiUser = (url: string, options: any = {}) => {
    const config = createApiConfig('user')
    return $fetch(url, {
      ...config,
      ...options
    })
  }

  // 创建管理后台 API 请求函数
  const apiAdmin = (url: string, options: any = {}) => {
    const config = createApiConfig('admin')
    return $fetch(url, {
      ...config,
      ...options
    })
  }

  // 带认证的管理后台 API 请求
  const apiAdminAuth = (url: string, options: any = {}) => {
    const { getToken } = useAuth()
    const token = getToken()
    
    if (!token) {
      throw new Error('未找到认证令牌')
    }

    const config = createApiConfig('admin')
    return $fetch(url, {
      ...config,
      ...options,
      headers: {
        ...config.headers,
        'Authorization': `Bearer ${token}`,
        ...options.headers
      }
    })
  }

  return {
    apiUser,
    apiAdmin,
    apiAdminAuth
  }
}

export const useAuth = () => {
  // 获取认证token (SSR兼容)
  const getToken = () => {
    if (process.client) {
      // 客户端优先从localStorage获取，fallback到cookie
      return localStorage.getItem('admin_token') || useCookie('admin_token').value
    } else {
      // 服务端从cookie获取
      return useCookie('admin_token').value
    }
  }

  // 获取用户信息 (SSR兼容)
  const getUser = () => {
    if (process.client) {
      // 客户端优先从localStorage获取
      const userStr = localStorage.getItem('admin_user')
      if (userStr) {
        try {
          return JSON.parse(userStr)
        } catch (error) {
          console.error('Error parsing localStorage user data:', error)
          localStorage.removeItem('admin_user')
          return null
        }
      }
      // fallback到cookie
      const cookieUser = useCookie('admin_user').value
      if (cookieUser) {
        try {
          return typeof cookieUser === 'string' ? JSON.parse(cookieUser) : cookieUser
        } catch (error) {
          console.error('Error parsing cookie user data:', error)
          return null
        }
      }
      return null
    } else {
      // 服务端从cookie获取
      const cookieUser = useCookie('admin_user').value
      if (cookieUser) {
        try {
          return typeof cookieUser === 'string' ? JSON.parse(cookieUser) : cookieUser
        } catch (error) {
          console.error('Error parsing cookie user data:', error)
          return null
        }
      }
      return null
    }
  }

  // 设置认证信息 (同时设置localStorage和cookie)
  const setAuth = (token: string, user: any) => {
    const tokenCookie = useCookie('admin_token', {
      maxAge: 60 * 60 * 24 * 7, // 7天
      httpOnly: false,
      secure: false,
      sameSite: 'lax'
    })
    const userCookie = useCookie('admin_user', {
      maxAge: 60 * 60 * 24 * 7, // 7天
      httpOnly: false,
      secure: false,
      sameSite: 'lax'
    })

    // 设置cookie (SSR兼容)
    tokenCookie.value = token
    userCookie.value = JSON.stringify(user)

    // 客户端同时设置localStorage
    if (process.client) {
      localStorage.setItem('admin_token', token)
      localStorage.setItem('admin_user', JSON.stringify(user))
    }
  }

  // 清除认证信息 (同时清除localStorage和cookie)
  const clearAuth = () => {
    const tokenCookie = useCookie('admin_token')
    const userCookie = useCookie('admin_user')

    // 清除cookie
    tokenCookie.value = null
    userCookie.value = null

    // 客户端同时清除localStorage
    if (process.client) {
      localStorage.removeItem('admin_token')
      localStorage.removeItem('admin_user')
    }
  }

  // 检查是否已认证 (SSR兼容)
  const isAuthenticated = () => {
    return !!getToken()
  }

  return {
    getToken,
    getUser,
    setAuth,
    clearAuth,
    isAuthenticated
  }
}

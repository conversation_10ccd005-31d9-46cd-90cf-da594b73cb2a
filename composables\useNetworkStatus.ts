/**
 * 网络状态监控组合函数
 * 用于监控网络连接状态和诊断网络问题
 */

export const useNetworkStatus = () => {
  const isOnline = ref(true)
  const connectionType = ref('unknown')
  const lastError = ref(null)
  const retryCount = ref(0)

  // 检查网络连接状态
  const checkNetworkStatus = () => {
    if (typeof navigator !== 'undefined') {
      isOnline.value = navigator.onLine
      
      // 获取连接类型（如果支持）
      if ('connection' in navigator) {
        const connection = (navigator as any).connection
        connectionType.value = connection?.effectiveType || 'unknown'
      }
    }
  }

  // 网络状态变化监听
  const setupNetworkListeners = () => {
    if (typeof window !== 'undefined') {
      window.addEventListener('online', () => {
        isOnline.value = true
        console.log('🌐 网络连接已恢复')
      })
      
      window.addEventListener('offline', () => {
        isOnline.value = false
        console.log('📵 网络连接已断开')
      })
    }
  }

  // 带重试的API请求
  const fetchWithRetry = async (fetchFn: Function, maxRetries = 3, delay = 1000) => {
    let lastErr = null
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        retryCount.value = i
        const result = await fetchFn()
        retryCount.value = 0
        lastError.value = null
        return result
      } catch (error) {
        lastErr = error
        lastError.value = error
        
        console.warn(`🔄 请求失败，第 ${i + 1} 次尝试:`, error.message)
        
        // 如果是最后一次重试，抛出错误
        if (i === maxRetries) {
          throw error
        }
        
        // 检查错误类型，决定是否重试
        if (error.name === 'AbortError') {
          throw error // 不重试被取消的请求
        }
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
      }
    }
    
    throw lastErr
  }

  // 诊断网络问题
  const diagnoseNetworkIssue = (error: any) => {
    const errorMessage = error?.message || ''
    
    if (!isOnline.value) {
      return {
        type: 'offline',
        message: '网络连接已断开，请检查网络设置',
        suggestion: '请检查WiFi或移动数据连接'
      }
    }
    
    if (errorMessage.includes('ECONNRESET')) {
      return {
        type: 'connection_reset',
        message: '网络连接被重置',
        suggestion: '这通常是网络不稳定导致的，请稍后重试'
      }
    }
    
    if (errorMessage.includes('ECONNREFUSED')) {
      return {
        type: 'connection_refused',
        message: '服务器拒绝连接',
        suggestion: '服务器可能暂时不可用，请稍后重试'
      }
    }
    
    if (errorMessage.includes('timeout')) {
      return {
        type: 'timeout',
        message: '请求超时',
        suggestion: '网络较慢或服务器响应缓慢，请稍后重试'
      }
    }
    
    if (errorMessage.includes('fetch')) {
      return {
        type: 'fetch_error',
        message: '网络请求失败',
        suggestion: '请检查网络连接或稍后重试'
      }
    }
    
    return {
      type: 'unknown',
      message: '未知网络错误',
      suggestion: '请检查网络连接后重试'
    }
  }

  // 初始化
  onMounted(() => {
    checkNetworkStatus()
    setupNetworkListeners()
  })

  return {
    isOnline: readonly(isOnline),
    connectionType: readonly(connectionType),
    lastError: readonly(lastError),
    retryCount: readonly(retryCount),
    checkNetworkStatus,
    fetchWithRetry,
    diagnoseNetworkIssue
  }
}

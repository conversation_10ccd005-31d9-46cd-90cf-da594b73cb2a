// 播放器管理器 - 统一管理所有播放器实例
export const usePlayerManager = () => {
  // 全局播放器实例存储
  const players = new Map()
  
  // 注册播放器实例
  const registerPlayer = (id, player, type = 'unknown') => {
    if (players.has(id)) {
      console.warn(`播放器 ${id} 已存在，将被替换`)
      destroyPlayer(id)
    }
    
    players.set(id, {
      instance: player,
      type: type,
      createdAt: Date.now()
    })
    
    console.log(`播放器 ${id} (${type}) 已注册`)
  }
  
  // 销毁指定播放器
  const destroyPlayer = (id) => {
    const playerData = players.get(id)
    if (!playerData) {
      console.warn(`播放器 ${id} 不存在`)
      return
    }
    
    const { instance, type } = playerData
    
    try {
      console.log(`开始销毁播放器 ${id} (${type})`)
      
      // 根据播放器类型执行不同的清理逻辑
      switch (type) {
        case 'plyr':
          if (instance && typeof instance.destroy === 'function') {
            instance.destroy()
          }
          break
          
        case 'hls':
          if (instance && typeof instance.destroy === 'function') {
            instance.destroy()
          }
          break

        case 'flv':
          if (instance) {
            try {
              if (typeof instance.pause === 'function') {
                instance.pause()
              }
              if (typeof instance.unload === 'function') {
                instance.unload()
              }
              if (typeof instance.detachMediaElement === 'function') {
                instance.detachMediaElement()
              }
              if (typeof instance.destroy === 'function') {
                instance.destroy()
              }
            } catch (flvError) {
              console.warn('清理FLV播放器时出错:', flvError)
            }
          }
          break


          
        case 'video':
          if (instance) {
            try {
              instance.pause()
              instance.src = ''
              if (typeof instance.load === 'function') {
                instance.load()
              }
            } catch (videoError) {
              console.warn('清理视频元素时出错:', videoError)
            }
          }
          break
          
        default:
          // 通用清理逻辑
          if (instance && typeof instance.destroy === 'function') {
            instance.destroy()
          } else if (instance && typeof instance.dispose === 'function') {
            instance.dispose()
          }
          break
      }
      
      players.delete(id)
      console.log(`播放器 ${id} 已销毁`)
      
    } catch (error) {
      console.error(`销毁播放器 ${id} 时出错:`, error)
      // 即使出错也要从Map中删除
      players.delete(id)
    }
  }
  
  // 销毁所有播放器
  const destroyAllPlayers = () => {
    console.log('开始销毁所有播放器')
    const playerIds = Array.from(players.keys())
    
    playerIds.forEach(id => {
      destroyPlayer(id)
    })
    
    console.log('所有播放器已销毁')
  }
  
  // 获取播放器实例
  const getPlayer = (id) => {
    const playerData = players.get(id)
    return playerData ? playerData.instance : null
  }
  
  // 检查播放器是否存在
  const hasPlayer = (id) => {
    return players.has(id)
  }
  
  // 获取所有播放器信息
  const getAllPlayers = () => {
    const result = []
    players.forEach((data, id) => {
      result.push({
        id,
        type: data.type,
        createdAt: data.createdAt,
        age: Date.now() - data.createdAt
      })
    })
    return result
  }
  
  // 清理超时的播放器（可选功能）
  const cleanupOldPlayers = (maxAge = 5 * 60 * 1000) => { // 默认5分钟
    const now = Date.now()
    const toDestroy = []
    
    players.forEach((data, id) => {
      if (now - data.createdAt > maxAge) {
        toDestroy.push(id)
      }
    })
    
    toDestroy.forEach(id => {
      console.log(`清理超时播放器: ${id}`)
      destroyPlayer(id)
    })
  }
  
  // 安全的播放器创建函数
  const safeCreatePlayer = async (id, createFn, type = 'unknown') => {
    try {
      // 先清理可能存在的旧实例
      if (hasPlayer(id)) {
        destroyPlayer(id)
      }
      
      // 创建新实例
      const player = await createFn()
      
      if (player) {
        registerPlayer(id, player, type)
        return player
      } else {
        console.error(`创建播放器 ${id} 失败: 返回值为空`)
        return null
      }
    } catch (error) {
      console.error(`创建播放器 ${id} 时出错:`, error)
      return null
    }
  }
  
  return {
    registerPlayer,
    destroyPlayer,
    destroyAllPlayers,
    getPlayer,
    hasPlayer,
    getAllPlayers,
    cleanupOldPlayers,
    safeCreatePlayer
  }
}

// 全局播放器管理器实例
let globalPlayerManager = null

// 获取全局播放器管理器
export const getGlobalPlayerManager = () => {
  if (!globalPlayerManager) {
    globalPlayerManager = usePlayerManager()
    
    // 页面卸载时清理所有播放器
    if (process.client) {
      window.addEventListener('beforeunload', () => {
        globalPlayerManager.destroyAllPlayers()
      })
      
      // 定期清理超时播放器
      setInterval(() => {
        globalPlayerManager.cleanupOldPlayers()
      }, 60000) // 每分钟检查一次
    }
  }
  
  return globalPlayerManager
}

// 便捷的组合式函数
export const usePlayer = (playerId) => {
  const manager = getGlobalPlayerManager()
  
  // 组件卸载时自动清理
  onUnmounted(() => {
    if (manager.hasPlayer(playerId)) {
      manager.destroyPlayer(playerId)
    }
  })
  
  return {
    register: (player, type) => manager.registerPlayer(playerId, player, type),
    destroy: () => manager.destroyPlayer(playerId),
    get: () => manager.getPlayer(playerId),
    exists: () => manager.hasPlayer(playerId),
    safeCreate: (createFn, type) => manager.safeCreatePlayer(playerId, createFn, type)
  }
}

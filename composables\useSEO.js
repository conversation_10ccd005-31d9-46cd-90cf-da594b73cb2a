/**
 * SEO组合函数
 * 为前台页面提供统一的SEO管理功能
 */

import { 
  baseSEOConfig,
  generatePageTitle,
  generatePageDescription,
  generateKeywords,
  generateOpenGraph,
  generateTwitterCard,
  generateStructuredData,
  generateHreflang,
  pageSpecificSEO
} from '~/config/seo.js'

export const useSEO = () => {
  const { locale } = useI18n()
  const route = useRoute()
  const runtimeConfig = useRuntimeConfig()
  
  // 获取当前页面URL
  const getCurrentUrl = () => {
    const baseUrl = runtimeConfig.public.baseUrl || `https://${baseSEOConfig.domain}`
    return `${baseUrl}${route.fullPath}`
  }
  
  // 设置基础SEO
  const setBasicSEO = (options = {}) => {
    const {
      title,
      description,
      keywords,
      image,
      type = 'website',
      noindex = false,
      nofollow = false
    } = options
    
    const currentUrl = getCurrentUrl()
    const currentLocale = locale.value || 'zh-CN'
    
    // 生成meta标签
    const metaTags = [
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      { name: 'description', content: description || generatePageDescription(null, 'default', currentLocale) },
      { name: 'keywords', content: keywords || generateKeywords([], 'default', currentLocale) },
      { name: 'author', content: baseSEOConfig.author.name },
      { name: 'robots', content: `${noindex ? 'noindex' : 'index'},${nofollow ? 'nofollow' : 'follow'}` },
      { name: 'googlebot', content: `${noindex ? 'noindex' : 'index'},${nofollow ? 'nofollow' : 'follow'}` },
      { name: 'format-detection', content: 'telephone=no' },
      { name: 'theme-color', content: '#f97316' }, // orange-500
      { name: 'msapplication-TileColor', content: '#f97316' },
      { name: 'apple-mobile-web-app-capable', content: 'yes' },
      { name: 'apple-mobile-web-app-status-bar-style', content: 'default' },
      { name: 'apple-mobile-web-app-title', content: baseSEOConfig.siteName }
    ]
    
    // 添加Open Graph标签
    const ogTags = generateOpenGraph({
      title: title || baseSEOConfig.siteName,
      description: description || baseSEOConfig.siteDescription,
      image,
      url: currentUrl,
      type,
      locale: currentLocale.replace('-', '_')
    })
    
    Object.entries(ogTags).forEach(([property, content]) => {
      metaTags.push({ property, content })
    })
    
    // 添加Twitter Card标签
    const twitterTags = generateTwitterCard({
      title: title || baseSEOConfig.siteName,
      description: description || baseSEOConfig.siteDescription,
      image
    })
    
    Object.entries(twitterTags).forEach(([name, content]) => {
      metaTags.push({ name, content })
    })
    
    // 生成hreflang链接
    const hreflangs = generateHreflang(route.path)
    const linkTags = [
      { rel: 'canonical', href: currentUrl },
      { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
      { rel: 'apple-touch-icon', href: '/favicon.ico' },
      { rel: 'manifest', href: '/manifest.json' },
      ...hreflangs
    ]
    
    useHead({
      title: title || generatePageTitle('', currentLocale),
      meta: metaTags,
      link: linkTags
    })
  }
  
  // 设置结构化数据
  const setStructuredData = (data) => {
    if (!data || (Array.isArray(data) && data.length === 0)) return
    
    const structuredDataArray = Array.isArray(data) ? data : [data]
    
    structuredDataArray.forEach((item, index) => {
      useHead({
        script: [
          {
            type: 'application/ld+json',
            innerHTML: JSON.stringify(item),
            key: `structured-data-${index}`
          }
        ]
      })
    })
  }
  
  // 首页SEO
  const setHomeSEO = () => {
    const currentLocale = locale.value || 'zh-CN'
    const seoConfig = pageSpecificSEO.home(currentLocale)
    
    setBasicSEO({
      title: seoConfig.title,
      description: seoConfig.description,
      keywords: seoConfig.keywords,
      type: 'website'
    })
    
    if (seoConfig.structuredData) {
      setStructuredData(seoConfig.structuredData)
    }
  }
  
  // 分类页SEO
  const setCategorySEO = (categoryName, categoryData = {}) => {
    const currentLocale = locale.value || 'zh-CN'
    const seoConfig = pageSpecificSEO.category(categoryName, currentLocale)
    
    setBasicSEO({
      title: seoConfig.title,
      description: seoConfig.description,
      keywords: seoConfig.keywords,
      image: categoryData.image,
      type: 'website'
    })
    
    // 添加面包屑结构化数据
    const breadcrumbData = generateStructuredData('BreadcrumbList', {
      items: [
        { name: '首页', url: `https://${baseSEOConfig.domain}` },
        { name: '分类', url: `https://${baseSEOConfig.domain}/categories` },
        { name: categoryName, url: `https://${baseSEOConfig.domain}/categories/${categoryData.id || ''}` }
      ]
    })
    
    setStructuredData([breadcrumbData])
  }
  
  // 视频播放页SEO
  const setVideoSEO = (videoData) => {
    const currentLocale = locale.value || 'zh-CN'
    const seoConfig = pageSpecificSEO.video(videoData, currentLocale)
    
    setBasicSEO({
      title: seoConfig.title,
      description: seoConfig.description,
      keywords: seoConfig.keywords,
      image: seoConfig.image,
      type: 'video.movie'
    })
    
    if (seoConfig.structuredData) {
      setStructuredData(seoConfig.structuredData)
    }
    
    // 添加面包屑结构化数据
    const breadcrumbData = generateStructuredData('BreadcrumbList', {
      items: [
        { name: '首页', url: `https://${baseSEOConfig.domain}` },
        { name: videoData.categoryName || '视频', url: `https://${baseSEOConfig.domain}/categories/${videoData.categoryId || ''}` },
        { name: videoData.title, url: getCurrentUrl() }
      ]
    })
    
    setStructuredData([...seoConfig.structuredData, breadcrumbData])
  }
  
  // 搜索页SEO
  const setSearchSEO = (query, results = []) => {
    const currentLocale = locale.value || 'zh-CN'
    const seoConfig = pageSpecificSEO.search(query, currentLocale)
    
    setBasicSEO({
      title: seoConfig.title,
      description: seoConfig.description,
      keywords: seoConfig.keywords,
      type: 'website',
      noindex: results.length === 0 // 如果没有搜索结果，不索引页面
    })
  }
  
  // 排行榜页SEO
  const setRankingsSEO = () => {
    const currentLocale = locale.value || 'zh-CN'
    const seoConfig = pageSpecificSEO.rankings(currentLocale)
    
    setBasicSEO({
      title: seoConfig.title,
      description: seoConfig.description,
      keywords: seoConfig.keywords,
      type: 'website'
    })
  }
  
  // 设置自定义SEO
  const setCustomSEO = (options) => {
    setBasicSEO(options)
    
    if (options.structuredData) {
      setStructuredData(options.structuredData)
    }
  }
  
  return {
    setBasicSEO,
    setStructuredData,
    setHomeSEO,
    setCategorySEO,
    setVideoSEO,
    setSearchSEO,
    setRankingsSEO,
    setCustomSEO,
    getCurrentUrl,
    baseSEOConfig
  }
}

/**
 * 构建优化配置
 * 解决构建警告和性能问题
 */

// Vite构建优化配置
export const viteConfig = {
  server: {
    fs: {
      allow: ['..']
    }
  },
  build: {
    // 代码分割优化
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // 将大型第三方库分离到单独的chunk
          if (id.includes('node_modules')) {
            // Vue相关
            if (id.includes('vue') || id.includes('@vue')) {
              return 'vendor-vue'
            }
            // Nuxt相关
            if (id.includes('nuxt') || id.includes('@nuxt')) {
              return 'vendor-nuxt'
            }
            // Font Awesome
            if (id.includes('@fortawesome')) {
              return 'vendor-fontawesome'
            }
            // UI组件库
            if (id.includes('@headlessui') || id.includes('@heroicons')) {
              return 'vendor-ui'
            }
            // 工具库
            if (id.includes('moment') || id.includes('js-cookie') || id.includes('lodash')) {
              return 'vendor-utils'
            }
            // 其他第三方库
            return 'vendor-others'
          }
        }
      }
    },
    // 提高chunk大小警告阈值
    chunkSizeWarningLimit: 1000,
    // 启用CSS代码分割
    cssCodeSplit: true,
    // 压缩配置
    minify: true // 启用默认压缩
  },
  // CSS优化
  css: {
    devSourcemap: false // 生产环境禁用CSS源映射
  }
}

// Nitro优化配置
export const nitroConfig = {
  // 压缩配置
  compressPublicAssets: true,
  // 预渲染配置
  prerender: {
    crawlLinks: false, // 禁用自动爬取链接
    routes: ['/robots.txt', '/sitemap.xml'] // 只预渲染必要的路由
  }
}

// 构建环境检测
export const getBuildConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production'
  const isDevelopment = process.env.NODE_ENV === 'development'
  
  return {
    isProduction,
    isDevelopment,
    // 生产环境优化
    productionOptimizations: {
      minify: isProduction,
      sourcemap: !isProduction,
      extractCSS: isProduction
    }
  }
}

export default {
  viteConfig,
  nitroConfig,
  getBuildConfig
}

// 全局弹窗广告配置文件
export const popupConfig = {
  // 弹窗广告配置
  popupAd: {
    // 是否启用弹窗广告
    enabled: true,

    // 弹窗内容配置
    title: '🎉 特别推荐',
    content: '发现更多精彩内容，立即体验我们的最新功能！',
    imageUrl: '/images/popup-ad.svg', // 可以放在 public/images/ 目录下

    // 目标链接配置
    targetUrl: 'https://jspg7.com', // 点击弹窗任何地方都会打开这个链接
    openInNewWindow: true, // 是否在新窗口打开

    // 显示设置
    delayShow: 2000, // 延迟显示时间，单位：毫秒
    autoCloseDelay: 0, // 0表示不自动关闭，单位：毫秒

    // 显示频率控制
    showOnce: false, // 是否每次访问只显示一次
    showInterval: 1 * 60 * 1000, // 再次显示的间隔时间（毫秒），1分钟

    // 路由控制 - 在哪些页面显示弹窗
    showOnRoutes: [
      '/',           // 首页
      '/categories', // 分类页
      '/live',       // 直播页
      '/rankings',   // 排行榜
      '/search',     // 搜索页
      '/video'       // 视频详情页（会匹配 /video/xxx）
    ],

    // 排除的页面路由
    excludeRoutes: [
      '/admin',      // 管理后台
      '/login',      // 登录页
      '/popup-demo'  // 演示页面
    ],

    // 路由匹配模式
    routeMatchMode: 'startsWith', // 'exact' 精确匹配, 'startsWith' 前缀匹配, 'includes' 包含匹配
  },


}

// 获取弹窗广告配置
export const getPopupAdConfig = () => {
  return { ...popupConfig.popupAd }
}



// 检查是否应该在当前页面显示弹窗广告
export const shouldShowPopupAd = (currentPath) => {
  const config = popupConfig.popupAd

  if (!config.enabled) return false

  // 检查排除页面
  if (config.excludeRoutes.some(route => {
    switch (config.routeMatchMode) {
      case 'exact':
        return currentPath === route
      case 'includes':
        return currentPath.includes(route)
      case 'startsWith':
      default:
        return currentPath.startsWith(route)
    }
  })) {
    return false
  }

  // 检查指定页面
  if (config.showOnRoutes.length > 0) {
    return config.showOnRoutes.some(route => {
      switch (config.routeMatchMode) {
        case 'exact':
          return currentPath === route
        case 'includes':
          return currentPath.includes(route)
        case 'startsWith':
        default:
          return currentPath.startsWith(route)
      }
    })
  }

  return true
}

// 检查弹窗显示间隔
export const checkPopupInterval = () => {
  if (typeof window === 'undefined') return true

  const config = popupConfig.popupAd
  const lastShownTime = localStorage.getItem('popup-ad-last-shown')

  if (!lastShownTime) return true

  const timeDiff = Date.now() - parseInt(lastShownTime)
  return timeDiff >= config.showInterval
}

// 记录弹窗显示时间
export const recordPopupShown = () => {
  if (typeof window === 'undefined') return

  localStorage.setItem('popup-ad-last-shown', Date.now().toString())
}



// 更新弹窗配置（用于动态配置）
export const updatePopupConfig = (newConfig) => {
  Object.assign(popupConfig, newConfig)
}

export default popupConfig

/**
 * 企业级SEO配置文件
 * 专门为前台页面优化，不包含后台管理页面
 */

import { siteConfig } from './site.js'

// 基础SEO配置
export const baseSEOConfig = {
  // 网站基本信息
  siteName: siteConfig.basic.siteName,
  siteDescription: siteConfig.basic.siteDescription,
  domain: siteConfig.basic.domain,
  keywords: siteConfig.basic.keywords,
  
  // 社交媒体配置
  social: {
    twitter: '@91jspg',
    facebook: 'https://facebook.com/91jspg',
    instagram: 'https://instagram.com/91jspg'
  },
  
  // 默认图片
  defaultImage: '/images/og-default.jpg',
  
  // 作者信息
  author: {
    name: '91JSPG.COM',
    url: `https://${siteConfig.basic.domain}`
  }
}

// 动态生成页面标题
export const generatePageTitle = (pageTitle, locale = 'zh-CN') => {
  if (!pageTitle) return baseSEOConfig.siteName
  
  const separator = locale.startsWith('zh') ? ' - ' : ' | '
  return `${pageTitle}${separator}${baseSEOConfig.siteName}`
}

// 动态生成页面描述 - 专业SEO优化版本
export const generatePageDescription = (customDescription, pageType = 'default', locale = 'zh-CN') => {
  if (customDescription) return customDescription

  const descriptions = {
    'zh-CN': {
      home: `${baseSEOConfig.siteName} - 专业成人影视平台，免费高清AV在线观看，日韩欧美精品成人电影，每日更新最新AV资源，支持多设备播放，打造您的私密观影体验`,
      category: `${baseSEOConfig.siteName}成人影片分类大全 - 精选日本AV、韩国成人电影、欧美AV等各类型高清成人影片，免费在线观看，每日更新最新AV资源`,
      video: `免费高清AV在线观看 - ${baseSEOConfig.siteName}提供优质成人影片播放体验，支持多种清晰度选择，流畅播放不卡顿，享受极致私密观影乐趣`,
      search: `${baseSEOConfig.siteName}成人影片搜索 - 快速找到您想看的AV电影，支持演员、类型、地区等多维度搜索，海量成人影视资源一搜即达`,
      rankings: `${baseSEOConfig.siteName}热门AV排行榜 - 最新最热门的成人电影推荐，实时更新观看排行，发现优质AV内容，不错过任何精彩成人影片`,
      default: baseSEOConfig.siteDescription
    },
    'zh-TW': {
      home: `${baseSEOConfig.siteName} - 專業成人影視平台，免費高清AV線上觀看，日韓歐美精品成人電影，每日更新最新AV資源，支援多設備播放，打造您的私密觀影體驗`,
      category: `${baseSEOConfig.siteName}成人影片分類大全 - 精選日本AV、韓國成人電影、歐美AV等各類型高清成人影片，免費線上觀看，每日更新最新AV資源`,
      video: `免費高清AV線上觀看 - ${baseSEOConfig.siteName}提供優質成人影片播放體驗，支援多種清晰度選擇，流暢播放不卡頓，享受極致私密觀影樂趣`,
      search: `${baseSEOConfig.siteName}成人影片搜尋 - 快速找到您想看的AV電影，支援演員、類型、地區等多維度搜尋，海量成人影視資源一搜即達`,
      rankings: `${baseSEOConfig.siteName}熱門AV排行榜 - 最新最熱門的成人電影推薦，即時更新觀看排行，發現優質AV內容，不錯過任何精彩成人影片`,
      default: baseSEOConfig.siteDescription
    },
    'en-US': {
      home: `${baseSEOConfig.siteName} - Premium Adult Entertainment Platform, Free HD AV Online Streaming, Japanese Korean European Adult Movies, Daily Updates, Multi-Device Support for Ultimate Private Viewing Experience`,
      category: `${baseSEOConfig.siteName} Adult Video Categories - Premium Japanese AV, Korean Adult Movies, European Adult Films, Free HD Adult Content Streaming, Daily Updated AV Resources`,
      video: `Free HD Adult Video Streaming - ${baseSEOConfig.siteName} Premium Adult Entertainment Experience, Multiple Quality Options, Smooth Playback, Ultimate Private Viewing Pleasure`,
      search: `${baseSEOConfig.siteName} Adult Video Search - Find Your Favorite AV Movies, Search by Actress, Genre, Region, Massive Adult Content Database`,
      rankings: `${baseSEOConfig.siteName} Top AV Rankings - Latest Hottest Adult Movies, Real-time Viewing Rankings, Discover Premium AV Content, Never Miss Amazing Adult Films`,
      default: 'Premium Adult Entertainment Platform - Free HD AV Streaming'
    },
    'ja-JP': {
      home: `${baseSEOConfig.siteName} - プレミアムアダルトエンターテイメントプラットフォーム、無料高画質AV動画オンライン視聴、日本・韓国・欧米のアダルト映画、毎日更新、マルチデバイス対応でプライベート視聴体験`,
      category: `${baseSEOConfig.siteName}アダルト動画カテゴリ大全 - 厳選された日本AV、韓国アダルト映画、欧米AVなど各種高画質アダルト作品、無料オンライン視聴、毎日最新AV資源更新`,
      video: `無料高画質AV動画オンライン視聴 - ${baseSEOConfig.siteName}優質アダルト作品再生体験、複数画質選択対応、スムーズ再生、極上プライベート視聴楽しみ`,
      search: `${baseSEOConfig.siteName}アダルト作品検索 - お気に入りのAV映画を素早く検索、女優・ジャンル・地域など多次元検索、海量アダルト映像資源一発検索`,
      rankings: `${baseSEOConfig.siteName}人気AVランキング - 最新最人気アダルト映画おすすめ、リアルタイム視聴ランキング更新、優質AV内容発見、素晴らしいアダルト作品見逃しなし`,
      default: 'プレミアムアダルトエンターテイメントプラットフォーム - 無料高画質AVストリーミング'
    }
  }
  
  return descriptions[locale]?.[pageType] || descriptions['zh-CN'][pageType] || descriptions['zh-CN'].default
}

// 生成关键词
export const generateKeywords = (customKeywords = [], pageType = 'default', locale = 'zh-CN') => {
  const baseKeywords = baseSEOConfig.keywords.split(',').map(k => k.trim())
  
  const typeKeywords = {
    'zh-CN': {
      home: ['AV在线观看', '成人电影', '高清AV', '免费AV', '日本AV', '韩国AV', '欧美AV', '成人影片', 'AV网站', '成人视频', 'AV资源', '在线AV', '高清成人', '免费成人电影', '成人在线', 'AV影院', '成人娱乐', 'AV下载', '最新AV', '热门AV'],
      category: ['AV分类', '成人影片分类', 'AV类型', '成人电影类型', '地区分类', '演员分类', '题材分类', '年份分类', '评分分类', '时长分类'],
      video: ['AV在线播放', '高清成人视频', '免费AV观看', '成人影片播放', '无码AV', '中文字幕', '高清无码', '在线播放', '流畅播放', '多清晰度'],
      search: ['AV搜索', '成人影片搜索', '演员搜索', 'AV资源搜索', '关键词搜索', '标题搜索', '导演搜索', '制作商搜索'],
      rankings: ['AV排行榜', '热门成人电影', '推荐AV', '最新AV', '人气AV', '评分排行', '观看排行', '下载排行', '收藏排行', '评论排行']
    },
    'zh-TW': {
      home: ['AV線上觀看', '成人電影', '高清AV', '免費AV', '日本AV', '韓國AV', '歐美AV', '成人影片', 'AV網站', '成人視頻', 'AV資源', '線上AV', '高清成人', '免費成人電影', '成人線上', 'AV影院', '成人娛樂', 'AV下載', '最新AV', '熱門AV'],
      category: ['AV分類', '成人影片分類', 'AV類型', '成人電影類型', '地區分類', '演員分類', '題材分類', '年份分類', '評分分類', '時長分類'],
      video: ['AV線上播放', '高清成人視頻', '免費AV觀看', '成人影片播放', '無碼AV', '中文字幕', '高清無碼', '線上播放', '流暢播放', '多清晰度'],
      search: ['AV搜尋', '成人影片搜尋', '演員搜尋', 'AV資源搜尋', '關鍵詞搜尋', '標題搜尋', '導演搜尋', '製作商搜尋'],
      rankings: ['AV排行榜', '熱門成人電影', '推薦AV', '最新AV', '人氣AV', '評分排行', '觀看排行', '下載排行', '收藏排行', '評論排行']
    },
    'en-US': {
      home: ['AV online', 'adult movies', 'free AV', 'HD adult videos', 'Japanese AV', 'Korean AV', 'European AV', 'adult films', 'AV website', 'adult videos', 'AV resources', 'online AV', 'HD adult', 'free adult movies', 'adult online', 'AV cinema', 'adult entertainment', 'AV download', 'latest AV', 'popular AV'],
      category: ['AV category', 'adult genres', 'porn types', 'adult film types', 'regional category', 'actress category', 'theme category', 'year category', 'rating category', 'duration category'],
      video: ['AV player', 'HD adult video', 'free adult watch', 'porn streaming', 'uncensored AV', 'subtitled', 'HD uncensored', 'online streaming', 'smooth playback', 'multiple quality'],
      search: ['AV search', 'adult movie search', 'porn search', 'actress search', 'keyword search', 'title search', 'director search', 'studio search'],
      rankings: ['AV rankings', 'popular adult movies', 'top AV', 'trending adult', 'latest AV', 'rating rankings', 'view rankings', 'download rankings', 'favorite rankings', 'comment rankings']
    },
    'ja-JP': {
      home: ['AV動画', '無料AV', 'アダルト動画', '高画質AV', 'エロ動画', '日本AV', '韓国AV', '欧米AV', 'アダルト映画', 'AVサイト', 'アダルトビデオ', 'AV資源', 'オンラインAV', '高画質アダルト', '無料アダルト映画', 'アダルトオンライン', 'AV映画館', 'アダルトエンターテイメント', 'AVダウンロード', '最新AV', '人気AV'],
      category: ['AV分類', 'ジャンル', 'カテゴリー', '女優別', 'アダルトジャンル', 'エロタイプ', 'アダルト映画タイプ', '地域別カテゴリー', '女優カテゴリー', 'テーマカテゴリー', '年別カテゴリー', '評価カテゴリー', '時間カテゴリー'],
      video: ['AV再生', '高画質', '無料視聴', 'ストリーミング', 'AV プレーヤー', '高画質アダルトビデオ', '無料アダルト視聴', 'エロストリーミング', '無修正AV', '字幕付き', '高画質無修正', 'オンラインストリーミング', 'スムーズ再生', '複数画質'],
      search: ['AV検索', '女優検索', '作品検索', 'タイトル検索', 'アダルト映画検索', 'エロ検索', 'キーワード検索', '監督検索', 'スタジオ検索'],
      rankings: ['人気AV', 'ランキング', 'おすすめ', '新作AV', 'AVランキング', '人気アダルト映画', 'トップAV', 'トレンドアダルト', '最新AV', '評価ランキング', '視聴ランキング', 'ダウンロードランキング', 'お気に入りランキング', 'コメントランキング']
    }
  }
  
  const pageKeywords = typeKeywords[locale]?.[pageType] || []
  const allKeywords = [...baseKeywords, ...pageKeywords, ...customKeywords]
  
  return [...new Set(allKeywords)].join(', ')
}

// Open Graph配置生成器
export const generateOpenGraph = (options = {}) => {
  const {
    title,
    description,
    image = baseSEOConfig.defaultImage,
    url,
    type = 'website',
    locale = 'zh_CN'
  } = options
  
  return {
    'og:site_name': baseSEOConfig.siteName,
    'og:title': title || baseSEOConfig.siteName,
    'og:description': description || baseSEOConfig.siteDescription,
    'og:image': image.startsWith('http') ? image : `https://${baseSEOConfig.domain}${image}`,
    'og:url': url || `https://${baseSEOConfig.domain}`,
    'og:type': type,
    'og:locale': locale,
    'og:locale:alternate': ['zh_CN', 'zh_TW', 'en_US', 'ja_JP'].filter(l => l !== locale)
  }
}

// Twitter Card配置生成器
export const generateTwitterCard = (options = {}) => {
  const {
    title,
    description,
    image = baseSEOConfig.defaultImage,
    card = 'summary_large_image'
  } = options
  
  return {
    'twitter:card': card,
    'twitter:site': baseSEOConfig.social.twitter,
    'twitter:title': title || baseSEOConfig.siteName,
    'twitter:description': description || baseSEOConfig.siteDescription,
    'twitter:image': image.startsWith('http') ? image : `https://${baseSEOConfig.domain}${image}`
  }
}

// 结构化数据模板
export const generateStructuredData = (type, data = {}) => {
  const baseData = {
    '@context': 'https://schema.org',
    '@type': type
  }
  
  switch (type) {
    case 'WebSite':
      return {
        ...baseData,
        name: baseSEOConfig.siteName,
        description: baseSEOConfig.siteDescription,
        url: `https://${baseSEOConfig.domain}`,
        potentialAction: {
          '@type': 'SearchAction',
          target: `https://${baseSEOConfig.domain}/search?q={search_term_string}`,
          'query-input': 'required name=search_term_string'
        },
        publisher: {
          '@type': 'Organization',
          name: baseSEOConfig.siteName,
          url: `https://${baseSEOConfig.domain}`
        }
      }
      
    case 'VideoObject':
      return {
        ...baseData,
        name: data.title,
        description: data.description,
        thumbnailUrl: data.thumbnail,
        uploadDate: data.uploadDate,
        duration: data.duration,
        contentUrl: data.contentUrl,
        embedUrl: data.embedUrl,
        publisher: {
          '@type': 'Organization',
          name: baseSEOConfig.siteName,
          url: `https://${baseSEOConfig.domain}`
        }
      }
      
    case 'BreadcrumbList':
      return {
        ...baseData,
        itemListElement: data.items?.map((item, index) => ({
          '@type': 'ListItem',
          position: index + 1,
          name: item.name,
          item: item.url
        })) || []
      }
      
    case 'Organization':
      return {
        ...baseData,
        name: baseSEOConfig.siteName,
        url: `https://${baseSEOConfig.domain}`,
        logo: `https://${baseSEOConfig.domain}/favicon.ico`,
        sameAs: Object.values(baseSEOConfig.social).filter(Boolean)
      }
      
    default:
      return baseData
  }
}

// 多语言hreflang生成器
export const generateHreflang = (currentPath, availableLocales = ['zh-CN', 'zh-TW', 'en-US', 'ja-JP']) => {
  const hreflangs = []
  
  availableLocales.forEach(locale => {
    const localeCode = locale.replace('-', '_').toLowerCase()
    const path = locale === 'zh-CN' ? currentPath : `/${locale.toLowerCase()}${currentPath}`
    
    hreflangs.push({
      rel: 'alternate',
      hreflang: localeCode,
      href: `https://${baseSEOConfig.domain}${path}`
    })
  })
  
  // 添加默认语言
  hreflangs.push({
    rel: 'alternate',
    hreflang: 'x-default',
    href: `https://${baseSEOConfig.domain}${currentPath}`
  })
  
  return hreflangs
}

// 页面特定SEO配置
export const pageSpecificSEO = {
  // 首页SEO
  home: (locale = 'zh-CN') => ({
    title: generatePageTitle('', locale),
    description: generatePageDescription(null, 'home', locale),
    keywords: generateKeywords([], 'home', locale),
    structuredData: [
      generateStructuredData('WebSite'),
      generateStructuredData('Organization')
    ]
  }),

  // 分类页SEO
  category: (categoryName, locale = 'zh-CN') => ({
    title: generatePageTitle(categoryName, locale),
    description: generatePageDescription(`浏览${categoryName}分类的精彩影片`, 'category', locale),
    keywords: generateKeywords([categoryName], 'category', locale)
  }),

  // 视频播放页SEO
  video: (videoData, locale = 'zh-CN') => ({
    title: generatePageTitle(videoData.title, locale),
    description: generatePageDescription(videoData.description, 'video', locale),
    keywords: generateKeywords(videoData.tags || [], 'video', locale),
    image: videoData.thumbnail,
    structuredData: [
      generateStructuredData('VideoObject', {
        title: videoData.title,
        description: videoData.description,
        thumbnail: videoData.thumbnail,
        uploadDate: videoData.uploadDate,
        duration: videoData.duration,
        contentUrl: videoData.contentUrl,
        embedUrl: videoData.embedUrl
      })
    ]
  }),

  // 搜索页SEO
  search: (query, locale = 'zh-CN') => ({
    title: generatePageTitle(`搜索: ${query}`, locale),
    description: generatePageDescription(`搜索"${query}"的相关影片结果`, 'search', locale),
    keywords: generateKeywords([query], 'search', locale)
  }),

  // 排行榜页SEO
  rankings: (locale = 'zh-CN') => ({
    title: generatePageTitle('热门排行榜', locale),
    description: generatePageDescription(null, 'rankings', locale),
    keywords: generateKeywords([], 'rankings', locale)
  }),

  // 采集API页面SEO
  collect: (locale = 'zh-CN') => {
    const isZh = locale.startsWith('zh')
    return {
      title: generatePageTitle(isZh ? '采集API' : 'Collection API', locale),
      description: isZh
        ? '91JSPG.COM提供专业的视频资源采集API服务，兼容Apple CMS格式，支持JSON和XML输出，无频率限制，免费使用。为影视网站提供稳定可靠的数据采集解决方案。'
        : '91JSPG.COM provides professional video resource collection API service, compatible with Apple CMS format, supports JSON and XML output, no rate limit, free to use.',
      keywords: generateKeywords(
        isZh
          ? ['采集API', '视频采集', 'Apple CMS', 'JSON API', 'XML API', '影视资源', '数据采集', '免费API']
          : ['collection API', 'video collection', 'Apple CMS', 'JSON API', 'XML API', 'video resources', 'data collection', 'free API'],
        'collect',
        locale
      ),
      structuredData: [
        generateStructuredData('WebAPI', {
          name: isZh ? '91JSPG视频采集API' : '91JSPG Video Collection API',
          description: isZh
            ? '专业的视频资源采集API，兼容Apple CMS格式'
            : 'Professional video resource collection API, compatible with Apple CMS format',
          url: `https://${baseSEOConfig.domain}/collect`,
          provider: {
            '@type': 'Organization',
            name: '91JSPG.COM',
            url: `https://${baseSEOConfig.domain}`
          }
        })
      ]
    }
  }
}

export default {
  baseSEOConfig,
  generatePageTitle,
  generatePageDescription,
  generateKeywords,
  generateOpenGraph,
  generateTwitterCard,
  generateStructuredData,
  generateHreflang,
  pageSpecificSEO
}

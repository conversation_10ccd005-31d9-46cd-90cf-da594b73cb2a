/**
 * 网站配置文件
 * 在这里手动修改网站的基本信息和显示设置
 */

export const siteConfig = {
  // 基本设置
  basic: {
    // 网站名称
    siteName: '91JSPG.COM',

    // 网站描述 - 成人影视专业SEO版本
    siteDescription: '91JSPG - 专业成人影视平台，提供高清AV在线观看，日韩欧美精品影片，免费观看最新成人电影，支持多设备播放，每日更新优质内容',

    // 联系邮箱
    contactEmail: '<EMAIL>',

    // 网站状态 (online/maintenance/offline)
    siteStatus: 'online',

    // 版权信息
    copyright: '© 2024 91JSPG.COM. 保留所有权利.',

    // 成人影视专业SEO关键词 - 高搜索量关键词
    keywords: 'AV在线观看,成人电影,高清AV,免费AV,日本AV,韩国AV,欧美AV,成人影片,AV网站,成人视频,AV资源,在线AV,高清成人,免费成人电影,成人在线,AV影院,成人娱乐,AV下载,最新AV,热门AV',

    // 网站域名
    domain: '91jspg.com',

    // SEO增强配置
    seo: {
      // 主要目标关键词 (高搜索量成人内容)
      primaryKeywords: [
        'AV在线观看', '成人电影', '高清AV', '免费AV', '日本AV'
      ],

      // 长尾关键词 (精准流量)
      longTailKeywords: [
        '免费高清AV在线观看', '最新成人电影免费观看', '日本AV高清在线',
        '韩国成人电影在线', '欧美AV免费观看', '成人影片在线播放',
        '高清成人视频下载', '最新AV资源在线', '免费成人娱乐网站',
        'AV影院免费观看', '成人电影大全在线', '高清无码AV观看'
      ],

      // 地域相关关键词
      regionalKeywords: [
        '日本AV', '韩国AV', '欧美AV', '国产AV', '亚洲AV', '中文AV'
      ],

      // 品牌相关关键词
      brandKeywords: [
        '91JSPG', '91JSPG成人网', '91JSPG AV', '91JSPG在线观看'
      ],

      // 竞争对手关键词 (合法替代)
      competitorKeywords: [
        'AV网站', '成人网站', '在线AV', '免费成人', '成人平台', 'AV资源站'
      ],

      // 技术相关关键词
      techKeywords: [
        '高清', '无码', '中文字幕', '在线播放', '免费下载', '手机观看'
      ],

      // 多语言关键词配置
      multiLanguageKeywords: {
        'zh-TW': {
          primary: ['AV線上觀看', '成人電影', '高清AV', '免費AV', '日本AV'],
          secondary: ['韓國AV', '歐美AV', '成人影片', 'AV網站', '成人視頻', 'AV資源', '線上AV', '高清成人', '免費成人電影', '成人線上']
        },
        'en-US': {
          primary: ['AV online', 'adult movies', 'free AV', 'HD adult videos', 'Japanese AV'],
          secondary: ['Korean AV', 'European AV', 'adult films', 'AV website', 'adult videos', 'AV resources', 'online AV', 'HD adult', 'free adult movies', 'adult entertainment']
        },
        'ja-JP': {
          primary: ['AV動画', '無料AV', 'アダルト動画', '高画質AV', 'エロ動画'],
          secondary: ['日本AV', '韓国AV', '欧米AV', 'アダルト映画', 'AVサイト', 'アダルトビデオ', 'AV資源', 'オンラインAV', '高画質アダルト', '無料アダルト映画']
        }
      }
    }
  },
  
  // 显示设置
  display: {
    // 每页显示影片数量
    videosPerPage: 24,
    
    // 主题色彩 (orange/red/blue/green/purple)
    themeColor: 'orange',
    
    // 是否显示观看次数
    showViewCount: true,
    
    // 是否显示影片时长
    showDuration: true,
    
    // 是否显示标签
    showTags: true,
    
    // 是否显示评分
    showRating: true,
    
    // 是否显示发布日期
    showReleaseDate: true
  },
  
  // 功能设置
  features: {
    // 是否启用搜索功能
    enableSearch: true,
    
    // 是否启用分类筛选
    enableCategoryFilter: true,
    
    // 是否启用排序功能
    enableSorting: true,
    
    // 是否启用收藏功能
    enableFavorites: true,
    
    // 是否启用评论功能
    enableComments: false,
    
    // 是否启用用户注册
    enableUserRegistration: false
  },
  
  // 服务器端口配置
  server: {
    // 用户前端端口（可通过环境变量覆盖）
    userPort: parseInt(process.env.USER_PORT) || 3000,

    // 管理后台端口（可通过环境变量覆盖）
    adminPort: parseInt(process.env.ADMIN_PORT) || 3002,

    // 后端API端口
    apiPort: parseInt(process.env.API_PORT) || 3001,

    // 服务器主机地址
    host: process.env.HOST || '0.0.0.0'
  },

  // API 配置
  api: {
    // 开发环境
    development: 'http://localhost:3001',

    // 生产环境 - 用户前端 API（可通过环境变量覆盖）
    production: process.env.API_URL || 'https://api.91jspg.com',

    // 生产环境 - 管理后台 API（可通过环境变量覆盖）
    admin: process.env.ADMIN_API_URL || 'https://api.91jspg.com',

    // 测试环境
    staging: 'https://staging-api.yourdomain.com'
  },

  // 社交媒体链接
  social: {
    // Twitter
    twitter: '',

    // Facebook
    facebook: '',

    // Instagram
    instagram: '',

    // Telegram
    telegram: '',

    // Discord
    discord: ''
  },
  
  // 广告设置
  ads: {
    // 是否启用广告
    enableAds: true,

    // 首页横幅广告
    homeBanner: {
      enabled: true,
      title: '🎬 91JSPG.COM',
      subtitle: '免費高清AV在線看 | 每日更新最新內容',
      features: ['✨ 高清畫質', '🚀 極速播放', '📱 多端適配']
    },

    // 侧边栏广告
    sidebar: {
      enabled: true
    }
  },

  // 统计分析配置
  analytics: {
    // 是否启用统计
    enabled: true,

    // Google Analytics 配置
    googleAnalytics: {
      enabled: false,
      measurementId: '', // 填入你的 GA4 测量ID，如: 'G-XXXXXXXXXX'
      config: {
        // GA4 配置选项
        send_page_view: true,
        anonymize_ip: true,
        allow_google_signals: false,
        allow_ad_personalization_signals: false
      }
    },

    // 百度统计配置
    baiduAnalytics: {
      enabled: false,
      siteId: '' // 填入你的百度统计站点ID
    },

    // 友盟统计配置
    umengAnalytics: {
      enabled: false,
      siteId: '', // 填入你的友盟站点ID
      autoPageview: true
    },

    // 51LA统计配置
    la51Analytics: {
      enabled: false,
      siteId: '', // 填入你的51LA站点ID
      autoPageview: true
    },

    // 自定义统计代码
    customAnalytics: {
      enabled: false,
      // 头部统计代码 (放在 <head> 中)
      headScript: '',
      // 页面底部统计代码 (放在 </body> 前)
      bodyScript: '',
      // 页面加载完成后执行的代码
      onPageLoad: ''
    },

    // 统计事件配置
    events: {
      // 是否统计视频播放事件
      trackVideoPlay: true,
      // 是否统计搜索事件
      trackSearch: true,
      // 是否统计下载事件
      trackDownload: true,
      // 是否统计分享事件
      trackShare: true,
      // 是否统计用户停留时间
      trackTimeOnPage: true
    },

    // 隐私设置
    privacy: {
      // 是否启用Cookie同意
      cookieConsent: false,
      // 是否匿名化IP
      anonymizeIp: true,
      // 数据保留时间（月）
      dataRetention: 14
    }
  }
}

// 获取主题色彩的 CSS 类名
export const getThemeColorClass = (type = 'bg') => {
  const color = siteConfig.display.themeColor
  const colorMap = {
    orange: {
      bg: 'bg-orange-500',
      text: 'text-orange-500',
      border: 'border-orange-500',
      gradient: 'from-orange-500'
    },
    red: {
      bg: 'bg-red-500',
      text: 'text-red-500',
      border: 'border-red-500',
      gradient: 'from-red-500'
    },
    blue: {
      bg: 'bg-blue-500',
      text: 'text-blue-500',
      border: 'border-blue-500',
      gradient: 'from-blue-500'
    },
    green: {
      bg: 'bg-green-500',
      text: 'text-green-500',
      border: 'border-green-500',
      gradient: 'from-green-500'
    },
    purple: {
      bg: 'bg-purple-500',
      text: 'text-purple-500',
      border: 'border-purple-500',
      gradient: 'from-purple-500'
    }
  }
  
  return colorMap[color]?.[type] || colorMap.orange[type]
}

// 获取完整的主题渐变类名
export const getThemeGradientClass = () => {
  return `bg-gradient-to-r ${getThemeColorClass('gradient')} to-red-500`
}

// 获取服务器端口
export const getServerPort = (type = 'user') => {
  switch (type) {
    case 'admin':
      return siteConfig.server.adminPort
    case 'api':
      return siteConfig.server.apiPort
    case 'user':
    default:
      return siteConfig.server.userPort
  }
}

// 获取服务器主机地址
export const getServerHost = () => {
  return siteConfig.server.host
}

// 获取完整的服务器地址
export const getServerUrl = (type = 'user') => {
  const host = getServerHost()
  const port = getServerPort(type)
  const protocol = 'http' // 可以根据需要改为 https

  return `${protocol}://${host}:${port}`
}

// 获取 API 基础 URL
export const getApiBaseUrl = (type = 'user') => {
  const isDev = process.env.NODE_ENV === 'development'

  if (isDev) {
    return siteConfig.api.development
  }

  // 生产环境根据类型返回不同的 API 地址
  switch (type) {
    case 'admin':
      return siteConfig.api.admin
    case 'user':
    default:
      return siteConfig.api.production
  }
}

// 创建 API 请求配置
export const createApiConfig = (type = 'user') => {
  return {
    baseURL: getApiBaseUrl(type),
    headers: {
      'Content-Type': 'application/json'
    },
    // 超时配置
    timeout: 30000, // 30秒超时

    // 重试配置
    retry: 2, // 重试2次
    retryDelay: 1000, // 重试延迟1秒

    // 连接配置
    keepAlive: true,

    // 错误处理
    onRequestError({ error }) {
      console.warn('API请求错误:', error.message)
    },

    onResponseError({ response }) {
      console.warn('API响应错误:', response.status, response.statusText)
    }
  }
}

export default siteConfig

# 统计分析配置指南

## 📊 统计工具配置

### 1. 在 `config/site.js` 中配置统计工具

```javascript
analytics: {
  // 是否启用统计
  enabled: true,
  
  // Google Analytics 配置
  googleAnalytics: {
    enabled: true,
    measurementId: 'G-XXXXXXXXXX', // 替换为你的GA4测量ID
    config: {
      send_page_view: true,
      anonymize_ip: true,
      allow_google_signals: false,
      allow_ad_personalization_signals: false
    }
  },
  
  // 百度统计配置
  baiduAnalytics: {
    enabled: true,
    siteId: 'xxxxxxxxxxxxxxxx' // 替换为你的百度统计站点ID
  },
  
  // 友盟统计配置
  umengAnalytics: {
    enabled: true,
    siteId: 'xxxxxxxx', // 替换为你的友盟站点ID
    autoPageview: true
  },
  
  // 51LA统计配置
  la51Analytics: {
    enabled: true,
    siteId: 'xxxxxxxx', // 替换为你的51LA站点ID
    autoPageview: true
  },
  
  // 自定义统计代码
  customAnalytics: {
    enabled: true,
    // 头部统计代码
    headScript: `
      // 你的自定义头部统计代码
      console.log('自定义统计代码已加载');
    `,
    // 页面底部统计代码
    bodyScript: `
      // 你的自定义底部统计代码
    `,
    // 页面加载完成后执行
    onPageLoad: `
      // 页面加载完成后的统计代码
      console.log('页面加载完成');
    `
  }
}
```

### 2. 统计事件配置

```javascript
events: {
  // 是否统计视频播放事件
  trackVideoPlay: true,
  // 是否统计搜索事件
  trackSearch: true,
  // 是否统计下载事件
  trackDownload: true,
  // 是否统计分享事件
  trackShare: true,
  // 是否统计用户停留时间
  trackTimeOnPage: true
}
```

## 🎯 如何在页面中使用统计

### 1. 自动统计（无需额外代码）

- **页面浏览**: 自动统计所有页面访问
- **路由跳转**: 自动统计页面切换

### 2. 手动统计事件

在任何页面组件中使用：

```vue
<script setup>
// 注入统计方法
const analytics = inject('analytics')

// 统计搜索事件
const handleSearch = (query) => {
  analytics.trackSearch(query, searchResults.length)
}

// 统计视频播放事件
const handleVideoPlay = (videoId, videoTitle) => {
  analytics.trackVideoPlay(videoId, videoTitle)
}

// 统计下载事件
const handleDownload = (fileUrl, fileName) => {
  analytics.trackDownload(fileUrl, fileName)
}

// 统计分享事件
const handleShare = (contentType, contentId, platform) => {
  analytics.trackShare(contentType, contentId, platform)
}
</script>

<template>
  <div>
    <!-- 搜索按钮 -->
    <button @click="handleSearch('关键词')">搜索</button>
    
    <!-- 播放按钮 -->
    <button @click="handleVideoPlay('video123', '电影标题')">播放</button>
    
    <!-- 下载按钮 -->
    <button @click="handleDownload('/file.mp4', '电影.mp4')">下载</button>
    
    <!-- 分享按钮 -->
    <button @click="handleShare('video', 'video123', 'wechat')">分享</button>
  </div>
</template>
```

## 🔧 常用统计工具配置示例

### Google Analytics 4 (GA4)

1. 访问 [Google Analytics](https://analytics.google.com/)
2. 创建新的GA4属性
3. 获取测量ID (格式: G-XXXXXXXXXX)
4. 在配置中启用并填入测量ID

### 百度统计

1. 访问 [百度统计](https://tongji.baidu.com/)
2. 添加网站
3. 获取统计代码中的站点ID
4. 在配置中启用并填入站点ID

### 友盟统计

1. 访问 [友盟+](https://www.umeng.com/)
2. 创建网站应用
3. 获取站点ID
4. 在配置中启用并填入站点ID

### 51LA统计

1. 访问 [51LA](https://www.51.la/)
2. 添加网站
3. 获取站点ID
4. 在配置中启用并填入站点ID

## 📈 统计数据说明

### 自动统计的数据

- **页面浏览量 (PV)**: 每次页面加载
- **独立访客 (UV)**: 基于IP和设备识别
- **访问时长**: 用户在页面停留时间
- **跳出率**: 只访问一个页面就离开的比例
- **来源分析**: 用户从哪里访问网站

### 自定义事件统计

- **视频播放**: 统计哪些视频被播放
- **搜索行为**: 统计用户搜索的关键词
- **下载行为**: 统计文件下载情况
- **分享行为**: 统计内容分享情况

## ⚠️ 注意事项

1. **隐私合规**: 确保符合当地隐私法规
2. **性能影响**: 过多统计代码可能影响页面加载速度
3. **数据准确性**: 避免重复统计同一事件
4. **跨域问题**: 某些统计工具可能有跨域限制

## 🚀 高级配置

### 自定义统计维度

```javascript
// 在统计事件中添加自定义维度
analytics.trackVideoPlay(videoId, videoTitle, {
  category: '成人影片',
  quality: '高清',
  duration: '120分钟',
  actor: '演员名称'
})
```

### 条件统计

```javascript
// 只在生产环境启用统计
analytics: {
  enabled: process.env.NODE_ENV === 'production',
  // ... 其他配置
}
```

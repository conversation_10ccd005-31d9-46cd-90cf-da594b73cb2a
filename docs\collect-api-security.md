# 采集接口安全配置指南

## 🎯 配置目标

为采集接口创建一个既**安全**又**友好**的Nginx配置，满足以下需求：

1. ✅ **采集接口开放访问** - 允许其他网站调用
2. ✅ **防止恶意攻击** - 限流、防DDoS
3. ✅ **前台页面安全** - 严格的安全策略
4. ✅ **性能优化** - 缓存、压缩、负载均衡

## 🔧 核心配置特点

### 1. 分层安全策略

```nginx
# 采集接口 (/api/collect/) - 开放但受控
location /api/collect/ {
    # 允许跨域，但有限流保护
}

# 前台页面 (/) - 严格安全
location / {
    # 完整的安全头配置
}
```

### 2. 智能限流保护

```nginx
# 三层限流保护
limit_req_zone $binary_remote_addr zone=ip_limit:10m rate=10r/s;     # IP限流
limit_req_zone $request_uri zone=api_limit:10m rate=5r/s;            # API限流  
limit_conn_zone $binary_remote_addr zone=conn_limit:10m;             # 连接限制
```

### 3. CORS跨域支持

```nginx
# 完整的CORS配置
add_header Access-Control-Allow-Origin "*" always;
add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, User-Agent" always;
```

## 📊 安全级别对比

| 功能 | 采集接口 | 前台页面 | 说明 |
|------|----------|----------|------|
| **跨域访问** | ✅ 允许 | ❌ 禁止 | 采集需要被第三方调用 |
| **X-Frame-Options** | SAMEORIGIN | DENY | 采集允许同源嵌入 |
| **CSP策略** | 宽松 | 严格 | 采集不能限制太严 |
| **缓存策略** | 5分钟 | 动态 | 采集适度缓存减压 |
| **限流策略** | 中等 | 宽松 | 采集需要防刷 |
| **HTTPS强制** | ✅ 是 | ✅ 是 | 全站HTTPS |

## 🛡️ 安全防护措施

### 1. 多层限流防护

```nginx
# 第一层：IP级别限流
limit_req zone=ip_limit burst=100 nodelay;

# 第二层：API级别限流  
limit_req zone=api_limit burst=20 nodelay;

# 第三层：连接数限制
limit_conn conn_limit 20;
```

**效果**：
- 单个IP每秒最多10个请求
- 单个API每秒最多5个请求
- 单个IP最多20个并发连接

### 2. 智能缓存策略

```nginx
# 采集接口缓存
add_header Cache-Control "public, max-age=300, s-maxage=300" always;  # 5分钟

# 静态资源缓存
expires 1y;
add_header Cache-Control "public, immutable";
```

**效果**：
- 减轻服务器压力
- 提高响应速度
- 降低带宽消耗

### 3. 错误处理和监控

```nginx
# 健康检查
location /health {
    return 200 "healthy\n";
}

# 错误页面
error_page 404 /404.html;
error_page 500 502 503 504 /50x.html;

# 日志记录
access_log /var/log/nginx/91jspg_access.log;
error_log /var/log/nginx/91jspg_error.log;
```

## 🚀 部署步骤

### 1. 备份现有配置

```bash
sudo cp /etc/nginx/sites-available/91jspg.com /etc/nginx/sites-available/91jspg.com.backup
```

### 2. 应用新配置

```bash
# 复制配置文件
sudo cp docs/nginx-collect-api.conf /etc/nginx/sites-available/91jspg.com

# 测试配置
sudo nginx -t

# 重载配置
sudo systemctl reload nginx
```

### 3. 验证配置

```bash
# 测试采集接口跨域
curl -H "Origin: https://example.com" \
     -H "Access-Control-Request-Method: GET" \
     -H "Access-Control-Request-Headers: X-Requested-With" \
     -X OPTIONS \
     https://91jspg.com/api/collect/vod

# 测试限流
for i in {1..20}; do curl https://91jspg.com/api/collect/vod; done
```

## 📈 性能优化

### 1. 启用Gzip压缩

```nginx
# 在http块中添加
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
```

### 2. 启用Brotli压缩（可选）

```nginx
brotli on;
brotli_comp_level 6;
brotli_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
```

### 3. 连接优化

```nginx
# 在http块中添加
keepalive_timeout 65;
keepalive_requests 100;
```

## 🔍 监控和维护

### 1. 日志分析

```bash
# 查看访问日志
tail -f /var/log/nginx/91jspg_access.log

# 分析API调用频率
awk '/\/api\/collect\// {print $1}' /var/log/nginx/91jspg_access.log | sort | uniq -c | sort -nr

# 查看限流情况
grep "limiting requests" /var/log/nginx/91jspg_error.log
```

### 2. 性能监控

```bash
# 检查Nginx状态
sudo systemctl status nginx

# 查看连接数
ss -tuln | grep :80
ss -tuln | grep :443

# 监控服务器资源
htop
iotop
```

### 3. 安全检查

```bash
# 检查SSL配置
nmap --script ssl-enum-ciphers -p 443 91jspg.com

# 检查安全头
curl -I https://91jspg.com/api/collect/vod
curl -I https://91jspg.com/
```

## ⚠️ 注意事项

### 1. 采集接口安全考虑

- ✅ **允许跨域**：必须支持第三方调用
- ✅ **适度限流**：防止恶意刷接口
- ✅ **缓存策略**：减轻服务器压力
- ❌ **不要过度限制**：避免影响正常采集

### 2. 前台页面安全

- ✅ **严格CSP**：防止XSS攻击
- ✅ **HSTS强制**：确保HTTPS访问
- ✅ **隐藏信息**：不暴露服务器版本
- ✅ **防点击劫持**：禁止iframe嵌入

### 3. 性能平衡

- 🎯 **采集接口**：响应速度 vs 安全防护
- 🎯 **前台页面**：用户体验 vs 安全策略
- 🎯 **静态资源**：缓存时间 vs 更新频率

## 🎉 预期效果

配置完成后，你的网站将实现：

1. **采集接口**：
   - ✅ 支持跨域调用
   - ✅ 防恶意刷接口
   - ✅ 5分钟智能缓存
   - ✅ 详细的访问日志

2. **前台页面**：
   - ✅ A+级安全评分
   - ✅ 完整的安全防护
   - ✅ 优秀的用户体验
   - ✅ SEO友好配置

3. **整体性能**：
   - ✅ 高并发处理能力
   - ✅ 智能缓存策略
   - ✅ 资源优化加载
   - ✅ 实时监控告警

这套配置将为你的采集接口提供**企业级的安全防护**，同时保持**最佳的可用性**！

# 宝塔面板部署指南

## 🚀 部署架构

```
用户请求 → Nginx反向代理 → Nuxt前端(3000端口) → 内部API代理 → 后端API(3001端口)
```

## 📋 部署步骤

### 1. 服务器环境准备

```bash
# 安装Node.js (建议18+)
# 安装PM2
npm install -g pm2

# 克隆项目到服务器
git clone <your-repo> /www/wwwroot/91jspg.com
cd /www/wwwroot/91jspg.com

# 安装依赖
npm install

# 构建项目
npm run build:user
```

### 2. PM2 配置文件

创建 `ecosystem.config.js`:

```javascript
module.exports = {
  apps: [
    {
      name: '91jspg-frontend',
      script: '.output/server/index.mjs',
      cwd: '/www/wwwroot/91jspg.com',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        NITRO_PORT: 3000
      },
      error_file: '/www/wwwroot/91jspg.com/logs/frontend-error.log',
      out_file: '/www/wwwroot/91jspg.com/logs/frontend-out.log',
      log_file: '/www/wwwroot/91jspg.com/logs/frontend.log'
    },
    {
      name: '91jspg-backend',
      script: 'server/index.js', // 你的后端启动文件
      cwd: '/www/wwwroot/91jspg.com',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      error_file: '/www/wwwroot/91jspg.com/logs/backend-error.log',
      out_file: '/www/wwwroot/91jspg.com/logs/backend-out.log',
      log_file: '/www/wwwroot/91jspg.com/logs/backend.log'
    }
  ]
}
```

### 3. 启动服务

```bash
# 创建日志目录
mkdir -p /www/wwwroot/91jspg.com/logs

# 启动服务
pm2 start ecosystem.config.js

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
```

### 4. Nginx 配置

在宝塔面板中添加网站，然后配置Nginx：

```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name 91jspg.com www.91jspg.com;
    
    # SSL配置（如果使用HTTPS）
    ssl_certificate /path/to/your/cert.pem;
    ssl_certificate_key /path/to/your/key.pem;
    
    # 完整的安全头配置
    # 基础安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # 内容安全策略 (CSP) - 防止XSS攻击
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com https://hm.baidu.com https://s4.cnzz.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: http:; connect-src 'self' https://www.google-analytics.com https://hm.baidu.com; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self';" always;

    # HTTP严格传输安全 (HSTS) - 强制HTTPS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

    # 推荐人策略 - 控制Referer头信息泄露
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 权限策略 - 控制浏览器功能访问
    add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()" always;

    # 跨域嵌入器策略 - 防止点击劫持
    add_header Cross-Origin-Embedder-Policy "require-corp" always;

    # 跨域开启器策略 - 隔离跨域资源
    add_header Cross-Origin-Opener-Policy "same-origin" always;

    # 跨域资源策略 - 控制跨域资源访问
    add_header Cross-Origin-Resource-Policy "same-origin" always;

    # 服务器信息隐藏
    server_tokens off;
    more_clear_headers Server;
    add_header Server "91JSPG" always;

    # 防止MIME类型嗅探
    add_header X-Download-Options noopen;
    add_header X-Permitted-Cross-Domain-Policies none;
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri @nuxt;
    }
    
    # 主要代理配置
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
    
    # 如果需要直接访问后端API（用于采集等特殊场景）
    location /direct-api/ {
        rewrite ^/direct-api/(.*)$ /api/$1 break;
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 5. 环境变量配置

创建 `.env.production`:

```bash
NODE_ENV=production
API_BASE=/api
REAL_API_URL=https://91jspg.com/api
SERVER_API_URL=http://127.0.0.1:3001/api
BASE_URL=https://91jspg.com
```

## 🔧 API 调用说明

### 普通页面API调用

```javascript
// 使用内部代理，用户看不到真实后端地址
const response = await $fetch('/api/videos')
// 实际请求: 前端(3000) → 内部代理 → 后端(3001)
```

### 采集页面API地址显示

```javascript
// 显示给用户的真实API地址
const { $config } = useNuxtApp()
const apiUrl = $config.public.realApiUrl
// 显示: https://91jspg.com/api
```

## 📊 监控和维护

### PM2 常用命令

```bash
# 查看状态
pm2 status

# 查看日志
pm2 logs 91jspg-frontend
pm2 logs 91jspg-backend

# 重启服务
pm2 restart 91jspg-frontend
pm2 restart 91jspg-backend

# 停止服务
pm2 stop all

# 删除服务
pm2 delete all
```

### 日志轮转配置

```bash
# 安装PM2日志轮转
pm2 install pm2-logrotate

# 配置日志轮转
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
pm2 set pm2-logrotate:compress true
```

## 🔒 安全配置

### 防火墙设置

```bash
# 只开放必要端口
# 80, 443 (Nginx)
# 22 (SSH)
# 不要开放 3000, 3001 端口给外网
```

### 宝塔安全设置

1. 修改默认端口
2. 启用面板SSL
3. 设置访问限制
4. 定期更新系统

## 🚀 部署优化

### 1. 启用Gzip压缩

在Nginx配置中添加：

```nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
```

### 2. 启用HTTP/2

```nginx
listen 443 ssl http2;
```

### 3. 配置缓存

```nginx
# 静态资源缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

这样配置后，你的网站将：
- ✅ 前后端在同一服务器运行
- ✅ 通过Nginx对外提供服务
- ✅ API请求通过内部代理，用户看不到后端端口
- ✅ 采集页面显示真实API地址给用户
- ✅ 支持PM2进程管理和监控

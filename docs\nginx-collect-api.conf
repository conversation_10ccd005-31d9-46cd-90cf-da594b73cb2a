# 采集接口专用Nginx配置
# 既安全又对采集接口友好的配置

# 在http块中添加限流配置
http {
    # IP限流 - 每个IP每秒最多10个请求
    limit_req_zone $binary_remote_addr zone=ip_limit:10m rate=10r/s;
    
    # API限流 - 每个接口每秒最多5个请求  
    limit_req_zone $request_uri zone=api_limit:10m rate=5r/s;
    
    # 连接数限制 - 每个IP最多50个并发连接
    limit_conn_zone $binary_remote_addr zone=conn_limit:10m;
    
    # 请求体大小限制
    client_max_body_size 1m;
    
    # 超时设置
    client_body_timeout 10s;
    client_header_timeout 10s;
    
    # 其他配置...
}

server {
    listen 80;
    listen 443 ssl http2;
    server_name 91jspg.com www.91jspg.com;
    
    # SSL配置
    ssl_certificate /path/to/your/cert.pem;
    ssl_certificate_key /path/to/your/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 全局安全设置
    server_tokens off;
    
    # 采集接口专用配置 - /api/collect/
    location /api/collect/ {
        # 连接数限制
        limit_conn conn_limit 20;
        
        # 请求频率限制
        limit_req zone=api_limit burst=20 nodelay;
        limit_req zone=ip_limit burst=100 nodelay;
        
        # CORS配置 - 允许跨域访问
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, User-Agent" always;
        add_header Access-Control-Max-Age 3600 always;
        add_header Access-Control-Expose-Headers "Content-Length, Content-Range" always;
        
        # 处理CORS预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, User-Agent";
            add_header Access-Control-Max-Age 3600;
            add_header Content-Type "text/plain charset=UTF-8";
            add_header Content-Length 0;
            return 204;
        }
        
        # 采集接口专用安全头 - 相对宽松但仍然安全
        add_header X-Content-Type-Options nosniff always;
        add_header X-Frame-Options SAMEORIGIN always;  # 允许同源嵌入
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        
        # 缓存控制 - 适度缓存减轻服务器压力
        add_header Cache-Control "public, max-age=300, s-maxage=300" always;  # 5分钟缓存
        add_header Vary "Accept-Encoding, Origin" always;
        
        # 自定义服务器标识
        add_header Server "91JSPG-API/1.0" always;
        
        # API响应时间标识
        add_header X-Response-Time $request_time always;
        
        # 代理到后端API
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Original-URI $request_uri;
        
        # 超时设置
        proxy_connect_timeout 10s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        # 错误处理
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
        proxy_next_upstream_tries 2;
        proxy_next_upstream_timeout 10s;
    }
    
    # 前台页面 - 严格安全策略
    location / {
        # 严格的安全头
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com https://hm.baidu.com https://s4.cnzz.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: http:; connect-src 'self' https://www.google-analytics.com https://hm.baidu.com; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self';" always;
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=(), usb=()" always;
        
        # 自定义服务器标识
        add_header Server "91JSPG" always;
        
        # 代理到前端Nuxt应用
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }
    
    # 静态资源优化
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff always;
        add_header Access-Control-Allow-Origin "*" always;
        
        # 尝试直接提供文件，失败则代理到Nuxt
        try_files $uri @nuxt;
    }
    
    # Nuxt fallback
    location @nuxt {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 阻止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(env|log|config)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    # 日志配置
    access_log /var/log/nginx/91jspg_access.log;
    error_log /var/log/nginx/91jspg_error.log;
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name 91jspg.com www.91jspg.com;
    return 301 https://$server_name$request_uri;
}

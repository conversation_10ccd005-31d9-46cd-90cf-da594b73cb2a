# 完整的安全头配置指南

## 🛡️ 基础安全头

### 1. X-Frame-Options
```nginx
add_header X-Frame-Options DENY;
```
- **作用**: 防止页面被嵌入到iframe中，防止点击劫持攻击
- **选项**: 
  - `DENY`: 完全禁止嵌入
  - `SAMEORIGIN`: 只允许同源嵌入
  - `ALLOW-FROM uri`: 允许指定域名嵌入

### 2. X-Content-Type-Options
```nginx
add_header X-Content-Type-Options nosniff;
```
- **作用**: 防止浏览器MIME类型嗅探，强制使用服务器声明的Content-Type
- **防护**: 防止XSS攻击和文件上传漏洞

### 3. X-XSS-Protection
```nginx
add_header X-XSS-Protection "1; mode=block";
```
- **作用**: 启用浏览器内置的XSS过滤器
- **选项**:
  - `0`: 禁用XSS过滤
  - `1`: 启用XSS过滤
  - `1; mode=block`: 启用并阻止页面加载

## 🔒 高级安全头

### 4. Content-Security-Policy (CSP)
```nginx
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com https://hm.baidu.com https://s4.cnzz.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: http:; connect-src 'self' https://www.google-analytics.com https://hm.baidu.com; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self';" always;
```
- **作用**: 最强大的XSS防护，控制页面可以加载的资源
- **指令说明**:
  - `default-src 'self'`: 默认只允许同源资源
  - `script-src`: 控制JavaScript来源
  - `style-src`: 控制CSS来源
  - `img-src`: 控制图片来源
  - `connect-src`: 控制AJAX/WebSocket连接
  - `frame-src 'none'`: 禁止嵌入iframe
  - `object-src 'none'`: 禁止插件对象

### 5. Strict-Transport-Security (HSTS)
```nginx
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
```
- **作用**: 强制浏览器使用HTTPS连接
- **参数**:
  - `max-age=31536000`: 有效期1年
  - `includeSubDomains`: 包含所有子域名
  - `preload`: 允许加入HSTS预加载列表

### 6. Referrer-Policy
```nginx
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
```
- **作用**: 控制Referer头信息的发送策略
- **选项**:
  - `no-referrer`: 不发送Referer
  - `strict-origin-when-cross-origin`: 跨域时只发送源信息
  - `same-origin`: 只在同源时发送

### 7. Permissions-Policy
```nginx
add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()" always;
```
- **作用**: 控制浏览器功能的访问权限
- **功能控制**:
  - `camera=()`: 禁用摄像头
  - `microphone=()`: 禁用麦克风
  - `geolocation=()`: 禁用地理位置
  - `payment=()`: 禁用支付API

## 🌐 跨域安全头

### 8. Cross-Origin-Embedder-Policy
```nginx
add_header Cross-Origin-Embedder-Policy "require-corp" always;
```
- **作用**: 要求所有跨域资源明确声明可以被嵌入
- **选项**:
  - `unsafe-none`: 默认行为
  - `require-corp`: 需要CORP头

### 9. Cross-Origin-Opener-Policy
```nginx
add_header Cross-Origin-Opener-Policy "same-origin" always;
```
- **作用**: 隔离跨域窗口，防止Spectre攻击
- **选项**:
  - `unsafe-none`: 默认行为
  - `same-origin`: 只允许同源

### 10. Cross-Origin-Resource-Policy
```nginx
add_header Cross-Origin-Resource-Policy "same-origin" always;
```
- **作用**: 控制跨域资源的访问
- **选项**:
  - `same-site`: 同站点
  - `same-origin`: 同源
  - `cross-origin`: 允许跨域

## 🔧 服务器信息隐藏

### 11. 隐藏服务器版本信息
```nginx
server_tokens off;
more_clear_headers Server;
add_header Server "91JSPG" always;
```
- **作用**: 隐藏Nginx版本信息，防止针对性攻击
- **效果**: 服务器头显示自定义名称而非真实版本

### 12. 其他安全头
```nginx
add_header X-Download-Options noopen;
add_header X-Permitted-Cross-Domain-Policies none;
```
- `X-Download-Options`: 防止IE自动打开下载文件
- `X-Permitted-Cross-Domain-Policies`: 禁止Flash跨域策略

## 📋 完整的Nginx安全配置

```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name 91jspg.com www.91jspg.com;
    
    # SSL配置
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 完整安全头配置
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com https://hm.baidu.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: http:; connect-src 'self' https://www.google-analytics.com; frame-src 'none'; object-src 'none';" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=()" always;
    
    # 隐藏服务器信息
    server_tokens off;
    add_header Server "91JSPG" always;
    
    # 其他安全配置
    add_header X-Download-Options noopen always;
    add_header X-Permitted-Cross-Domain-Policies none always;
    
    # 主要代理配置
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 🔍 安全头检测工具

### 在线检测工具
1. **Security Headers**: https://securityheaders.com/
2. **Mozilla Observatory**: https://observatory.mozilla.org/
3. **SSL Labs**: https://www.ssllabs.com/ssltest/

### 检测命令
```bash
# 检测安全头
curl -I https://91jspg.com

# 检测SSL配置
nmap --script ssl-enum-ciphers -p 443 91jspg.com
```

## ⚠️ 注意事项

1. **CSP配置**: 根据实际使用的第三方服务调整
2. **HSTS预加载**: 确保SSL证书稳定后再启用preload
3. **跨域策略**: 如果需要嵌入其他网站，调整相关策略
4. **兼容性**: 某些老旧浏览器可能不支持新的安全头

## 🎯 安全等级评估

配置完成后，你的网站安全等级将达到：
- **Security Headers**: A+级别
- **SSL Labs**: A级别
- **Mozilla Observatory**: A+级别

这套配置将为你的成人影视网站提供企业级的安全防护！

## 🔄 采集接口专用安全配置

### 针对采集接口的特殊需求

由于采集接口需要被其他网站调用，需要特殊的安全策略：

```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name 91jspg.com www.91jspg.com;

    # SSL配置
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;

    # 采集接口专用配置 - 更宽松的安全策略
    location /api/collect/ {
        # 允许跨域访问
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control" always;
        add_header Access-Control-Max-Age 3600 always;

        # 处理预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control";
            add_header Access-Control-Max-Age 3600;
            add_header Content-Type "text/plain charset=UTF-8";
            add_header Content-Length 0;
            return 204;
        }

        # 采集接口专用安全头 - 相对宽松
        add_header X-Content-Type-Options nosniff always;
        add_header X-Frame-Options SAMEORIGIN always;  # 允许同源嵌入
        add_header Referrer-Policy "no-referrer-when-downgrade" always;  # 更宽松的引用策略

        # 不设置严格的CSP，因为采集接口需要被各种环境调用
        # add_header Content-Security-Policy "default-src *; script-src *; style-src *;" always;

        # 缓存控制 - 适度缓存以减轻服务器压力
        add_header Cache-Control "public, max-age=300" always;  # 5分钟缓存

        # 限流配置 - 防止滥用
        limit_req zone=api_limit burst=20 nodelay;
        limit_req zone=ip_limit burst=100 nodelay;

        # 代理到后端
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 30s;
        proxy_connect_timeout 10s;
    }

    # 前台页面 - 严格安全策略
    location / {
        # 严格的安全头
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com https://hm.baidu.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: http:; connect-src 'self' https://www.google-analytics.com; frame-src 'none'; object-src 'none';" always;
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=()" always;

        # 隐藏服务器信息
        server_tokens off;
        add_header Server "91JSPG" always;

        # 代理到前端
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff always;
        try_files $uri @nuxt;
    }

    # 限流区域定义（放在http块中）
    # limit_req_zone $binary_remote_addr zone=ip_limit:10m rate=10r/s;
    # limit_req_zone $request_uri zone=api_limit:10m rate=5r/s;
}
```

### 限流配置（在http块中添加）

```nginx
http {
    # IP限流 - 每个IP每秒最多10个请求
    limit_req_zone $binary_remote_addr zone=ip_limit:10m rate=10r/s;

    # API限流 - 每个接口每秒最多5个请求
    limit_req_zone $request_uri zone=api_limit:10m rate=5r/s;

    # 连接数限制 - 每个IP最多50个并发连接
    limit_conn_zone $binary_remote_addr zone=conn_limit:10m;

    # 其他配置...
}
```

### 采集接口安全特点

1. **跨域友好**: 允许所有域名访问采集接口
2. **适度缓存**: 5分钟缓存减轻服务器压力
3. **智能限流**: 防止恶意刷接口
4. **宽松CSP**: 不阻止第三方调用
5. **预检处理**: 正确处理CORS预检请求

### 安全与开放的平衡

- ✅ **采集接口**: 开放访问，适度限流
- ✅ **前台页面**: 严格安全策略
- ✅ **静态资源**: 长期缓存优化
- ✅ **DDoS防护**: 多层限流保护

这样配置既保证了采集接口的可用性，又维护了网站的整体安全性！

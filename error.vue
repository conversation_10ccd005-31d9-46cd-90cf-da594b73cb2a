<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center relative overflow-hidden">
    <!-- 背景动画元素 -->
    <div class="absolute inset-0 overflow-hidden">
      <!-- 浮动的圆形背景 -->
      <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-orange-500/10 rounded-full blur-3xl animate-pulse"></div>
      <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-red-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl animate-pulse delay-500"></div>
      
      <!-- 星星背景 -->
      <div class="stars"></div>
    </div>

    <!-- 主要内容 -->
    <div class="relative z-10 text-center px-4 max-w-4xl mx-auto">
      <!-- 404 大标题 -->
      <div class="mb-8">
        <h1 class="text-8xl md:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-orange-400 via-red-500 to-pink-500 animate-pulse">
          {{ error.statusCode || '404' }}
        </h1>
        <div class="mt-4 text-2xl md:text-3xl font-semibold text-white">
          {{ getErrorTitle() }}
        </div>
      </div>

      <!-- 错误描述 -->
      <div class="mb-12">
        <p class="text-lg md:text-xl text-gray-300 mb-4 leading-relaxed">
          {{ getErrorMessage() }}
        </p>
        <p class="text-gray-400">
          {{ getErrorDescription() }}
        </p>
      </div>

      <!-- 动画图标 -->
      <div class="mb-12 flex justify-center">
        <div class="relative">
          <!-- 主图标 -->
          <div class="w-32 h-32 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center shadow-2xl animate-bounce">
            <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33"></path>
            </svg>
          </div>
          
          <!-- 环绕的小圆点 -->
          <div class="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full animate-ping"></div>
          <div class="absolute -bottom-2 -left-2 w-4 h-4 bg-blue-400 rounded-full animate-ping delay-300"></div>
          <div class="absolute top-1/2 -left-4 w-3 h-3 bg-green-400 rounded-full animate-ping delay-700"></div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
        <!-- 返回首页按钮 -->
        <NuxtLink
          :to="$localePath('/')"
          class="group relative px-8 py-4 bg-gradient-to-r from-orange-500 to-red-500 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 overflow-hidden"
        >
          <span class="relative z-10 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
            </svg>
            {{ $t('error.actions.goHome') }}
          </span>
          <div class="absolute inset-0 bg-gradient-to-r from-orange-600 to-red-600 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></div>
        </NuxtLink>

        <!-- 返回上一页按钮 -->
        <button
          @click="goBack"
          class="group relative px-8 py-4 bg-gray-700 hover:bg-gray-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
        >
          <span class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            {{ $t('error.actions.goBack') }}
          </span>
        </button>
      </div>

      <!-- 快速导航 -->
      <div class="mt-16 pt-8 border-t border-gray-700">
        <h3 class="text-lg font-semibold text-white mb-6">{{ $t('error.navigation.title') }}</h3>
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-2xl mx-auto">
          <NuxtLink
            :to="$localePath('/categories')"
            class="group p-4 bg-gray-800/50 backdrop-blur-sm rounded-lg hover:bg-gray-700/50 transition-all duration-300 border border-gray-700 hover:border-orange-500"
          >
            <div class="text-orange-400 mb-2">
              <svg class="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
            </div>
            <div class="text-white font-medium group-hover:text-orange-400 transition-colors">{{ $t('error.navigation.categories') }}</div>
          </NuxtLink>

          <NuxtLink
            :to="$localePath('/rankings')"
            class="group p-4 bg-gray-800/50 backdrop-blur-sm rounded-lg hover:bg-gray-700/50 transition-all duration-300 border border-gray-700 hover:border-orange-500"
          >
            <div class="text-orange-400 mb-2">
              <svg class="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
              </svg>
            </div>
            <div class="text-white font-medium group-hover:text-orange-400 transition-colors">{{ $t('error.navigation.rankings') }}</div>
          </NuxtLink>

          <NuxtLink
            :to="$localePath('/search')"
            class="group p-4 bg-gray-800/50 backdrop-blur-sm rounded-lg hover:bg-gray-700/50 transition-all duration-300 border border-gray-700 hover:border-orange-500"
          >
            <div class="text-orange-400 mb-2">
              <svg class="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
            <div class="text-white font-medium group-hover:text-orange-400 transition-colors">{{ $t('error.navigation.search') }}</div>
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 接收错误对象和多语言
const props = defineProps({
  error: Object
})

const { t } = useI18n()

// 页面元信息
useHead({
  title: computed(() => `${props.error?.statusCode || '404'} ${t('error.meta.titleSuffix')}`),
  meta: [
    { name: 'description', content: computed(() => t('error.meta.description')) }
  ]
})

// 获取错误标题
const getErrorTitle = () => {
  const statusCode = props.error?.statusCode || 404
  switch (statusCode) {
    case 404:
      return t('error.titles.404')
    case 500:
      return t('error.titles.500')
    case 403:
      return t('error.titles.403')
    default:
      return t('error.titles.default')
  }
}

// 获取错误消息
const getErrorMessage = () => {
  const statusCode = props.error?.statusCode || 404
  switch (statusCode) {
    case 404:
      return t('error.messages.404')
    case 500:
      return t('error.messages.500')
    case 403:
      return t('error.messages.403')
    default:
      return props.error?.message || t('error.messages.default')
  }
}

// 获取错误描述
const getErrorDescription = () => {
  const statusCode = props.error?.statusCode || 404
  switch (statusCode) {
    case 404:
      return t('error.descriptions.404')
    case 500:
      return t('error.descriptions.500')
    case 403:
      return t('error.descriptions.403')
    default:
      return t('error.descriptions.default')
  }
}

// 返回上一页
const goBack = () => {
  if (import.meta.client && window.history.length > 1) {
    window.history.back()
  } else {
    const { $localePath } = useNuxtApp()
    navigateTo($localePath('/'))
  }
}
</script>

<style scoped>
/* 星星背景动画 */
.stars {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
}

.stars::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(2px 2px at 20px 30px, #eee, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, #fff, transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
    radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: sparkle 3s linear infinite;
}

@keyframes sparkle {
  from { transform: translateX(0); }
  to { transform: translateX(-200px); }
}

/* 渐变文字动画 */
@keyframes gradient {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* 响应式调整 */
@media (max-width: 640px) {
  .text-8xl {
    font-size: 4rem;
  }
  .text-9xl {
    font-size: 5rem;
  }
}
</style>

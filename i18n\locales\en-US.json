{"common": {"home": "Home", "categories": "All Categories", "live": "Live", "rankings": "Rankings", "collect": "Collect API", "search": "Search", "more": "More", "loading": "Loading...", "noData": "No data available", "confirm": "Confirm", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "add": "Add", "back": "Back", "next": "Next Page", "prev": "Previous Page", "total": "Total {count} items", "page": "Page {page}"}, "nav": {"home": "Home", "categories": "All Categories", "live": "Live", "rankings": "Rankings", "admin": "Admin Panel"}, "home": {"title": "91JSPG.COM", "subtitle": "Free HD AV Online | Daily Updates with Latest Content", "bannerTitle": "91JSPG.COM", "bannerSubtitle": "Free HD AV Online", "bannerFeatures": ["HD Quality", "Fast Streaming", "Multi-Device"], "features": {"hd": "HD Quality", "fast": "Fast Streaming", "responsive": "Multi-Device"}, "latest": "Latest Updates", "latestDesc": "Newest uploaded popular content", "hotCategories": "Hot Categories", "viewAll": "More", "noVideos": "No videos available", "loading": "Loading...", "loadingImage": "Loading...", "retry": "Retry", "uncategorized": "Uncategorized", "duration": "Duration", "rating": "Rating", "playVideo": "Play Video", "errorLoadingVideos": "Failed to load latest videos", "errorLoadingPage": "Failed to load page data", "imageLoadError": "Image loading failed", "videoGrid": {"playButton": "Play", "viewCount": "View Count", "uploadTime": "Upload Time"}, "latestVideos": "Latest Videos", "watchOnline": "Watch Online"}, "admin": {"title": "91JSPG.COM Admin Panel", "dashboard": "Dashboard", "videos": "Video Management", "categories": "Category Management", "api": "API Management", "admins": "Admin Management", "settings": "Site Settings", "logout": "Logout", "changePassword": "Change Password"}, "layout": {"siteName": "91JSPG.COM", "siteDescription": "Free HD AV Online", "searchPlaceholder": "Search videos...", "footerDescription": "Providing the latest and most comprehensive video resources, supporting online viewing, high-definition quality, and timely updates.", "quickNavigation": "Quick Navigation", "quickLinks": {"byCategory": "By Category"}, "contactUs": "Contact Us", "contact": {"support": "Online Support 24/7", "technical": "Technical Support", "mobile": "Mobile App"}, "adultContent": "18+ Adult Content", "metaDescription": "Providing the latest and most comprehensive video resources, supporting online viewing, high-definition quality, and timely updates."}, "categories": {"title": "Video Categories", "subtitle": "Browse different types of exciting content", "totalCount": "Total {count} categories", "videoCount": "{count} videos", "error": {"title": "Something went wrong", "loadFailed": "Failed to load categories", "retry": "Retry"}, "empty": {"title": "No Categories", "description": "No video categories available yet"}, "pagination": {"previous": "Previous", "next": "Next"}, "meta": {"title": "Video Categories - 91JSPG.COM | Free HD AV Online", "description": "Browse different types of exciting video content and discover your favorite categories.", "keywords": "video categories,movie categories,online viewing"}, "detail": {"breadcrumb": {"home": "Home", "categories": "Categories", "loading": "Loading..."}, "stats": {"totalVideos": "Total {count} videos", "lastUpdated": "Last updated: ", "showingResults": "Showing {start}-{end} of {total} results", "noResults": "No results"}, "sort": {"latest": "Latest Upload", "popular": "Most Popular", "rating": "Highest Rated", "views": "Most Viewed"}, "error": {"title": "Something went wrong", "loadFailed": "Failed to load category videos", "retry": "Retry", "imageLoadFailed": "Image loading failed"}, "empty": {"title": "No Videos", "description": "No videos available in this category yet"}, "loading": "Loading...", "uncategorized": "Uncategorized", "meta": {"titleSuffix": "- Video Categories - 91JSPG.COM", "descriptionTemplate": "Browse exciting video content in {name} category.", "keywordsTemplate": "{name},video categories,online viewing"}}}, "play": {"breadcrumb": {"home": "Home", "play": "Play"}, "error": {"title": "Something went wrong", "retry": "Retry", "videoNotFound": "Video not found", "loadFailed": "Failed to load video details", "pageLoadFailed": "Failed to load page data"}, "player": {"noSupport": "Your browser does not support HTML5 video playback.", "noVideoSource": "No video source available", "unknownDuration": "Unknown duration", "loading": "Player loading...", "errors": {"videoNotExist": "Video file does not exist", "videoNotAccessible": "Video file is not accessible", "playbackFailed": "Video playback failed", "playbackAborted": "Video playback was aborted", "networkError": "Network error, unable to load video", "decodeError": "Video decode error", "formatNotSupported": "Video format not supported", "segmentLoadFailed": "Video segment loading failed", "mediaRecoveryFailed": "Media error recovery failed", "unrecoverableError": "Unrecoverable error, destroying HLS instance"}}, "info": {"rating": "Rating", "views": "Views", "duration": "Duration", "status": "Status", "statusActive": "Active", "statusInactive": "Inactive", "featured": "Featured", "uncategorized": "Uncategorized"}, "sections": {"description": "Description", "recommended": "Related Videos"}, "meta": {"defaultTitle": "Play - 91JSPG.COM | Free HD AV Online", "defaultDescription": "High-definition video online playback, providing the best viewing experience.", "defaultKeywords": "online playback,HD video,free viewing", "titleSuffix": "- 91JSPG.COM | Free HD AV Online"}}, "rankings": {"title": "Popular Rankings", "subtitle": "Discover the most popular exciting content with real-time updated hot video rankings", "breadcrumb": {"home": "Home", "rankings": "Rankings"}, "categories": {"title": "Ranking Categories", "subtitle": "Select different ranking types to view data"}, "tabs": {"hot": "Hot Rankings", "latest": "Latest Rankings", "rating": "Rating Rankings", "views": "View Rankings"}, "periods": {"thisWeek": "This Week", "today": "Today", "thisMonth": "This Month"}, "labels": {"viewCount": "View Count", "publishTime": "Publish Time", "rating": "Rating", "views": "Views", "uncategorized": "Uncategorized"}, "error": {"title": "Something went wrong", "loadFailed": "Failed to load rankings data", "reload": "Reload"}, "dateFormat": {"today": "Today", "yesterday": "Yesterday", "daysAgo": "{days} days ago", "weeksAgo": "{weeks} weeks ago", "monthsAgo": "{months} months ago"}, "meta": {"title": "Rankings - 91JSPG.COM | Free HD AV Online", "description": "91JSPG.COM popular video rankings, real-time updates of the most popular content", "keywords": "rankings,hot videos,latest videos,recommended videos,view rankings"}}, "search": {"placeholder": "Search videos...", "title": "Search Results", "resultsCount": "Found {count} related videos", "sort": {"latest": "Latest Upload", "popular": "Most Popular", "rating": "Highest Rated", "views": "Most Viewed"}, "filter": {"allCategories": "All Categories"}, "loading": "Loading...", "uncategorized": "Uncategorized", "noResults": {"title": "No Content Found", "message": "No videos found related to \"{query}\"", "suggestions": {"title": "Suggestions:", "checkSpelling": "Check if the search term is correct", "useSimpleKeywords": "Try using simpler keywords", "browseCategories": "Browse different categories"}}, "defaultState": {"title": "Search Exciting Videos", "subtitle": "Enter keywords to start searching"}, "errors": {"searchFailed": "Search failed", "categoriesFailed": "Failed to load categories", "imageLoadFailed": "Image loading failed"}, "meta": {"title": "Search Videos - 91JSPG.COM | Free HD AV Online", "description": "Search for your favorite video content and discover more exciting content.", "keywords": "video search,movie search,online viewing"}}, "error": {"titles": {"404": "Page Not Found", "500": "Server Error", "403": "Access Denied", "default": "Something Went Wrong"}, "messages": {"404": "Sorry, the page you are looking for might have been removed, renamed, or is temporarily unavailable.", "500": "The server encountered an error and we are working to fix it.", "403": "You don't have permission to access this page.", "default": "An unknown error occurred."}, "descriptions": {"404": "Please check if the URL is correct, or use the navigation below to return.", "500": "Please try again later, or contact our technical support.", "403": "If you think this is an error, please contact the administrator.", "default": "Please try refreshing the page or return to the homepage."}, "actions": {"goHome": "Go Home", "goBack": "Go Back"}, "navigation": {"title": "Or visit these pages", "categories": "All Categories", "rankings": "Rankings", "search": "Search"}, "meta": {"titleSuffix": "- Page Not Found", "description": "Sorry, the page you are looking for does not exist."}}, "collect": {"title": "Collection API", "subtitle": "Professional video resource collection API compatible with Apple CMS format", "stats": {"videoResources": "{count} video resources", "categories": "{count} categories"}, "banner": {"title": "91JSPG Collection API", "subtitle": "Professional video resource collection service", "features": {"compatible": "✓ Apple CMS Compatible", "unlimited": "✓ No Rate Limit", "service": "✓ 7x24 Hour Service", "free": "✓ Completely Free"}}, "announcement": {"title": "📢 Latest Announcement", "status": "Real-time Updates", "launch": {"date": "2025-07-24", "title": "API Launch", "content": "Collection API officially launched, supports Apple CMS v10 format, welcome all video sites to integrate!"}, "features": {"title": "🚀 API Features", "content": "No registration required, no rate limit, supports JSON/XML dual formats, real-time data updates"}, "support": {"title": "🛠️ Technical Support", "content": "If you have any questions, please contact the webmaster, we will reply within 24 hours"}}, "coreFeatures": {"apiInfo": {"title": "API Information", "version": "API Version", "compatibility": "Compatible Format", "outputFormat": "Output Format", "rateLimit": "Rate Limit", "unlimited": "Unlimited", "totalVideos": "Total Videos", "totalCategories": "Total Categories"}, "quickStart": {"title": "Quick Start", "step1": "Get Video List", "step2": "Get Video Details", "step3": "Search Videos"}, "realTimeStats": {"title": "Real-time Statistics", "totalVideosLabel": "Total Videos", "categoriesLabel": "Categories", "onlineService": "Online Service", "freeService": "Completely Free"}}, "demo": {"title": "API Demo", "subtitle": "Real-time data preview and online testing", "realTimeData": "Real-time Data", "json": {"title": "JSON Format Example", "refresh": "Refresh Data", "requestUrl": "Request URL"}, "xml": {"title": "XML Format Example", "refresh": "Refresh Data", "requestUrl": "Request URL"}}, "documentation": {"title": "API Documentation", "subtitle": "Detailed API usage instructions and parameter descriptions", "apiTitle": "Video Collection API", "apiAddress": "API Address", "requestParams": "Request Parameters", "responseParams": "Response Parameters", "copyToClipboard": "Copy to Clipboard", "copied": "<PERSON>pied", "categoryUsage": {"title": "Category Usage Instructions", "paramUsage": "Use parameter", "example": "Example:", "allSupported": "All categories support pagination, search and other functions"}, "testCollection": "Test Collection"}, "serviceGuarantee": {"title": "Service Guarantee", "service247": {"title": "7x24 Hour Stable Service", "description": "Year-round, stable and reliable"}, "noRateLimit": {"title": "No Rate Limit", "description": "Collect anytime, unlimited times"}, "realTimeUpdate": {"title": "Real-time Data Updates", "description": "Latest resources, timely synchronization"}, "completelyFree": {"title": "Completely Free to Use", "description": "No registration required, permanently free"}}, "technicalSupport": {"title": "Technical Support", "officialWebsite": {"title": "Official Website", "description": "Visit the main site for more information"}, "apiStatus": {"title": "API Status", "description": "Real-time service monitoring", "running": "Running Normally"}, "responseTime": {"title": "Response Time", "description": "Average API response speed"}, "support24h": {"title": "24-Hour Technical Support", "description": "If you encounter technical problems, please contact us through the official website, and we will reply within 24 hours. Professional team provides you with round-the-clock technical support services."}}, "disclaimer": {"title": "Terms of Use", "content": "This collection API is provided completely free of charge and is for learning and communication purposes only. Please comply with relevant laws and regulations and use API resources reasonably. Using this API means you agree to abide by the terms of use and jointly maintain a good collection environment. We reserve the right to maintain and upgrade the API, and will notify in advance if there are major changes.", "copyright": "© 2025 91JSPG.COM - Professional Video Resource Collection API Service Provider"}, "meta": {"title": "Collection API - 91JSPG.COM", "description": "91JSPG.COM video resource collection API, compatible with Apple CMS format, supports JSON and XML output, no rate limit", "keywords": "video collection,Apple CMS,API interface,video collection,resource site"}, "onlineTestTool": {"title": "Online Test Tool", "testListApi": "Test List API", "testDetailApi": "Test Detail API", "testSearchApi": "Test Search API", "testXmlFormat": "Test XML Format"}, "categories": {"title": "Resource Categories", "subtitle": "Collect different types of video resources by category", "categoryCount": "{count} categories", "videoCount": "{count} videos"}, "apiParams": {"paramName": "Parameter", "type": "Type", "required": "Required", "description": "Description", "yes": "Yes", "no": "No", "descriptions": {"ac": "Operation type: list(list) | detail(details) | videolist(content)", "t": "Category ID, get videos of specified category", "pg": "Page number, default is 1", "wd": "Search keyword", "h": "Get data within hours", "ids": "Video IDs, separated by commas, used to get details", "at": "Output format: json(default) | xml"}}, "examples": {"title": "Example Requests", "getVideoList": "Get Video List (Page 1)", "getCategoryVideos": "Get Category Videos", "searchVideos": "Search Videos", "getVideoDetails": "Get Video Details", "getRecentVideos": "Get Videos Updated in 24 Hours", "xmlFormat": "XML Format Output"}, "actions": {"copyAddress": "Copy Address", "copyLink": "Copy Link", "testApi": "Test API", "copied": "Copied to clipboard", "copyFailed": "Co<PERSON> failed"}, "loading": {"loadingExample": "Loading example data...", "loading": "Loading...", "loadFailed": "Load failed, please check if the API is working properly"}}, "live": {"seo": {"title": "Live Platforms", "description": "Premium live streaming platforms, real-time online viewing, multi-platform aggregation, mobile optimized, HD streaming support", "keywords": "live streaming,online live,live platforms,real-time viewing,HD streaming,mobile streaming"}, "platform": {"seo": {"title": "Live Rooms", "description": "Watch exciting live content on {platform} platform, real-time interaction, HD quality, perfectly optimized for mobile", "keywords": "{platform},live rooms,online streaming,real-time viewing,{platform} live,HD streaming", "roomList": "Live Room List", "liveRoom": "'s Live Room"}}}}
{"common": {"home": "ホーム", "categories": "すべてのカテゴリ", "live": "ライブ", "rankings": "ランキング", "collect": "収集API", "search": "検索", "more": "もっと見る", "loading": "読み込み中...", "noData": "データがありません", "confirm": "確認", "cancel": "キャンセル", "save": "保存", "edit": "編集", "delete": "削除", "add": "追加", "back": "戻る", "next": "次のページ", "prev": "前のページ", "total": "合計 {count} 件", "page": "{page} ページ目"}, "nav": {"home": "ホーム", "categories": "すべてのカテゴリ", "live": "ライブ", "rankings": "ランキング", "admin": "管理パネル"}, "home": {"title": "91JSPG.COM", "subtitle": "無料高画質AV動画 | 毎日最新コンテンツ更新", "bannerTitle": "91JSPG.COM", "bannerSubtitle": "無料高画質AVオンライン", "bannerFeatures": ["高画質", "高速再生", "マルチデバイス対応"], "features": {"hd": "高画質", "fast": "高速再生", "responsive": "マルチデバイス対応"}, "latest": "最新更新", "latestDesc": "最新アップロードの人気コンテンツ", "hotCategories": "人気カテゴリ", "viewAll": "もっと見る", "noVideos": "動画がありません", "loading": "読み込み中...", "loadingImage": "読み込み中...", "retry": "再試行", "uncategorized": "未分類", "duration": "再生時間", "rating": "評価", "playVideo": "動画を再生", "errorLoadingVideos": "最新動画の取得に失敗しました", "errorLoadingPage": "ページデータの読み込みに失敗しました", "imageLoadError": "画像の読み込みに失敗しました", "videoGrid": {"playButton": "再生", "viewCount": "視聴回数", "uploadTime": "アップロード時間"}, "latestVideos": "最新動画", "watchOnline": "オンライン視聴"}, "admin": {"title": "91JSPG.COM 管理パネル", "dashboard": "ダッシュボード", "videos": "動画管理", "categories": "カテゴリ管理", "api": "API管理", "admins": "管理者管理", "settings": "サイト設定", "logout": "ログアウト", "changePassword": "パスワード変更"}, "layout": {"siteName": "91JSPG.COM", "siteDescription": "無料高画質AVオンライン", "searchPlaceholder": "動画を検索...", "footerDescription": "最新で最も包括的な動画リソースを提供し、オンライン視聴、高画質、タイムリーな更新をサポートします。", "quickNavigation": "クイックナビゲーション", "quickLinks": {"byCategory": "カテゴリ別"}, "contactUs": "お問い合わせ", "contact": {"support": "オンラインサポート 24/7", "technical": "技術サポート", "mobile": "モバイルアプリ"}, "adultContent": "18+ アダルトコンテンツ", "metaDescription": "最新で最も包括的な動画リソースを提供し、オンライン視聴、高画質、タイムリーな更新をサポートします。"}, "categories": {"title": "動画カテゴリ", "subtitle": "さまざまなタイプのエキサイティングなコンテンツを閲覧", "totalCount": "合計 {count} カテゴリ", "videoCount": "{count} 動画", "error": {"title": "エラーが発生しました", "loadFailed": "カテゴリの読み込みに失敗しました", "retry": "再試行"}, "empty": {"title": "カテゴリがありません", "description": "まだ動画カテゴリがありません"}, "pagination": {"previous": "前へ", "next": "次へ"}, "meta": {"title": "動画カテゴリ - 91JSPG.COM | 無料高画質AVオンライン", "description": "さまざまなタイプのエキサイティングな動画コンテンツを閲覧し、お気に入りのカテゴリを発見してください。", "keywords": "動画カテゴリ,映画カテゴリ,オンライン視聴"}, "detail": {"breadcrumb": {"home": "ホーム", "categories": "カテゴリ", "loading": "読み込み中..."}, "stats": {"totalVideos": "合計 {count} 動画", "lastUpdated": "最終更新：", "showingResults": "{start}-{end} 件を表示（全 {total} 件）", "noResults": "結果なし"}, "sort": {"latest": "最新アップロード", "popular": "最も人気", "rating": "最高評価", "views": "最多視聴"}, "error": {"title": "エラーが発生しました", "loadFailed": "カテゴリ動画の読み込みに失敗しました", "retry": "再試行", "imageLoadFailed": "画像の読み込みに失敗しました"}, "empty": {"title": "動画がありません", "description": "このカテゴリにはまだ動画がありません"}, "loading": "読み込み中...", "uncategorized": "未分類", "meta": {"titleSuffix": "- 動画カテゴリ - 91JSPG.COM", "descriptionTemplate": "{name}カテゴリのエキサイティングな動画コンテンツを閲覧。", "keywordsTemplate": "{name},動画カテゴリ,オンライン視聴"}}}, "play": {"breadcrumb": {"home": "ホーム", "play": "再生"}, "error": {"title": "エラーが発生しました", "retry": "再試行", "videoNotFound": "動画が見つかりません", "loadFailed": "動画詳細の読み込みに失敗しました", "pageLoadFailed": "ページデータの読み込みに失敗しました"}, "player": {"noSupport": "お使いのブラウザはHTML5動画再生をサポートしていません。", "noVideoSource": "動画ソースがありません", "unknownDuration": "不明な再生時間", "loading": "プレーヤー読み込み中...", "errors": {"videoNotExist": "動画ファイルが存在しません", "videoNotAccessible": "動画ファイルにアクセスできません", "playbackFailed": "動画再生に失敗しました", "playbackAborted": "動画再生が中止されました", "networkError": "ネットワークエラー、動画を読み込めません", "decodeError": "動画デコードエラー", "formatNotSupported": "動画形式がサポートされていません", "segmentLoadFailed": "動画セグメントの読み込みに失敗しました", "mediaRecoveryFailed": "メディアエラーの回復に失敗しました", "unrecoverableError": "回復不可能なエラー、HLSインスタンスを破棄"}}, "info": {"rating": "評価", "views": "視聴回数", "duration": "再生時間", "status": "ステータス", "statusActive": "アクティブ", "statusInactive": "非アクティブ", "featured": "おすすめ", "uncategorized": "未分類"}, "sections": {"description": "動画説明", "recommended": "関連動画"}, "meta": {"defaultTitle": "再生 - 91JSPG.COM | 無料高画質AVオンライン", "defaultDescription": "高画質動画のオンライン再生、最高の視聴体験を提供します。", "defaultKeywords": "オンライン再生,高画質動画,無料視聴", "titleSuffix": "- 91JSPG.COM | 無料高画質AVオンライン"}}, "rankings": {"title": "人気ランキング", "subtitle": "最も人気のあるエキサイティングなコンテンツを発見、リアルタイム更新の人気動画ランキング", "breadcrumb": {"home": "ホーム", "rankings": "ランキング"}, "categories": {"title": "ランキングカテゴリ", "subtitle": "異なるランキングタイプを選択してデータを表示"}, "tabs": {"hot": "人気ランキング", "latest": "最新ランキング", "rating": "評価ランキング", "views": "視聴ランキング"}, "periods": {"thisWeek": "今週", "today": "今日", "thisMonth": "今月"}, "labels": {"viewCount": "視聴回数", "publishTime": "公開時間", "rating": "評価", "views": "視聴", "uncategorized": "未分類"}, "error": {"title": "エラーが発生しました", "loadFailed": "ランキングデータの読み込みに失敗しました", "reload": "再読み込み"}, "dateFormat": {"today": "今日", "yesterday": "昨日", "daysAgo": "{days}日前", "weeksAgo": "{weeks}週間前", "monthsAgo": "{months}ヶ月前"}, "meta": {"title": "ランキング - 91JSPG.COM | 無料高画質AVオンライン", "description": "91JSPG.COM 人気動画ランキング、最も人気のあるコンテンツをリアルタイム更新", "keywords": "ランキング,人気動画,最新動画,おすすめ動画,視聴ランキング"}}, "search": {"placeholder": "動画を検索...", "title": "検索結果", "resultsCount": "{count}件の関連動画が見つかりました", "sort": {"latest": "最新アップロード", "popular": "最も人気", "rating": "最高評価", "views": "最多視聴"}, "filter": {"allCategories": "すべてのカテゴリ"}, "loading": "読み込み中...", "uncategorized": "未分類", "noResults": {"title": "関連コンテンツが見つかりません", "message": "\"{query}\"に関連する動画が見つかりませんでした", "suggestions": {"title": "提案：", "checkSpelling": "検索語が正しいか確認してください", "useSimpleKeywords": "より簡単なキーワードを試してください", "browseCategories": "異なるカテゴリを閲覧してください"}}, "defaultState": {"title": "エキサイティングな動画を検索", "subtitle": "キーワードを入力して検索を開始"}, "errors": {"searchFailed": "検索に失敗しました", "categoriesFailed": "カテゴリの読み込みに失敗しました", "imageLoadFailed": "画像の読み込みに失敗しました"}, "meta": {"title": "動画検索 - 91JSPG.COM | 無料高画質AVオンライン", "description": "お気に入りの動画コンテンツを検索し、より多くのエキサイティングなコンテンツを発見してください。", "keywords": "動画検索,映画検索,オンライン視聴"}}, "error": {"titles": {"404": "ページが見つかりません", "500": "サーバーエラー", "403": "アクセス拒否", "default": "問題が発生しました"}, "messages": {"404": "申し訳ございませんが、お探しのページは削除、名前変更、または一時的に利用できない可能性があります。", "500": "サーバーでエラーが発生しました。修復作業を行っております。", "403": "このページにアクセスする権限がありません。", "default": "不明なエラーが発生しました。"}, "descriptions": {"404": "URLが正しいかご確認いただくか、下記のナビゲーションをご利用ください。", "500": "しばらくしてから再度お試しいただくか、技術サポートにお問い合わせください。", "403": "これがエラーだと思われる場合は、管理者にお問い合わせください。", "default": "ページを更新するか、ホームページに戻ってください。"}, "actions": {"goHome": "ホームに戻る", "goBack": "前のページに戻る"}, "navigation": {"title": "または、これらのページをご覧ください", "categories": "すべてのカテゴリ", "rankings": "ランキング", "search": "検索"}, "meta": {"titleSuffix": "- ページが見つかりません", "description": "申し訳ございませんが、お探しのページは存在しません。"}}, "collect": {"title": "収集API", "subtitle": "Apple CMS形式対応のプロフェッショナル動画リソース収集API", "stats": {"videoResources": "{count}個の動画リソース", "categories": "{count}個のカテゴリ"}, "banner": {"title": "91JSPG 収集API", "subtitle": "プロフェッショナル動画リソース収集サービス", "features": {"compatible": "✓ Apple CMS対応", "unlimited": "✓ レート制限なし", "service": "✓ 7x24時間サービス", "free": "✓ 完全無料"}}, "announcement": {"title": "📢 最新お知らせ", "status": "リアルタイム更新", "launch": {"date": "2025-07-24", "title": "API開始", "content": "収集APIが正式にリリースされ、Apple CMS v10形式をサポートします。すべての動画サイトの統合を歓迎します！"}, "features": {"title": "🚀 API機能", "content": "登録不要、レート制限なし、JSON/XMLデュアル形式サポート、リアルタイムデータ更新"}, "support": {"title": "🛠️ 技術サポート", "content": "ご質問がございましたら、ウェブマスターまでご連絡ください。24時間以内に返信いたします"}}, "coreFeatures": {"apiInfo": {"title": "API情報", "version": "APIバージョン", "compatibility": "対応形式", "outputFormat": "出力形式", "rateLimit": "レート制限", "unlimited": "無制限", "totalVideos": "総動画数", "totalCategories": "総カテゴリ数"}, "quickStart": {"title": "クイックスタート", "step1": "動画リスト取得", "step2": "動画詳細取得", "step3": "動画検索"}, "realTimeStats": {"title": "リアルタイム統計", "totalVideosLabel": "総動画数", "categoriesLabel": "カテゴリ数", "onlineService": "オンラインサービス", "freeService": "完全無料"}}, "demo": {"title": "APIデモ", "subtitle": "リアルタイムデータプレビューとオンラインテスト", "realTimeData": "リアルタイムデータ", "json": {"title": "JSON形式の例", "refresh": "データ更新", "requestUrl": "リクエストURL"}, "xml": {"title": "XML形式の例", "refresh": "データ更新", "requestUrl": "リクエストURL"}}, "documentation": {"title": "APIドキュメント", "subtitle": "詳細なAPI使用説明とパラメータ説明", "apiTitle": "動画収集API", "apiAddress": "APIアドレス", "requestParams": "リクエストパラメータ", "responseParams": "レスポンスパラメータ", "copyToClipboard": "クリップボードにコピー", "copied": "コピー済み", "categoryUsage": {"title": "カテゴリ使用説明", "paramUsage": "パラメータ使用", "example": "例:", "allSupported": "すべてのカテゴリでページネーション、検索などの機能をサポート"}, "testCollection": "収集テスト"}, "serviceGuarantee": {"title": "サービス保証", "service247": {"title": "7x24時間安定サービス", "description": "年中無休、安定で信頼性"}, "noRateLimit": {"title": "レート制限なし", "description": "いつでも収集、回数制限なし"}, "realTimeUpdate": {"title": "リアルタイムデータ更新", "description": "最新リソース、タイムリー同期"}, "completelyFree": {"title": "完全無料使用", "description": "登録不要、永久無料"}}, "technicalSupport": {"title": "技術サポート", "officialWebsite": {"title": "公式ウェブサイト", "description": "メインサイトにアクセスして詳細情報を取得"}, "apiStatus": {"title": "APIステータス", "description": "リアルタイムサービス監視", "running": "正常稼働"}, "responseTime": {"title": "応答時間", "description": "平均API応答速度"}, "support24h": {"title": "24時間技術サポート", "description": "技術的な問題が発生した場合は、公式ウェブサイトからお問い合わせください。24時間以内に返信いたします。プロフェッショナルチームが24時間体制で技術サポートサービスを提供します。"}}, "disclaimer": {"title": "利用規約", "content": "この収集APIは完全に無料で提供され、学習と交流の目的でのみ使用されます。関連する法律や規制を遵守し、APIリソースを合理的に使用してください。このAPIを使用することは、利用規約に同意し、良好な収集環境を共同で維持することを意味します。APIの保守とアップグレードの権利を留保し、重大な変更がある場合は事前に通知します。", "copyright": "© 2025 91JSPG.COM - プロフェッショナル動画リソース収集APIサービスプロバイダー"}, "meta": {"title": "収集API - 91JSPG.COM", "description": "91JSPG.COM 動画リソース収集API、Apple CMS形式対応、JSONとXML出力サポート、レート制限なし", "keywords": "動画収集,Apple CMS,APIインターフェース,動画収集,リソースサイト"}, "onlineTestTool": {"title": "オンラインテストツール", "testListApi": "リストAPIテスト", "testDetailApi": "詳細APIテスト", "testSearchApi": "検索APIテスト", "testXmlFormat": "XML形式テスト"}, "categories": {"title": "リソースカテゴリ", "subtitle": "カテゴリ別に異なるタイプの動画リソースを収集", "categoryCount": "{count}個のカテゴリ", "videoCount": "{count}個の動画"}, "apiParams": {"paramName": "パラメータ名", "type": "タイプ", "required": "必須", "description": "説明", "yes": "はい", "no": "いいえ", "descriptions": {"ac": "操作タイプ：list(リスト) | detail(詳細) | videolist(コンテンツ)", "t": "カテゴリID、指定カテゴリの動画を取得", "pg": "ページ番号、デフォルトは1", "wd": "検索キーワード", "h": "数時間以内のデータを取得", "ids": "動画ID、カンマ区切り、詳細取得用", "at": "出力形式：json(デフォルト) | xml"}}, "examples": {"title": "リクエスト例", "getVideoList": "動画リスト取得（1ページ目）", "getCategoryVideos": "指定カテゴリ動画取得", "searchVideos": "動画検索", "getVideoDetails": "動画詳細取得", "getRecentVideos": "24時間以内に更新された動画取得", "xmlFormat": "XML形式出力"}, "actions": {"copyAddress": "アドレスをコピー", "copyLink": "リンクをコピー", "testApi": "APIテスト", "copied": "クリップボードにコピーしました", "copyFailed": "コピーに失敗しました"}, "loading": {"loadingExample": "サンプルデータを読み込み中...", "loading": "読み込み中...", "loadFailed": "読み込みに失敗しました。APIが正常に動作しているか確認してください"}}, "live": {"seo": {"title": "ライブプラットフォーム", "description": "厳選された高品質ライブ配信プラットフォーム、リアルタイムオンライン視聴、マルチプラットフォーム統合、モバイル最適化、HD配信対応", "keywords": "ライブ配信,オンラインライブ,ライブプラットフォーム,リアルタイム視聴,HD配信,モバイル配信"}, "platform": {"seo": {"title": "ライブルーム", "description": "{platform}プラットフォームの素晴らしいライブコンテンツを視聴、リアルタイム交流、HD画質、モバイル完全対応", "keywords": "{platform},ライブルーム,オンライン配信,リアルタイム視聴,{platform}ライブ,HD配信", "roomList": "ライブルーム一覧", "liveRoom": "のライブルーム"}}}}
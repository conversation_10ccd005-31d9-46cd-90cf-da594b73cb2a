{"common": {"home": "首页", "categories": "全部分类", "live": "直播", "rankings": "排行榜", "collect": "采集接口", "search": "搜索", "more": "更多", "loading": "加载中...", "noData": "暂无数据", "confirm": "确认", "cancel": "取消", "save": "保存", "edit": "编辑", "delete": "删除", "add": "添加", "back": "返回", "next": "下一页", "prev": "上一页", "total": "共 {count} 条", "page": "第 {page} 页"}, "nav": {"home": "首页", "categories": "全部分类", "live": "直播", "rankings": "排行榜", "admin": "管理后台"}, "home": {"title": "91JSPG.COM", "subtitle": "免费高清AV在线看 | 每日更新最新内容", "bannerTitle": "91JSPG.COM", "bannerSubtitle": "免费高清AV在线看", "bannerFeatures": ["高清画质", "极速播放", "多端适配"], "features": {"hd": "高清画质", "fast": "极速播放", "responsive": "多端适配"}, "latest": "最近更新", "latestDesc": "最新上架的热门内容", "hotCategories": "热门分类", "viewAll": "更多", "noVideos": "暂无视频", "loading": "加载中...", "loadingImage": "加载中...", "retry": "重试", "uncategorized": "未分类", "duration": "时长", "rating": "评分", "playVideo": "播放视频", "errorLoadingVideos": "获取最新视频失败", "errorLoadingPage": "页面数据加载失败", "imageLoadError": "图片加载失败", "videoGrid": {"playButton": "播放", "viewCount": "观看次数", "uploadTime": "上传时间"}, "latestVideos": "最新视频", "watchOnline": "在线观看"}, "admin": {"title": "91JSPG.COM 管理后台", "dashboard": "仪表盘", "videos": "视频管理", "categories": "分类管理", "api": "API管理", "admins": "管理员管理", "settings": "网站设置", "logout": "退出登录", "changePassword": "修改密码"}, "layout": {"siteName": "91JSPG.COM", "siteDescription": "免费高清AV在线看", "searchPlaceholder": "搜索视频...", "footerDescription": "提供最新最全的影视资源，支持在线观看，高清画质，更新及时。", "quickNavigation": "快速导航", "quickLinks": {"byCategory": "按主题"}, "contactUs": "联系我们", "contact": {"support": "在线客服 24/7", "technical": "技术支持", "mobile": "移动应用"}, "adultContent": "18+ 成人内容", "metaDescription": "提供最新最全的影视资源，支持在线观看，高清画质，更新及时。"}, "categories": {"title": "视频分类", "subtitle": "浏览不同类型的精彩内容", "totalCount": "共 {count} 个分类", "videoCount": "{count} 个视频", "error": {"title": "出错了", "loadFailed": "获取分类列表失败", "retry": "重试"}, "empty": {"title": "暂无分类", "description": "还没有任何视频分类"}, "pagination": {"previous": "上一页", "next": "下一页"}, "meta": {"title": "视频分类 - 91JSPG.COM | 免费高清AV在线看", "description": "浏览不同类型的精彩视频内容，发现你喜欢的分类。", "keywords": "视频分类,影片分类,在线观看"}, "detail": {"breadcrumb": {"home": "首页", "categories": "分类", "loading": "加载中..."}, "stats": {"totalVideos": "共 {count} 个视频", "lastUpdated": "最后更新：", "showingResults": "显示第 {start}-{end} 个，共 {total} 个结果", "noResults": "暂无结果"}, "sort": {"latest": "最新上传", "popular": "最受欢迎", "rating": "评分最高", "views": "观看最多"}, "error": {"title": "出错了", "loadFailed": "获取分类视频失败", "retry": "重试", "imageLoadFailed": "图片加载失败"}, "empty": {"title": "暂无视频", "description": "该分类下还没有任何视频"}, "loading": "加载中...", "uncategorized": "未分类", "meta": {"titleSuffix": "- 视频分类 - 91JSPG.COM", "descriptionTemplate": "浏览{name}分类下的精彩视频内容。", "keywordsTemplate": "{name},视频分类,在线观看"}}}, "play": {"breadcrumb": {"home": "首页", "play": "播放"}, "error": {"title": "出错了", "retry": "重试", "videoNotFound": "视频不存在", "loadFailed": "获取视频详情失败", "pageLoadFailed": "页面数据加载失败"}, "player": {"noSupport": "您的浏览器不支持HTML5视频播放。", "noVideoSource": "暂无视频源", "unknownDuration": "未知时长", "loading": "播放器加载中...", "errors": {"videoNotExist": "视频文件不存在", "videoNotAccessible": "视频文件无法访问", "playbackFailed": "视频播放失败", "playbackAborted": "视频播放被中止", "networkError": "网络错误，无法加载视频", "decodeError": "视频解码错误", "formatNotSupported": "视频格式不支持", "segmentLoadFailed": "视频片段加载失败", "mediaRecoveryFailed": "媒体错误恢复失败", "unrecoverableError": "无法恢复的错误，销毁HLS实例"}}, "info": {"rating": "评分", "views": "观看", "duration": "时长", "status": "状态", "statusActive": "正常", "statusInactive": "下架", "featured": "推荐", "uncategorized": "未分类"}, "sections": {"description": "影片介绍", "recommended": "相关推荐"}, "meta": {"defaultTitle": "播放 - 91JSPG.COM | 免费高清AV在线看", "defaultDescription": "高清影片在线播放，提供最佳的观看体验。", "defaultKeywords": "在线播放,高清影片,免费观看", "titleSuffix": "- 91JSPG.COM | 免费高清AV在线看"}}, "rankings": {"title": "热门排行榜", "subtitle": "发现最受欢迎的精彩内容，实时更新的热门视频排行", "breadcrumb": {"home": "首页", "rankings": "排行榜"}, "categories": {"title": "排行榜分类", "subtitle": "选择不同的排行榜类型查看数据"}, "tabs": {"hot": "热门排行", "latest": "最新排行", "rating": "评分排行", "views": "观看排行"}, "periods": {"thisWeek": "本周", "today": "今日", "thisMonth": "本月"}, "labels": {"viewCount": "观看次数", "publishTime": "发布时间", "rating": "评分", "views": "观看", "uncategorized": "未分类"}, "error": {"title": "出错了", "loadFailed": "获取排行榜数据失败", "reload": "重新加载"}, "dateFormat": {"today": "今天", "yesterday": "昨天", "daysAgo": "{days}天前", "weeksAgo": "{weeks}周前", "monthsAgo": "{months}月前"}, "meta": {"title": "排行榜 - 91JSPG.COM | 免费高清AV在线看", "description": "91JSPG.COM 热门影片排行榜，实时更新最受欢迎的内容", "keywords": "排行榜,热门视频,最新视频,推荐视频,观看排行"}}, "search": {"placeholder": "搜索视频...", "title": "搜索结果", "resultsCount": "找到 {count} 个相关视频", "sort": {"latest": "最新上传", "popular": "最受欢迎", "rating": "评分最高", "views": "观看最多"}, "filter": {"allCategories": "所有分类"}, "loading": "加载中...", "uncategorized": "未分类", "noResults": {"title": "未找到相关内容", "message": "没有找到与 \"{query}\" 相关的视频", "suggestions": {"title": "建议：", "checkSpelling": "检查搜索词是否正确", "useSimpleKeywords": "尝试使用更简单的关键词", "browseCategories": "浏览不同的分类"}}, "defaultState": {"title": "搜索精彩视频", "subtitle": "输入关键词开始搜索"}, "errors": {"searchFailed": "搜索失败", "categoriesFailed": "获取分类失败", "imageLoadFailed": "图片加载失败"}, "meta": {"title": "搜索视频 - 91JSPG.COM | 免费高清AV在线看", "description": "搜索你喜欢的视频内容，发现更多精彩。", "keywords": "视频搜索,影片搜索,在线观看"}}, "error": {"titles": {"404": "页面走丢了", "500": "服务器开小差了", "403": "访问被拒绝", "default": "出现了一些问题"}, "messages": {"404": "抱歉，您访问的页面可能已被删除、重命名或暂时不可用。", "500": "服务器遇到了一个错误，我们正在努力修复中。", "403": "您没有权限访问此页面。", "default": "发生了未知错误。"}, "descriptions": {"404": "请检查网址是否正确，或使用下方的导航返回。", "500": "请稍后再试，或联系我们的技术支持。", "403": "如果您认为这是错误，请联系管理员。", "default": "请尝试刷新页面或返回首页。"}, "actions": {"goHome": "返回首页", "goBack": "返回上一页"}, "navigation": {"title": "或者访问这些页面", "categories": "全部分类", "rankings": "排行榜", "search": "搜索"}, "meta": {"titleSuffix": "- 页面未找到", "description": "抱歉，您访问的页面不存在。"}}, "collect": {"title": "采集接口", "subtitle": "兼容苹果CMS格式的专业影视资源采集接口", "stats": {"videoResources": "{count} 个视频资源", "categories": "{count} 个分类"}, "banner": {"title": "91JSPG 采集接口", "subtitle": "专业影视资源采集服务", "features": {"compatible": "✓ 兼容苹果CMS", "unlimited": "✓ 无频率限制", "service": "✓ 7x24小时服务", "free": "✓ 完全免费"}}, "announcement": {"title": "📢 最新公告", "status": "实时更新", "launch": {"date": "2025-07-24", "title": "接口上线", "content": "采集接口正式上线，支持苹果CMS v10格式，欢迎各大影视站对接！"}, "features": {"title": "🚀 接口特色", "content": "无需注册、无频率限制、支持JSON/XML双格式、实时数据更新"}, "support": {"title": "🛠️ 技术支持", "content": "如有问题请联系站长，我们将在24小时内回复"}}, "coreFeatures": {"apiInfo": {"title": "接口信息", "version": "接口版本", "compatibility": "兼容格式", "outputFormat": "输出格式", "rateLimit": "频率限制", "unlimited": "无限制", "totalVideos": "总视频数", "totalCategories": "分类数量"}, "quickStart": {"title": "快速开始", "step1": "获取视频列表", "step2": "获取视频详情", "step3": "搜索视频"}, "realTimeStats": {"title": "实时统计", "totalVideosLabel": "总视频数量", "categoriesLabel": "分类数量", "onlineService": "在线服务", "freeService": "完全免费使用"}}, "demo": {"title": "接口示例演示", "subtitle": "实时数据预览和在线测试", "realTimeData": "实时数据", "json": {"title": "JSON格式示例", "refresh": "刷新数据", "requestUrl": "请求地址"}, "xml": {"title": "XML格式示例", "refresh": "刷新数据", "requestUrl": "请求地址"}}, "documentation": {"title": "接口文档", "subtitle": "详细的API使用说明和参数介绍", "apiTitle": "视频采集接口", "apiAddress": "接口地址", "requestParams": "请求参数", "responseParams": "响应参数", "copyToClipboard": "复制到剪贴板", "copied": "已复制", "categoryUsage": {"title": "分类使用说明", "paramUsage": "使用参数", "example": "示例:", "allSupported": "所有分类都支持分页、搜索等功能"}, "testCollection": "测试采集"}, "serviceGuarantee": {"title": "服务保障", "service247": {"title": "7x24小时稳定服务", "description": "全年无休，稳定可靠"}, "noRateLimit": {"title": "无频率限制", "description": "随时采集，不限次数"}, "realTimeUpdate": {"title": "实时数据更新", "description": "最新资源，及时同步"}, "completelyFree": {"title": "完全免费使用", "description": "无需注册，永久免费"}}, "technicalSupport": {"title": "技术支持", "officialWebsite": {"title": "官方网站", "description": "访问主站获取更多信息"}, "apiStatus": {"title": "接口状态", "description": "实时监控服务状态", "running": "正常运行"}, "responseTime": {"title": "响应时间", "description": "平均API响应速度"}, "support24h": {"title": "24小时技术支持", "description": "如遇到技术问题，请通过官网联系我们，我们将在24小时内回复。专业团队为您提供全天候技术支持服务。"}}, "disclaimer": {"title": "使用声明", "content": "本采集接口完全免费提供，仅供学习交流使用。请遵守相关法律法规，合理使用接口资源。使用本接口即表示您同意遵守使用条款，共同维护良好的采集环境。我们保留对接口进行维护和升级的权利，如有重大变更将提前通知。", "copyright": "© 2025 91JSPG.COM - 专业的影视资源采集接口服务商"}, "meta": {"title": "采集接口 - 91JSPG.COM", "description": "91JSPG.COM 影视资源采集接口，兼容苹果CMS格式，支持JSON和XML输出，无频率限制", "keywords": "影视采集,苹果CMS,API接口,视频采集,资源站"}, "onlineTestTool": {"title": "在线测试工具", "testListApi": "测试列表接口", "testDetailApi": "测试详情接口", "testSearchApi": "测试搜索接口", "testXmlFormat": "测试XML格式"}, "categories": {"title": "资源分类", "subtitle": "按分类采集不同类型的影视资源", "categoryCount": "{count} 个分类", "videoCount": "{count} 个视频"}, "apiParams": {"paramName": "参数名", "type": "类型", "required": "必填", "description": "说明", "yes": "是", "no": "否", "descriptions": {"ac": "操作类型：list(列表) | detail(详情) | videolist(内容)", "t": "分类ID，获取指定分类的视频", "pg": "页码，默认为1", "wd": "搜索关键字", "h": "获取几小时内的数据", "ids": "视频ID，多个用逗号分隔，用于获取详情", "at": "输出格式：json(默认) | xml"}}, "examples": {"title": "示例请求", "getVideoList": "获取视频列表（第1页）", "getCategoryVideos": "获取指定分类视频", "searchVideos": "搜索视频", "getVideoDetails": "获取视频详情", "getRecentVideos": "获取24小时内更新的视频", "xmlFormat": "XML格式输出"}, "actions": {"copyAddress": "复制地址", "copyLink": "复制链接", "testApi": "测试接口", "copied": "已复制到剪贴板", "copyFailed": "复制失败"}, "loading": {"loadingExample": "正在加载示例数据...", "loading": "正在加载...", "loadFailed": "加载失败，请检查接口是否正常"}}, "live": {"seo": {"title": "直播平台", "description": "精选优质直播平台，实时在线观看，多平台聚合，移动端适配，支持高清直播流", "keywords": "直播,在线直播,直播平台,实时观看,高清直播,移动直播"}, "platform": {"seo": {"title": "直播间", "description": "观看 {platform} 平台的精彩直播内容，实时互动，高清画质，移动端完美适配", "keywords": "{platform},直播间,在线直播,实时观看,{platform}直播,高清直播", "roomList": "直播间列表", "liveRoom": "的直播间"}}}}
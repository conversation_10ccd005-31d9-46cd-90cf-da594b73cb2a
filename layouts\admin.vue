<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-900 via-gray-900 to-gray-800">
    <!-- 顶部导航栏 -->
    <nav class="bg-gray-800/95 backdrop-blur-sm border-b border-gray-700/50 sticky top-0 z-50">
      <div class="w-full px-4 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- 左侧 Logo -->
          <div class="flex items-center">
            <div class="flex-shrink-0 flex items-center">
              <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                <span class="text-white font-bold text-lg">J</span>
              </div>
              <div class="ml-3">
                <span class="text-xl font-bold text-white">91JSPG.COM</span>
                <div class="text-xs text-gray-400">管理后台</div>
              </div>
            </div>
          </div>

          <!-- 右侧用户菜单 -->
          <div class="flex items-center space-x-4">
            <div class="relative">
              <button
                @click="toggleUserMenu"
                class="flex items-center space-x-3 text-gray-300 hover:text-white transition-colors p-2 rounded-xl hover:bg-gray-700/50"
              >
                <div class="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                  <span class="text-white font-medium text-sm">A</span>
                </div>
                <span class="text-sm font-medium">管理员</span>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              <!-- 用户下拉菜单 -->
              <div
                v-show="isUserMenuOpen"
                class="origin-top-right absolute right-0 mt-2 w-48 rounded-xl shadow-xl bg-gray-800/95 backdrop-blur-sm ring-1 ring-gray-700/50 z-50"
              >
                <div class="py-2">
                  <button
                    @click="showChangePasswordModal"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700/50 transition-colors"
                  >
                    修改密码
                  </button>
                  <div class="border-t border-gray-700/50 my-1"></div>
                  <button
                    @click="logout"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700/50 transition-colors"
                  >
                    退出登录
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <div class="flex">
      <!-- 侧边栏 -->
      <div class="w-72 bg-gray-800/95 backdrop-blur-sm border-r border-gray-700/50 min-h-screen">
        <div class="flex flex-col h-full">
          <!-- 侧边栏导航 -->
          <nav class="flex-1 px-6 py-8 space-y-3">
            <NuxtLink
              to="/admin"
              class="flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group"
              :class="$route.path === '/admin' ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg shadow-orange-500/25' : 'text-gray-300 hover:text-white hover:bg-gray-700/50'"
            >
              <div class="w-8 h-8 rounded-lg flex items-center justify-center mr-3 transition-colors" :class="$route.path === '/admin' ? 'bg-white/20' : 'bg-gray-700/50 group-hover:bg-gray-600/50'">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                </svg>
              </div>
              <span class="font-medium">仪表盘</span>
            </NuxtLink>

            <NuxtLink
              to="/admin/videos"
              class="flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group"
              :class="$route.path.startsWith('/admin/videos') ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg shadow-orange-500/25' : 'text-gray-300 hover:text-white hover:bg-gray-700/50'"
            >
              <div class="w-8 h-8 rounded-lg flex items-center justify-center mr-3 transition-colors" :class="$route.path.startsWith('/admin/videos') ? 'bg-white/20' : 'bg-gray-700/50 group-hover:bg-gray-600/50'">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </div>
              <span class="font-medium">视频管理</span>
            </NuxtLink>

            <NuxtLink
              to="/admin/categories"
              class="flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group"
              :class="$route.path.startsWith('/admin/categories') ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg shadow-orange-500/25' : 'text-gray-300 hover:text-white hover:bg-gray-700/50'"
            >
              <div class="w-8 h-8 rounded-lg flex items-center justify-center mr-3 transition-colors" :class="$route.path.startsWith('/admin/categories') ? 'bg-white/20' : 'bg-gray-700/50 group-hover:bg-gray-600/50'">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <span class="font-medium">分类管理</span>
            </NuxtLink>

            <NuxtLink
              to="/admin/api"
              class="flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group"
              :class="$route.path.startsWith('/admin/api') ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg shadow-orange-500/25' : 'text-gray-300 hover:text-white hover:bg-gray-700/50'"
            >
              <div class="w-8 h-8 rounded-lg flex items-center justify-center mr-3 transition-colors" :class="$route.path.startsWith('/admin/api') ? 'bg-white/20' : 'bg-gray-700/50 group-hover:bg-gray-600/50'">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <span class="font-medium">API管理</span>
            </NuxtLink>

            <NuxtLink
              to="/admin/collect"
              class="flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group"
              :class="$route.path.startsWith('/admin/collect') ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg shadow-orange-500/25' : 'text-gray-300 hover:text-white hover:bg-gray-700/50'"
            >
              <div class="w-8 h-8 rounded-lg flex items-center justify-center mr-3 transition-colors" :class="$route.path.startsWith('/admin/collect') ? 'bg-white/20' : 'bg-gray-700/50 group-hover:bg-gray-600/50'">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
              </div>
              <span class="font-medium">采集管理</span>
            </NuxtLink>

            <NuxtLink
              to="/admin/admins"
              class="flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group"
              :class="$route.path.startsWith('/admin/admins') ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg shadow-orange-500/25' : 'text-gray-300 hover:text-white hover:bg-gray-700/50'"
            >
              <div class="w-8 h-8 rounded-lg flex items-center justify-center mr-3 transition-colors" :class="$route.path.startsWith('/admin/admins') ? 'bg-white/20' : 'bg-gray-700/50 group-hover:bg-gray-600/50'">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <span class="font-medium">管理员管理</span>
            </NuxtLink>


          </nav>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="flex-1">
        <main class="w-full px-4 lg:px-8 py-8">
          <slot />
        </main>
      </div>
    </div>

    <!-- 修改密码模态框 -->
    <div
      v-show="showPasswordModal"
      class="fixed inset-0 bg-gray-900/75 backdrop-blur-sm overflow-y-auto h-full w-full z-50"
      @click="closePasswordModal"
    >
      <div
        class="relative top-20 mx-auto p-6 border w-full max-w-md shadow-2xl rounded-2xl bg-gray-800/95 backdrop-blur-sm border-gray-700/50 m-4"
        @click.stop
      >
        <div class="mt-3">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-bold text-white">修改密码</h3>
            <button
              @click="closePasswordModal"
              class="text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-700/50 rounded-lg"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form @submit.prevent="changePassword">
            <div class="space-y-6">
              <!-- 当前密码 -->
              <div>
                <label for="currentPassword" class="block text-sm font-medium text-gray-300 mb-2">
                  当前密码 <span class="text-red-400">*</span>
                </label>
                <input
                  id="currentPassword"
                  v-model="passwordForm.currentPassword"
                  type="password"
                  required
                  class="block w-full px-4 py-3 border border-gray-600/50 placeholder-gray-400 text-white bg-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
                  placeholder="请输入当前密码"
                >
              </div>

              <!-- 新密码 -->
              <div>
                <label for="newPassword" class="block text-sm font-medium text-gray-300 mb-2">
                  新密码 <span class="text-red-400">*</span>
                </label>
                <input
                  id="newPassword"
                  v-model="passwordForm.newPassword"
                  type="password"
                  required
                  class="block w-full px-4 py-3 border border-gray-600/50 placeholder-gray-400 text-white bg-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
                  placeholder="请输入新密码"
                >
              </div>

              <!-- 确认新密码 -->
              <div>
                <label for="confirmPassword" class="block text-sm font-medium text-gray-300 mb-2">
                  确认新密码 <span class="text-red-400">*</span>
                </label>
                <input
                  id="confirmPassword"
                  v-model="passwordForm.confirmPassword"
                  type="password"
                  required
                  class="block w-full px-4 py-3 border border-gray-600/50 placeholder-gray-400 text-white bg-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
                  placeholder="请再次输入新密码"
                >
              </div>

              <!-- 密码强度提示 -->
              <div class="text-xs text-gray-500">
                <p>密码要求：</p>
                <ul class="list-disc list-inside mt-1 space-y-1">
                  <li>至少8个字符</li>
                  <li>包含大小写字母</li>
                  <li>包含数字</li>
                  <li>包含特殊字符</li>
                </ul>
              </div>
            </div>

            <div class="flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-700/50">
              <button
                type="button"
                @click="closePasswordModal"
                class="px-6 py-3 border border-gray-600/50 rounded-xl text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-700/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200"
              >
                取消
              </button>
              <button
                type="submit"
                class="px-6 py-3 border border-transparent rounded-xl text-sm font-medium text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200 shadow-lg shadow-orange-500/25"
              >
                修改密码
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 响应式数据
const isUserMenuOpen = ref(false)
const showPasswordModal = ref(false)

// 密码表单
const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 切换用户菜单
const toggleUserMenu = () => {
  isUserMenuOpen.value = !isUserMenuOpen.value
}

// 显示修改密码弹窗
const showChangePasswordModal = () => {
  isUserMenuOpen.value = false // 关闭用户菜单
  showPasswordModal.value = true
}

// 关闭修改密码弹窗
const closePasswordModal = () => {
  showPasswordModal.value = false
  passwordForm.value = {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  }
}

// 修改密码
const changePassword = () => {
  // 验证新密码和确认密码是否一致
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    alert('新密码和确认密码不一致！')
    return
  }

  // 验证密码强度
  if (passwordForm.value.newPassword.length < 8) {
    alert('密码长度至少8个字符！')
    return
  }

  // 这里可以添加更多密码强度验证逻辑
  const hasUpperCase = /[A-Z]/.test(passwordForm.value.newPassword)
  const hasLowerCase = /[a-z]/.test(passwordForm.value.newPassword)
  const hasNumbers = /\d/.test(passwordForm.value.newPassword)
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(passwordForm.value.newPassword)

  if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
    alert('密码必须包含大小写字母、数字和特殊字符！')
    return
  }

  // 模拟密码修改请求
  console.log('修改密码:', {
    currentPassword: passwordForm.value.currentPassword,
    newPassword: passwordForm.value.newPassword
  })

  // 显示成功消息
  alert('密码修改成功！')

  // 关闭弹窗
  closePasswordModal()
}

// 退出登录
const logout = () => {
  const { clearAuth } = useAuth()

  // 清除认证信息
  clearAuth()

  // 跳转到登录页面
  navigateTo('/admin/login')
}

// 点击外部关闭菜单
onMounted(() => {
  if (process.client) {
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.relative')) {
        isUserMenuOpen.value = false
      }
    })
  }
})
</script>

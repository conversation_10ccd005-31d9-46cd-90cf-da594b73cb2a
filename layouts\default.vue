<template>
  <div class="min-h-screen bg-gray-900">
    <!-- 顶部导航栏 -->
    <nav class="bg-gray-800 border-b border-gray-700 sticky top-0 z-50 w-full">
      <div class="w-full px-4 lg:px-8">
        <div class="w-full lg:w-[70%] mx-auto">
          <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <NuxtLink :to="localePath('/')" class="flex items-center space-x-2 group flex-shrink-0">
            <div class="w-10 h-10 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform" :class="themeGradientClass">
              <span class="text-white font-bold text-lg">{{ $t('layout.siteName').charAt(0) }}</span>
            </div>
            <!-- 移动端只显示图标，桌面端显示完整信息 -->
            <div class="hidden sm:block">
              <span class="text-xl font-bold text-white">
                {{ $t('layout.siteName') }}
              </span>
              <div class="text-xs text-gray-400 hidden lg:block">{{ $t('layout.siteDescription') }}</div>
            </div>
          </NuxtLink>

          <!-- 居中导航菜单 -->
          <div class="hidden md:flex items-center space-x-8 absolute left-1/2 transform -translate-x-1/2">
            <NuxtLink
              :to="localePath('/')"
              class="text-gray-300 hover:text-orange-400 transition-colors text-sm font-medium"
              :class="{ 'text-orange-400': $route.path === localePath('/') }"
            >
              {{ $t('common.home') }}
            </NuxtLink>
            <NuxtLink
              :to="localePath('/categories')"
              class="text-gray-300 hover:text-orange-400 transition-colors text-sm font-medium"
              :class="{ 'text-orange-400': $route.path.startsWith(localePath('/categories')) }"
            >
              {{ $t('common.categories') }}
            </NuxtLink>
            <NuxtLink
              :to="localePath('/live')"
              class="text-gray-300 hover:text-purple-400 transition-colors text-sm font-medium flex items-center space-x-1"
              :class="{ 'text-purple-400': $route.path.startsWith(localePath('/live')) }"
            >
              <span class="text-red-400">🔴</span>
              <span>{{ $t('common.live') }}</span>
            </NuxtLink>
            <NuxtLink
              :to="localePath('/rankings')"
              class="text-gray-300 hover:text-orange-400 transition-colors text-sm font-medium"
              :class="{ 'text-orange-400': $route.path.startsWith(localePath('/rankings')) }"
            >
              {{ $t('common.rankings') }}
            </NuxtLink>
            <!-- 采集功能暂时关闭 - 导航链接已注释
            <NuxtLink
              :to="localePath('/collect')"
              class="text-gray-300 hover:text-orange-400 transition-colors text-sm font-medium"
              :class="{ 'text-orange-400': $route.path.startsWith(localePath('/collect')) }"
            >
              {{ $t('common.collect') }}
            </NuxtLink>
            -->
          </div>

          <!-- 右侧菜单 -->
          <div class="flex items-center space-x-1 xs:space-x-2 sm:space-x-4 flex-shrink-0">
            <!-- 桌面端搜索按钮 -->
            <button
              @click="toggleSearch"
              class="hidden lg:block text-gray-300 hover:text-white transition-colors p-2 -m-2"
              id="desktop-search-button"
              aria-label="打开搜索"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </button>

            <!-- 移动端搜索按钮 -->
            <button
              @click="toggleSearch"
              @touchstart="handleTouchStart"
              class="lg:hidden text-gray-300 hover:text-white transition-colors p-1.5 xs:p-2 -m-1.5 xs:-m-2"
              id="mobile-search-button"
              aria-label="打开搜索"
            >
              <svg class="w-4 h-4 xs:w-5 xs:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </button>

            <!-- 语言切换器 -->
            <LanguageSwitcher />

            <!-- 移动端导航菜单按钮 -->
            <button
              @click="toggleMobileMenu"
              @touchstart="handleTouchStart"
              class="md:hidden text-gray-300 hover:text-white transition-colors p-1.5 xs:p-2 -m-1.5 xs:-m-2 relative z-50"
              id="mobile-menu-button"
              :aria-expanded="isMobileMenuOpen"
              aria-label="打开导航菜单"
            >
              <svg
                class="w-5 h-5 xs:w-6 xs:h-6 transition-transform duration-200"
                :class="{ 'rotate-90': isMobileMenuOpen }"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  :d="isMobileMenuOpen ? 'M6 18L18 6M6 6l12 12' : 'M4 6h16M4 12h16M4 18h16'"
                ></path>
              </svg>
            </button>
          </div>
        </div>
        </div>
      </div>

      <!-- 搜索框 - 支持桌面端和移动端 -->
      <div
        v-show="isSearchOpen"
        class="bg-gray-800 border-t border-gray-700 px-4 py-3"
      >
        <div class="w-full lg:w-[70%] mx-auto">
          <div class="relative">
            <input
              v-model="searchQuery"
              @keyup.enter="performSearch"
              @keyup.esc="isSearchOpen = false"
              type="text"
              :placeholder="$t('layout.searchPlaceholder')"
              class="w-full bg-gray-700 text-white border border-gray-600 rounded-lg px-4 py-2 pr-10 text-sm focus:outline-none focus:border-orange-500 transition-colors"
              id="search-input"
              ref="searchInput"
            />
            <button
              @click="performSearch"
              class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-orange-500 transition-colors"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- 移动端菜单 -->
      <Transition
        enter-active-class="transition-all duration-200 ease-out"
        enter-from-class="opacity-0 -translate-y-2"
        enter-to-class="opacity-100 translate-y-0"
        leave-active-class="transition-all duration-150 ease-in"
        leave-from-class="opacity-100 translate-y-0"
        leave-to-class="opacity-0 -translate-y-2"
      >
        <div
          v-show="isMobileMenuOpen"
          class="md:hidden bg-gray-800 border-t border-gray-700 relative z-40"
          id="mobile-menu"
        >
        <div class="px-4 py-3 space-y-1">
          <NuxtLink
            :to="localePath('/')"
            @click="closeMobileMenu"
            class="block px-4 py-3 text-gray-300 hover:text-orange-400 hover:bg-gray-700 rounded-lg transition-colors font-medium"
            :class="{ 'text-orange-400 bg-gray-700': $route.path === localePath('/') }"
          >
            {{ $t('common.home') }}
          </NuxtLink>
          <NuxtLink
            :to="localePath('/categories')"
            @click="closeMobileMenu"
            class="block px-4 py-3 text-gray-300 hover:text-orange-400 hover:bg-gray-700 rounded-lg transition-colors font-medium"
            :class="{ 'text-orange-400 bg-gray-700': $route.path.startsWith(localePath('/categories')) }"
          >
            {{ $t('common.categories') }}
          </NuxtLink>
          <NuxtLink
            :to="localePath('/live')"
            @click="closeMobileMenu"
            class="flex items-center space-x-2 px-4 py-3 text-gray-300 hover:text-purple-400 hover:bg-gray-700 rounded-lg transition-colors font-medium"
            :class="{ 'text-purple-400 bg-gray-700': $route.path.startsWith(localePath('/live')) }"
          >
            <span class="text-red-400">🔴</span>
            <span>{{ $t('common.live') }}</span>
          </NuxtLink>
          <NuxtLink
            :to="localePath('/rankings')"
            @click="closeMobileMenu"
            class="block px-4 py-3 text-gray-300 hover:text-orange-400 hover:bg-gray-700 rounded-lg transition-colors font-medium"
            :class="{ 'text-orange-400 bg-gray-700': $route.path.startsWith(localePath('/rankings')) }"
          >
            {{ $t('common.rankings') }}
          </NuxtLink>
          <!-- 采集功能暂时关闭 - 移动端导航链接已注释
          <NuxtLink
            :to="localePath('/collect')"
            @click="closeMobileMenu"
            class="block px-4 py-3 text-gray-300 hover:text-orange-400 hover:bg-gray-700 rounded-lg transition-colors font-medium"
            :class="{ 'text-orange-400 bg-gray-700': $route.path.startsWith(localePath('/collect')) }"
          >
            {{ $t('common.collect') }}
          </NuxtLink>
          -->


        </div>
        </div>
      </Transition>
    </nav>

    <!-- 主要内容 -->
    <main class="relative z-10">
      <slot />
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-800 border-t border-gray-700 text-white">
      <div class="w-full px-4 lg:px-8 py-6 md:py-8">
        <div class="w-full lg:w-[70%] mx-auto">
        <!-- 移动端优化布局 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
          <!-- Logo和描述 -->
          <div class="text-center md:text-left">
            <div class="flex items-center justify-center md:justify-start space-x-3 mb-4">
              <div class="w-8 h-8 rounded-lg flex items-center justify-center" :class="themeGradientClass">
                <span class="text-white font-bold">{{ $t('layout.siteName').charAt(0) }}</span>
              </div>
              <span class="text-lg font-bold text-white">{{ $t('layout.siteName') }}</span>
            </div>
            <p class="text-sm leading-relaxed text-gray-400 max-w-md mx-auto md:mx-0">
              {{ $t('layout.siteDescription') }}，{{ $t('layout.footerDescription') }}
            </p>
          </div>

          <!-- 快速链接 -->
          <div class="text-center md:text-left">
            <h3 class="font-semibold mb-3 text-white">{{ $t('layout.quickNavigation') }}</h3>
            <div class="grid grid-cols-2 md:grid-cols-1 gap-2">
              <NuxtLink :to="localePath('/categories')" class="text-sm text-gray-400 hover:text-orange-500 transition-colors block py-1">{{ $t('layout.quickLinks.byCategory') }}</NuxtLink>
              <NuxtLink :to="localePath('/rankings')" class="text-sm text-gray-400 hover:text-orange-500 transition-colors block py-1">{{ $t('common.rankings') }}</NuxtLink>
              <NuxtLink :to="localePath('/search')" class="text-sm text-gray-400 hover:text-orange-500 transition-colors block py-1">{{ $t('common.search') }}</NuxtLink>
              <!-- 采集功能暂时关闭 - 页脚链接已注释
              <NuxtLink :to="localePath('/collect')" class="text-sm text-gray-400 hover:text-orange-500 transition-colors block py-1">{{ $t('common.collect') }}</NuxtLink>
              -->
            </div>
          </div>

          <!-- 联系信息 -->
          <div class="text-center md:text-left">
            <h3 class="font-semibold mb-3 text-white">{{ $t('layout.contactUs') }}</h3>
            <div class="grid grid-cols-2 md:grid-cols-1 gap-2 text-sm text-gray-400">
              <div class="flex items-center justify-center md:justify-start space-x-2 py-1">
                <span>📧</span>
                <span><EMAIL></span>
              </div>
              <div class="flex items-center justify-center md:justify-start space-x-2 py-1">
                <span>💬</span>
                <span>{{ $t('layout.contact.support') }}</span>
              </div>
              <div class="flex items-center justify-center md:justify-start space-x-2 py-1">
                <span>🔧</span>
                <span>{{ $t('layout.contact.technical') }}</span>
              </div>
              <div class="flex items-center justify-center md:justify-start space-x-2 py-1">
                <span>📱</span>
                <span>{{ $t('layout.contact.mobile') }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="mt-6 pt-4 md:pt-6 border-t border-gray-700 text-center text-xs md:text-sm text-gray-400">
          <p>{{ copyright }}</p>
          <p class="mt-1">{{ $t('layout.siteDescription') }} | {{ $t('layout.adultContent') }}</p>
        </div>
        </div>
      </div>
    </footer>

    <!-- 全局弹窗广告 -->
    <GlobalPopupAd
      v-if="showPopupAd"
      :ad-config="popupAdConfig"
      @close="closePopupAd"
      @click="handlePopupClick"
    />
  </div>
</template>

<script setup>
// 导入网站配置
import { siteConfig, getThemeGradientClass } from '~/config/site.js'

// i18n相关
const { t } = useI18n()
const localePath = useLocalePath()
const switchLocalePath = useSwitchLocalePath()

// 统计分析
const {
  initAllAnalytics,
  trackPageView,
  trackSearch,
  trackVideoPlay,
  trackDownload,
  trackShare
} = useAnalytics()

// 提供全局统计方法
provide('analytics', {
  trackSearch,
  trackVideoPlay,
  trackDownload,
  trackShare
})

// 网站配置
const copyright = siteConfig.basic.copyright
const keywords = siteConfig.basic.keywords
const themeGradientClass = getThemeGradientClass()

// 页面元数据
useHead({
  title: computed(() => `${t('layout.siteName')} | ${t('layout.siteDescription')}`),
  meta: [
    { name: 'description', content: computed(() => `${t('layout.siteDescription')}，${t('layout.metaDescription')}`) },
    { name: 'keywords', content: keywords }
  ]
})

// 移动端菜单状态
const isMobileMenuOpen = ref(false)
const isSearchOpen = ref(false)
const searchQuery = ref('')

// 调试模式
const isDebugMode = ref(false)



// 执行搜索
const performSearch = () => {
  if (!searchQuery.value.trim()) return

  navigateTo({
    path: localePath('/search'),
    query: { q: searchQuery.value }
  })

  // 关闭移动端搜索框
  isSearchOpen.value = false
}

// 切换搜索框
const toggleSearch = () => {
  isSearchOpen.value = !isSearchOpen.value
  // 关闭其他菜单
  isMobileMenuOpen.value = false

  // 如果打开搜索框，自动聚焦到输入框
  if (isSearchOpen.value) {
    nextTick(() => {
      const searchInput = document.querySelector('input[ref="searchInput"]') || document.querySelector('#search-input')
      if (searchInput) {
        searchInput.focus()
      }
    })
  }
}

// 处理触摸事件（移动端优化）
const handleTouchStart = (event) => {
  // 防止触摸事件被阻止
  event.stopPropagation()

  if (isDebugMode.value) {
    console.log('Touch event triggered:', event.target.id)
  }
}

// 切换移动端菜单
const toggleMobileMenu = (event) => {
  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }

  const newState = !isMobileMenuOpen.value
  isMobileMenuOpen.value = newState

  // 关闭其他菜单
  isSearchOpen.value = false

  if (isDebugMode.value) {
    console.log('Mobile menu toggled:', newState)
  }
}

// 关闭移动端菜单
const closeMobileMenu = () => {
  isMobileMenuOpen.value = false
  isSearchOpen.value = false

  if (isDebugMode.value) {
    console.log('Mobile menu closed')
  }
}



// 监听路由变化，统计页面浏览和关闭菜单
const route = useRoute()
watch(() => route.path, (newPath) => {
  if (import.meta.client) {
    // 统计页面浏览
    nextTick(() => {
      trackPageView(newPath, document.title)
    })
  }

  // 关闭移动端菜单
  isMobileMenuOpen.value = false
  isSearchOpen.value = false
}, { immediate: true })

// 初始化统计和事件监听
onMounted(() => {
  if (import.meta.client) {
    // 初始化统计工具
    initAllAnalytics()

    // 启用调试模式（开发环境）
    if (process.env.NODE_ENV === 'development') {
      isDebugMode.value = true
      console.log('Mobile menu debug mode enabled')
    }

    // 点击外部关闭菜单 - 改进的逻辑
    const handleClickOutside = (e) => {
      // 检查点击的元素是否在菜单相关的元素内
      const isMenuButton = e.target.closest('#mobile-menu-button')
      const isSearchButton = e.target.closest('#search-button, #desktop-search-button, #mobile-search-button')
      const isSearchArea = e.target.closest('#search-input') || e.target.closest('.bg-gray-800.border-t.border-gray-700')
      const isMobileMenu = e.target.closest('[class*="md:hidden"][class*="bg-gray-800"]')
      const isLanguageSwitcher = e.target.closest('[class*="language-switcher"]')

      // 如果点击的不是菜单相关元素，则关闭菜单
      if (!isMenuButton && !isSearchButton && !isSearchArea && !isMobileMenu && !isLanguageSwitcher) {
        if (isMobileMenuOpen.value || isSearchOpen.value) {
          isMobileMenuOpen.value = false
          isSearchOpen.value = false

          if (isDebugMode.value) {
            console.log('Menus closed by outside click')
          }
        }
      }
    }

    // 添加点击和触摸事件监听
    document.addEventListener('click', handleClickOutside)
    document.addEventListener('touchstart', handleClickOutside)

    // 添加ESC键关闭菜单
    const handleKeyDown = (e) => {
      if (e.key === 'Escape') {
        if (isMobileMenuOpen.value || isSearchOpen.value) {
          isMobileMenuOpen.value = false
          isSearchOpen.value = false

          if (isDebugMode.value) {
            console.log('Menus closed by ESC key')
          }
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)

    // 清理事件监听器
    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside)
      document.removeEventListener('touchstart', handleClickOutside)
      document.removeEventListener('keydown', handleKeyDown)
    })
  }
})

// ==================== 全局弹窗广告功能 ====================

// 导入弹窗配置
import {
  getPopupAdConfig,
  shouldShowPopupAd,
  checkPopupInterval,
  recordPopupShown
} from '~/config/popup.js'

// 弹窗广告状态
const showPopupAd = ref(false)

// 获取当前路由路径
const currentRoute = useRoute()

// 弹窗广告配置
const popupAdConfig = ref(getPopupAdConfig())

// 检查是否应该显示弹窗广告
const checkShowPopupAd = () => {
  // 只在客户端执行
  if (import.meta.server) return

  // 检查当前页面是否应该显示弹窗
  if (!shouldShowPopupAd(currentRoute.path)) return

  // 检查显示间隔
  if (!checkPopupInterval()) return

  // 延迟显示弹窗
  setTimeout(() => {
    showPopupAd.value = true
    // 记录显示时间
    recordPopupShown()
  }, popupAdConfig.value.delayShow)
}

// 关闭弹窗广告
const closePopupAd = () => {
  showPopupAd.value = false
}

// 处理弹窗点击
const handlePopupClick = (targetUrl) => {
  console.log('🎯 弹窗广告被点击，目标URL:', targetUrl)
  // 这里可以添加统计代码
  if (typeof gtag !== 'undefined') {
    gtag('event', 'popup_ad_click', {
      'target_url': targetUrl
    })
  }
}



// 监听路由变化，每次路由变化都检查是否显示弹窗
watch(() => currentRoute.path, (newPath) => {
  console.log('🔄 路由变化:', newPath)
  // 延迟一点时间，确保页面已经渲染
  setTimeout(() => {
    checkShowPopupAd()
  }, 500)
}, { immediate: false })

// 页面加载时初始化
onMounted(() => {
  // 初始检查是否显示弹窗广告
  checkShowPopupAd()
})

// 提供全局弹窗方法
provide('globalPopup', {
  showPopupAd: (config) => {
    Object.assign(popupAdConfig.value, config)
    showPopupAd.value = true
  },
  closePopupAd
})
</script>

<style scoped>
/* 动画效果 */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* 主题切换动画 */
.theme-icon-enter-active,
.theme-icon-leave-active {
  transition: all 0.3s ease;
}

.theme-icon-enter-from {
  opacity: 0;
  transform: rotate(-180deg) scale(0.5);
}

.theme-icon-leave-to {
  opacity: 0;
  transform: rotate(180deg) scale(0.5);
}

/* 移动端菜单优化 */
@media (max-width: 768px) {
  /* 确保按钮可点击 */
  #mobile-menu-button,
  #mobile-search-button {
    min-width: 44px;
    min-height: 44px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* 菜单容器优化 */
  #mobile-menu {
    transform: translateZ(0); /* 启用硬件加速 */
  }

  /* 导航栏布局优化 */
  .flex.justify-between.items-center.h-16 {
    gap: 0.5rem;
  }

  /* 确保右侧按钮不被挤压 */
  .flex.items-center.space-x-2.sm\\:space-x-4.flex-shrink-0 {
    min-width: fit-content;
  }
}

/* 通用触摸优化 */
button, a {
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}
</style>

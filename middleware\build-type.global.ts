/**
 * 全局中间件：根据构建类型阻止访问不应该存在的路由
 */

export default defineNuxtRouteMiddleware((to) => {
  // 只在客户端运行
  if (process.server) return

  // 获取构建类型（在构建时注入）
  const buildType = process.env.BUILD_TYPE || 'all'

  // 如果是开发环境或包含所有功能的构建，允许访问所有路由
  if (buildType === 'all' || process.env.NODE_ENV === 'development') {
    return
  }

  const path = to.path

  // 用户前端构建：阻止访问管理后台路由
  if (buildType === 'user') {
    if (path.startsWith('/admin')) {
      // 重定向到首页或显示 404
      throw createError({
        statusCode: 404,
        statusMessage: 'Page Not Found'
      })
    }
  }

  // 管理后台构建：阻止访问用户前端路由
  if (buildType === 'admin') {
    const adminPaths = ['/admin', '/admin/login']
    const isAdminPath = adminPaths.some(adminPath => 
      path === adminPath || path.startsWith(adminPath + '/')
    )
    
    if (!isAdminPath && path !== '/') {
      // 重定向到管理后台登录页
      return navigateTo('/admin/login')
    }
  }
})

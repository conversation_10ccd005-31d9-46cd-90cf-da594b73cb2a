// https://nuxt.com/docs/api/configuration/nuxt-config

// 导入站点配置
import { siteConfig, getServerPort, getServerHost } from './config/site.js'

// 获取构建类型
const buildType = process.env.BUILD_TYPE || 'all'

// 根据构建类型决定忽略的文件
const getIgnorePatterns = () => {
  switch (buildType) {
    case 'user':
      // 用户前端构建：排除所有管理后台相关文件
      return [
        'pages/admin/**',
        'layouts/admin.vue',
        'middleware/auth.ts',
        'composables/useAuth.ts',
        'plugins/auth.client.ts'  // 排除认证插件
      ]
    case 'admin':
      // 管理后台构建：排除用户前端页面，只保留管理相关
      return [
        'pages/index.vue',
        'pages/search.vue',
        'pages/rankings.vue',
        'pages/categories/index.vue',  // 排除用户前端的分类页面
        'pages/categories/[id].vue',   // 排除用户前端的分类详情页面
        'pages/videos/**',             // 排除用户前端的视频页面
        'pages/api-docs.vue',
        'layouts/default.vue'
      ]
    case 'all':
    default:
      // 开发环境：包含所有文件
      return []
  }
}

export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',

  // 调试配置
  // debug: process.env.NODE_ENV === 'development',

  devtools: {
    enabled: true
  },



  // 根据构建类型忽略文件
  ignore: getIgnorePatterns(),

  // 中间件配置
  router: {
    options: {
      // 禁用某些内置中间件的重复检查
      strict: false
    }
  },

  // 开发服务器配置
  devServer: {
    port: buildType === 'admin' ? getServerPort('admin') : getServerPort('user'),
    host: getServerHost()
  },

  // Nitro 配置
  nitro: {
    // 调试配置
    logLevel: process.env.NODE_ENV === 'development' ? 4 : 3, // 开发环境详细日志，生产环境信息日志
    timing: process.env.NODE_ENV === 'development', // 开发环境启用时间信息

    // 解决路径编码问题
    experimental: {
      wasm: false
    },

    // 存储配置 - 解决虚拟模块解析问题
    storage: {},

    // 压缩配置
    compressPublicAssets: true,
    // 预渲染配置
    prerender: {
      crawlLinks: false,
      routes: ['/robots.txt']
    },
    routeRules: {
      // API代理规则 - 适配宝塔+PM2+Nginx部署
      '/api/**': {
        // 开发环境：代理到本地后端端口
        // 生产环境：代理到同服务器的后端端口
        proxy: process.env.NODE_ENV === 'production'
          ? `http://127.0.0.1:${getServerPort('api')}/api/**`  // 生产环境使用127.0.0.1
          : `http://localhost:${getServerPort('api')}/api/**`, // 开发环境使用localhost
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
      },

      // 静态文件预渲染规则
      '/manifest.json': { prerender: true },
      '/robots.txt': { prerender: true },
      '/favicon.ico': { prerender: true },

      // 采集页面特殊处理 - 不代理API，让前端直接访问
      '/collect': {
        // 采集页面不使用API代理，让用户看到真实的API地址
        ssr: true
      }
    }
  },

  // 模块配置
  modules: [
    '@pinia/nuxt',
    '@nuxtjs/tailwindcss',
    '@nuxtjs/i18n',
    '@nuxtjs/sitemap',
    '@nuxtjs/robots',
    '@nuxtjs/seo'
  ],

  // CSS配置
  css: [
    // Font Awesome 本地CSS
    '@fortawesome/fontawesome-free/css/all.min.css',
    // 直播页面样式
    '~/assets/css/live.css',
    '~/assets/css/plyr.css'
    // Plyr CSS 将通过 app.head 配置加载
  ],

  // i18n 国际化配置
  i18n: {
    // 默认语言
    defaultLocale: 'zh-CN',

    // 可用语言列表
    locales: [
      {
        code: 'zh-CN',
        iso: 'zh-CN',
        name: '简体中文',
        file: 'zh-CN.json'
      },
      {
        code: 'zh',
        iso: 'zh-CN',
        name: '简体中文（别名）',
        file: 'zh-CN.json'
      },
      {
        code: 'zh-TW',
        iso: 'zh-TW',
        name: '繁體中文',
        file: 'zh-TW.json'
      },
      {
        code: 'ja-JP',
        iso: 'ja-JP',
        name: '日本語',
        file: 'ja-JP.json'
      },
      {
        code: 'en-US',
        iso: 'en-US',
        name: 'English',
        file: 'en-US.json'
      }
    ],

    // 存放语言文件的目录
    langDir: 'locales',

    // URL策略
    strategy: 'prefix_except_default',

    // 浏览器语言检测
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root',
      alwaysRedirect: false,
      fallbackLocale: 'zh-CN'
    }
  },





  // 应用配置
  app: {
    head: {
      title: siteConfig.basic.siteName,
      titleTemplate: `%s - ${siteConfig.basic.siteName}`,
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no' },
        { name: 'description', content: siteConfig.basic.siteDescription },
        { name: 'keywords', content: siteConfig.basic.keywords },
        { name: 'author', content: siteConfig.basic.siteName },
        { name: 'robots', content: 'index,follow' },
        { name: 'googlebot', content: 'index,follow' },
        { name: 'format-detection', content: 'telephone=no' },
        { name: 'theme-color', content: '#f97316' },
        { name: 'msapplication-TileColor', content: '#f97316' },
        { name: 'apple-mobile-web-app-capable', content: 'yes' },
        { name: 'apple-mobile-web-app-status-bar-style', content: 'default' },
        { name: 'apple-mobile-web-app-title', content: siteConfig.basic.siteName },
        // Open Graph基础标签
        { property: 'og:site_name', content: siteConfig.basic.siteName },
        { property: 'og:type', content: 'website' },
        { property: 'og:locale', content: 'zh_CN' },
        { property: 'og:locale:alternate', content: 'zh_TW' },
        { property: 'og:locale:alternate', content: 'en_US' },
        { property: 'og:locale:alternate', content: 'ja_JP' },
        // Twitter Card基础标签
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:site', content: '@91jspg' },
        // 安全相关标签
        { 'http-equiv': 'X-UA-Compatible', content: 'IE=edge' },
        { name: 'referrer', content: 'no-referrer-when-downgrade' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'apple-touch-icon', href: '/favicon.ico' },
        { rel: 'manifest', href: '/manifest.json' },
        { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
        { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: '' },
        { rel: 'dns-prefetch', href: '//fonts.googleapis.com' },
        { rel: 'dns-prefetch', href: '//fonts.gstatic.com' }
      ],
      script: [
        // 结构化数据 - 网站基本信息
        {
          type: 'application/ld+json',
          innerHTML: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'WebSite',
            name: siteConfig.basic.siteName,
            description: siteConfig.basic.siteDescription,
            url: `https://${siteConfig.basic.domain}`,
            potentialAction: {
              '@type': 'SearchAction',
              target: `https://${siteConfig.basic.domain}/search?q={search_term_string}`,
              'query-input': 'required name=search_term_string'
            }
          })
        }
      ]
    }
  },

  // Sitemap配置 - 简化配置避免外部依赖
  sitemap: {
    // 不使用外部sources，避免构建时的404错误
    exclude: ['/admin/**', '/api/**']
  },

  // Robots配置
  robots: {
    disallow: ['/admin/', '/admin/*']
  },

  // 运行时配置
  runtimeConfig: {
    public: {
      // 内部API代理路径 - 用于大部分页面
      apiBase: '/api',

      // 真实API地址 - 用于采集页面显示给用户
      realApiUrl: process.env.NODE_ENV === 'production'
        ? `https://${siteConfig.basic.domain}/api`  // 生产环境的真实API地址
        : `http://localhost:${getServerPort('api')}/api`, // 开发环境的API地址

      // 后端服务地址 - 用于服务器端调用
      serverApiUrl: process.env.NODE_ENV === 'production'
        ? `http://127.0.0.1:${getServerPort('api')}/api` // 生产环境内网地址
        : `http://localhost:${getServerPort('api')}/api`, // 开发环境地址

      // 管理后台API地址
      adminApiUrl: process.env.ADMIN_API_URL || `http://localhost:${getServerPort('api')}`,

      // 构建类型
      buildType: buildType,

      // 网站基础URL
      baseUrl: process.env.NODE_ENV === 'production'
        ? `https://${siteConfig.basic.domain}`
        : 'http://localhost:3000',

      // 部署环境标识
      deploymentMode: process.env.NODE_ENV === 'production' ? 'nginx-proxy' : 'development'
    }
  },

  // Site Config 配置
  site: {
    url: process.env.NODE_ENV === 'production'
      ? `https://${siteConfig.basic.domain}`
      : `https://${siteConfig.basic.domain}`, // 生产环境URL
    name: siteConfig.basic.siteName,
    description: siteConfig.basic.siteDescription,
    defaultLocale: 'zh-CN'
  },

  // Vite 配置
  vite: {
    // 调试配置
    logLevel: process.env.NODE_ENV === 'development' ? 'info' : 'warn',

    server: {
      fs: {
        allow: ['..']
      }
    },
    build: {
      // 提高chunk大小警告阈值
      chunkSizeWarningLimit: 1000,
      // 启用CSS代码分割
      cssCodeSplit: true,
      // 开发环境启用源映射
      sourcemap: process.env.NODE_ENV === 'development'
    }
  },

  // SSR 配置
  ssr: true
})

{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "build:user": "node scripts/build-user.js", "build:admin": "node scripts/build-admin.js", "build:all": "nuxt build", "verify:user": "node scripts/verify-build.js user", "verify:admin": "node scripts/verify-build.js admin", "clean": "node scripts/clean.js", "test:network": "node scripts/test-network.js", "test:protection": "node scripts/test-api-protection.js", "env:dev": "node scripts/switch-env.js development", "env:prod": "node scripts/switch-env.js production", "fix:mobile": "node scripts/fix-mobile-menu.js", "dev": "nuxt dev", "dev:user": "node scripts/start-with-ports.js user", "dev:admin": "node scripts/start-with-ports.js admin", "start:user": "node scripts/start-production.js user", "start:admin": "node scripts/start-production.js admin", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@fortawesome/fontawesome-free": "^7.0.0", "@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@nuxtjs/i18n": "^10.0.2", "@nuxtjs/robots": "^5.4.0", "@nuxtjs/seo": "^3.1.0", "@nuxtjs/sitemap": "^7.4.3", "@pinia/nuxt": "^0.11.2", "@types/js-cookie": "^3.0.6", "@videojs/http-streaming": "^3.17.0", "bcryptjs": "^3.0.2", "compression": "^1.8.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-validator": "^7.2.1", "flv.js": "^1.6.2", "helmet": "^8.1.0", "hls.js": "^1.6.7", "joi": "^17.13.3", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lucide-vue-next": "^0.525.0", "moment": "^2.30.1", "morgan": "^1.10.1", "multer": "^2.0.2", "nodemailer": "^7.0.5", "nuxt": "^4.0.0", "nuxt-schema-org": "^5.0.6", "pg": "^8.16.3", "plyr": "^3.7.8", "redis": "^5.6.0", "sharp": "^0.34.3", "uuid": "^11.1.0", "video.js": "^8.23.3", "vue": "^3.5.17", "vue-router": "^4.5.1", "winston": "^3.17.0"}, "devDependencies": {"@nuxtjs/tailwindcss": "^6.14.0"}}
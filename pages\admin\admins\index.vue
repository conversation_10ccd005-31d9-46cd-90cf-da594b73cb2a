<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-white">管理员管理</h1>
      <p class="mt-2 text-gray-400">管理系统管理员账户，修改用户名和密码。</p>
    </div>

    <!-- 管理员列表 -->
    <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl">
      <div class="px-6 py-4 border-b border-gray-700/50 flex justify-between items-center">
        <div>
          <h3 class="text-lg font-medium text-white">管理员列表</h3>
          <p class="mt-1 text-sm text-gray-400">共 {{ admins.length }} 个管理员</p>
        </div>
        <button
          @click="showCreateModal = true"
          class="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors"
        >
          添加管理员
        </button>
      </div>

      <div class="p-6">
        <!-- 加载状态 -->
        <div v-if="loading" class="flex items-center justify-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
          <span class="ml-3 text-gray-400">加载中...</span>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="bg-red-900/20 border border-red-500/50 rounded-lg p-4 mb-6">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="text-red-400">{{ error }}</span>
          </div>
        </div>

        <!-- 管理员列表 -->
        <div v-else class="space-y-4">
          <div v-for="admin in admins" :key="admin.id" class="flex items-center justify-between p-4 bg-gray-700/30 rounded-xl hover:bg-gray-700/50 transition-colors">
            <div class="flex items-center">
              <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center mr-4">
                <span class="text-white font-medium text-sm">{{ admin.name.charAt(0) }}</span>
              </div>
              <div>
                <div class="text-sm font-medium text-white">{{ admin.name }}</div>
                <div class="text-sm text-gray-400">@{{ admin.username }}</div>
                <div class="text-xs text-gray-500">最后登录: {{ admin.lastLogin }}</div>
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <span
                :class="[
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                  admin.status === '活跃' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                ]"
              >
                {{ admin.status }}
              </span>
              <button
                @click="editAdmin(admin)"
                class="px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 rounded-lg transition-all duration-200 mr-2"
              >
                编辑
              </button>
              <button
                @click="deleteAdmin(admin)"
                class="px-3 py-1.5 text-xs font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-all duration-200"
              >
                删除
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建管理员模态框 -->
    <div v-show="showCreateModal" class="fixed inset-0 bg-gray-900/75 backdrop-blur-sm overflow-y-auto h-full w-full z-50" @click="closeCreateModal">
      <div class="relative top-20 mx-auto p-6 border w-full max-w-md shadow-2xl rounded-2xl bg-gray-800/95 backdrop-blur-sm border-gray-700/50 m-4" @click.stop>
        <div class="mt-3">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-bold text-white">创建管理员</h3>
            <button @click="closeCreateModal" class="text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-700/50 rounded-lg">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form @submit.prevent="createAdmin" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">姓名</label>
              <input v-model="adminForm.name" type="text" required class="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="请输入管理员姓名" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">用户名</label>
              <input v-model="adminForm.username" type="text" required class="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="请输入用户名" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">邮箱</label>
              <input v-model="adminForm.email" type="email" class="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="请输入邮箱地址" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">密码</label>
              <input v-model="adminForm.password" type="password" required class="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="请输入密码" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">状态</label>
              <select v-model="adminForm.status" class="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                <option value="active">活跃</option>
                <option value="inactive">禁用</option>
              </select>
            </div>

            <div class="flex justify-end space-x-3 pt-4">
              <button type="button" @click="closeCreateModal" class="px-4 py-2 text-sm font-medium text-gray-300 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors">
                取消
              </button>
              <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 rounded-lg transition-all duration-200">
                创建
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 编辑管理员模态框 -->
    <div v-show="showEditModal" class="fixed inset-0 bg-gray-900/75 backdrop-blur-sm overflow-y-auto h-full w-full z-50" @click="closeEditModal">
      <div class="relative top-20 mx-auto p-6 border w-full max-w-md shadow-2xl rounded-2xl bg-gray-800/95 backdrop-blur-sm border-gray-700/50 m-4" @click.stop>
        <div class="mt-3">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-bold text-white">编辑管理员</h3>
            <button @click="closeEditModal" class="text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-700/50 rounded-lg">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form @submit.prevent="saveAdmin" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">姓名</label>
              <input v-model="adminForm.name" type="text" required class="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="请输入管理员姓名" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">用户名</label>
              <input v-model="adminForm.username" type="text" disabled class="w-full px-3 py-2 bg-gray-600/50 border border-gray-600 rounded-lg text-gray-400 cursor-not-allowed" placeholder="用户名不可修改" />
              <p class="text-xs text-gray-500 mt-1">出于安全考虑，用户名不可修改</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">邮箱</label>
              <input v-model="adminForm.email" type="email" class="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="请输入邮箱地址" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">密码</label>
              <input v-model="adminForm.password" type="password" class="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="留空则不修改密码" />
              <p class="text-xs text-gray-500 mt-1">留空则不修改密码</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">状态</label>
              <select v-model="adminForm.status" class="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                <option value="active">活跃</option>
                <option value="inactive">禁用</option>
              </select>
            </div>

            <div class="flex justify-end space-x-3 pt-4">
              <button type="button" @click="closeEditModal" class="px-4 py-2 text-sm font-medium text-gray-300 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors">
                取消
              </button>
              <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 rounded-lg transition-all duration-200">
                保存
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin',
  middleware: 'auth'
})

const showEditModal = ref(false)
const showCreateModal = ref(false)
const editingAdmin = ref(null)
const loading = ref(false)
const error = ref(null)

const adminForm = ref({
  name: '',
  username: '',
  password: '',
  email: '',
  status: 'active'
})

const admins = ref([])

const fetchAdmins = async () => {
  try {
    loading.value = true
    error.value = null

    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth('/api/admin/admins')

    if (response.success) {
      admins.value = response.data.admins.map(admin => ({
        ...admin,
        status: admin.status === 'active' ? '活跃' : '禁用',
        lastLogin: admin.lastLoginAt ? formatLastLogin(admin.lastLoginAt) : '从未登录'
      }))
    }
  } catch (error) {
    console.error('获取管理员列表失败:', error)
    error.value = '获取管理员列表失败'
    if (error.message.includes('认证令牌')) {
      await navigateTo('/admin/login')
    }
  } finally {
    loading.value = false
  }
}

const formatLastLogin = (lastLoginAt) => {
  const now = new Date()
  const loginTime = new Date(lastLoginAt)
  const diffMs = now - loginTime
  const diffMins = Math.floor(diffMs / 60000)
  const diffHours = Math.floor(diffMs / 3600000)
  const diffDays = Math.floor(diffMs / 86400000)

  if (diffMins < 1) return '刚刚'
  if (diffMins < 60) return `${diffMins}分钟前`
  if (diffHours < 24) return `${diffHours}小时前`
  if (diffDays < 7) return `${diffDays}天前`
  return loginTime.toLocaleDateString()
}

const editAdmin = (admin) => {
  editingAdmin.value = admin
  adminForm.value = {
    name: admin.name,
    username: admin.username,
    password: '',
    email: admin.email || '',
    status: admin.status === '活跃' ? 'active' : 'inactive'
  }
  showEditModal.value = true
}

const deleteAdmin = async (admin) => {
  if (confirm(`确定要删除管理员 "${admin.name}" 吗？删除后将无法恢复！`)) {
    try {
      const { apiAdminAuth } = useApi()
      const response = await apiAdminAuth(`/api/admin/admins/${admin.id}`, {
        method: 'DELETE'
      })

      if (response.success) {
        await fetchAdmins()
        alert('管理员删除成功')
      } else {
        alert('删除失败: ' + response.message)
      }
    } catch (error) {
      console.error('删除管理员失败:', error)
      if (error.message.includes('认证令牌')) {
        await navigateTo('/admin/login')
      } else {
        alert('删除失败，请稍后重试')
      }
    }
  }
}

const createAdmin = async () => {
  try {
    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth('/api/admin/admins', {
      method: 'POST',
      body: {
        name: adminForm.value.name,
        username: adminForm.value.username,
        password: adminForm.value.password,
        email: adminForm.value.email,
        status: adminForm.value.status
      }
    })

    if (response.success) {
      await fetchAdmins()
      closeCreateModal()
      alert('管理员创建成功')
    } else {
      alert('创建失败: ' + response.message)
    }
  } catch (error) {
    console.error('创建管理员失败:', error)
    if (error.message.includes('认证令牌')) {
      await navigateTo('/admin/login')
    } else {
      alert('创建失败，请稍后重试')
    }
  }
}

const closeCreateModal = () => {
  showCreateModal.value = false
  adminForm.value = {
    name: '',
    username: '',
    password: '',
    email: '',
    status: 'active'
  }
}

const closeEditModal = () => {
  showEditModal.value = false
  editingAdmin.value = null
  adminForm.value = {
    name: '',
    username: '',
    password: '',
    email: '',
    status: 'active'
  }
}

const saveAdmin = async () => {
  try {
    const updateData = {
      name: adminForm.value.name,
      email: adminForm.value.email,
      status: adminForm.value.status
    }

    // 只有在输入了密码时才包含密码字段
    if (adminForm.value.password && adminForm.value.password.trim()) {
      updateData.password = adminForm.value.password
    }

    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth(`/api/admin/admins/${editingAdmin.value.id}`, {
      method: 'PUT',
      body: updateData
    })

    if (response.success) {
      await fetchAdmins()
      closeEditModal()
      alert('管理员信息更新成功')
    } else {
      alert('更新失败: ' + response.message)
    }
  } catch (error) {
    console.error('更新管理员失败:', error)
    if (error.message.includes('认证令牌')) {
      await navigateTo('/admin/login')
    } else {
      alert('更新失败，请稍后重试')
    }
  }
}

onMounted(() => {
  fetchAdmins()
})

useHead({
  title: '管理员管理 - 91JSPG.COM 管理后台'
})
</script>

<template>
  <div>
    <!-- 页面标题和操作 -->
    <div class="sm:flex sm:items-center sm:justify-between mb-8">
      <div>
        <h1 class="text-3xl font-bold text-white">API管理</h1>
        <p class="mt-2 text-gray-400">查看所有API接口文档，管理Python脚本上传接口的密钥与权限。</p>
      </div>
      <div class="mt-4 sm:mt-0">
        <button
          @click="showAddUploadModal = true"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200 shadow-lg shadow-orange-500/25"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          添加上传API密钥
        </button>
      </div>
    </div>

    <!-- API统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl p-6 hover:scale-105 transition-transform duration-200">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-400">API接口总数</p>
            <p class="text-2xl font-bold text-white">{{ totalApis }}</p>
          </div>
        </div>
      </div>

      <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl p-6 hover:scale-105 transition-transform duration-200">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-400">上传API密钥</p>
            <p class="text-2xl font-bold text-white">{{ uploadKeys }}</p>
          </div>
        </div>
      </div>

      <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl p-6 hover:scale-105 transition-transform duration-200">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-400">今日请求</p>
            <p class="text-2xl font-bold text-white">{{ todayRequests }}</p>
          </div>
        </div>
      </div>

      <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl p-6 hover:scale-105 transition-transform duration-200">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg shadow-purple-500/25">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-400">平均响应时间</p>
            <p class="text-2xl font-bold text-white">{{ avgResponseTime }}ms</p>
          </div>
        </div>
      </div>
    </div>

    <!-- API接口文档 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      <!-- 前端API接口文档 -->
      <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl">
        <div class="px-6 py-4 border-b border-gray-700/50">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 class="text-lg font-medium text-white">前端API接口文档</h3>
            </div>
            <span class="text-sm text-gray-400">{{ frontendApis.length }} 个接口</span>
          </div>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div
              v-for="api in frontendApis"
              :key="api.id"
              class="p-4 bg-gray-700/30 rounded-xl hover:bg-gray-700/50 transition-colors"
            >
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium bg-blue-500/20 text-blue-400 mr-3">
                    {{ api.method }}
                  </span>
                  <div class="text-sm font-medium text-white">{{ api.name }}</div>
                </div>
                <span
                  :class="[
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    api.status === '正常' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                  ]"
                >
                  {{ api.status }}
                </span>
              </div>
              <div class="text-sm text-gray-400 font-mono mb-2">{{ api.endpoint }}</div>
              <div class="text-xs text-gray-500">{{ api.description }}</div>
              <div class="text-xs text-gray-500 mt-1">请求量: {{ api.requests }} 次/日</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Python上传API接口文档 -->
      <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl">
        <div class="px-6 py-4 border-b border-gray-700/50">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 class="text-lg font-medium text-white">Python上传API接口文档</h3>
            </div>
            <span class="text-sm text-gray-400">{{ uploadApis.length }} 个接口 (需要密钥)</span>
          </div>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div
              v-for="api in uploadApis"
              :key="api.id"
              class="p-4 bg-gray-700/30 rounded-xl hover:bg-gray-700/50 transition-colors"
            >
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium bg-orange-500/20 text-orange-400 mr-3">
                    {{ api.method }}
                  </span>
                  <div class="text-sm font-medium text-white">{{ api.name }}</div>
                </div>
                <span
                  :class="[
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    api.status === '正常' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                  ]"
                >
                  {{ api.status }}
                </span>
              </div>
              <div class="text-sm text-gray-400 font-mono mb-2">{{ api.endpoint }}</div>
              <div class="text-xs text-gray-500">{{ api.description }}</div>
              <div class="text-xs text-gray-500 mt-1">请求量: {{ api.requests }} 次/日 | 🔑 需要API密钥</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Python上传API密钥管理 -->
    <div class="max-w-4xl">
      <!-- Python上传API密钥 -->
      <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl">
        <div class="px-6 py-4 border-b border-gray-700/50">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-white">Python上传API密钥</h3>
            <button
              @click="showAddUploadModal = true"
              class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-lg text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200"
            >
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              添加密钥
            </button>
          </div>
        </div>
        <div class="p-6">
          <!-- 加载状态 -->
          <div v-if="loading" class="flex items-center justify-center py-12">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
            <span class="ml-3 text-gray-400">加载中...</span>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="error" class="bg-red-900/20 border border-red-500/50 rounded-lg p-4 mb-6">
            <div class="flex items-center">
              <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span class="text-red-400">{{ error }}</span>
            </div>
          </div>

          <!-- API密钥列表 -->
          <div v-else class="space-y-4">
            <div
              v-for="key in apiKeys"
              :key="key.id"
              class="flex items-center justify-between p-4 bg-gray-700/30 rounded-xl hover:bg-gray-700/50 transition-colors"
            >
              <div class="flex items-center">
                <div class="w-10 h-10 bg-orange-500/20 rounded-lg flex items-center justify-center mr-4">
                  <svg class="w-5 h-5 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                  </svg>
                </div>
                <div>
                  <div class="text-sm font-medium text-white">{{ key.name }}</div>
                  <div class="text-xs text-gray-400 font-mono">{{ key.maskedKey }}</div>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <span
                  :class="[
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    key.status === 'active' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                  ]"
                >
                  {{ key.status === 'active' ? '活跃' : '禁用' }}
                </span>
                <button
                  @click="toggleKeyVisibility(key)"
                  class="text-gray-400 hover:text-white transition-colors p-1"
                >
                  <svg v-if="key.masked" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                  </svg>
                </button>
                <button
                  @click="deleteApiKey(key)"
                  class="text-gray-400 hover:text-red-400 transition-colors p-1"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加上传API密钥模态框 -->
    <div
      v-show="showAddUploadModal"
      class="fixed inset-0 bg-gray-900/75 backdrop-blur-sm overflow-y-auto h-full w-full z-50"
      @click="closeModal"
    >
      <div
        class="relative top-20 mx-auto p-6 border w-full max-w-md shadow-2xl rounded-2xl bg-gray-800/95 backdrop-blur-sm border-gray-700/50 m-4"
        @click.stop
      >
        <div class="mt-3">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-bold text-white">添加上传API密钥</h3>
            <button
              @click="closeModal"
              class="text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-700/50 rounded-lg"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form @submit.prevent="saveApiKey">
            <div class="space-y-6">
              <!-- 密钥名称 -->
              <div>
                <label for="keyName" class="block text-sm font-medium text-gray-300 mb-2">
                  密钥名称 <span class="text-red-400">*</span>
                </label>
                <input
                  id="keyName"
                  v-model="apiKeyForm.name"
                  type="text"
                  required
                  class="block w-full px-4 py-3 border border-gray-600/50 placeholder-gray-400 text-white bg-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
                  placeholder="请输入密钥名称，如：Python脚本主密钥"
                >
              </div>

              <!-- 密钥描述 -->
              <div>
                <label for="keyDescription" class="block text-sm font-medium text-gray-300 mb-2">
                  密钥描述
                </label>
                <textarea
                  id="keyDescription"
                  v-model="apiKeyForm.description"
                  rows="3"
                  class="block w-full px-4 py-3 border border-gray-600/50 placeholder-gray-400 text-white bg-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
                  placeholder="请输入密钥用途描述"
                ></textarea>
              </div>

              <!-- 生成的密钥显示 -->
              <div v-if="generatedKey">
                <label class="block text-sm font-medium text-gray-300 mb-2">
                  生成的API密钥
                </label>
                <div class="p-4 bg-gray-700/50 rounded-xl border border-gray-600/50">
                  <div class="flex items-center justify-between">
                    <code class="text-sm text-green-400 font-mono break-all">{{ generatedKey }}</code>
                    <button
                      type="button"
                      @click="copyToClipboard(generatedKey)"
                      class="ml-2 text-gray-400 hover:text-white transition-colors p-1"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                    </button>
                  </div>
                  <p class="text-xs text-yellow-400 mt-2">⚠️ 请立即复制并保存此密钥，关闭弹窗后将无法再次查看！</p>
                </div>
              </div>
            </div>

            <div class="flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-700/50">
              <button
                type="button"
                @click="closeModal"
                class="px-6 py-3 border border-gray-600/50 rounded-xl text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-700/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200"
              >
                {{ generatedKey ? '关闭' : '取消' }}
              </button>
              <button
                v-if="!generatedKey"
                type="submit"
                class="px-6 py-3 border border-transparent rounded-xl text-sm font-medium text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200 shadow-lg shadow-orange-500/25"
              >
                生成密钥
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 设置页面元信息
definePageMeta({
  layout: 'admin',
  middleware: 'auth',
  title: 'API管理 - 91JSPG.COM 管理后台'
})

// 响应式数据
const showAddUploadModal = ref(false)
const loading = ref(false)
const error = ref(null)
const generatedKey = ref('')

// API密钥数据
const apiKeys = ref([])
const apiStats = ref({
  totalKeys: 0,
  activeKeys: 0,
  todayRequests: 0,
  avgResponseTime: 0
})

// 表单数据
const apiKeyForm = ref({
  name: '',
  description: ''
})

// 计算属性
const totalApis = computed(() => frontendApis.value.length + uploadApis.value.length)
const uploadKeys = computed(() => apiStats.value.activeKeys)
const todayRequests = computed(() => apiStats.value.todayRequests)
const avgResponseTime = computed(() => apiStats.value.avgResponseTime)

// 前端API接口数据
const frontendApis = ref([])

// Python上传API接口数据
const uploadApis = ref([])

// 获取前端API文档
const fetchFrontendApiDocs = async () => {
  try {
    const { getToken } = useAuth()
    const token = getToken()
    if (!token) {
      throw new Error('未找到认证令牌')
    }

    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth('/api/admin/api-docs/frontend')

    if (response.success) {
      frontendApis.value = response.data.apis.map(api => ({
        ...api,
        status: api.status === 'active' ? '正常' : '异常'
      }))
    }
  } catch (error) {
    console.error('获取前端API文档失败:', error)
    if (error.message.includes('认证令牌')) {
      await navigateTo('/admin/login')
    }
  }
}

// 获取Python上传API文档
const fetchUploadApiDocs = async () => {
  try {
    const { getToken } = useAuth()
    const token = getToken()
    if (!token) {
      throw new Error('未找到认证令牌')
    }

    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth('/api/admin/api-docs/upload')

    if (response.success) {
      uploadApis.value = response.data.apis.map(api => ({
        ...api,
        status: api.status === 'active' ? '正常' : '异常'
      }))
    }
  } catch (error) {
    console.error('获取上传API文档失败:', error)
    if (error.message.includes('认证令牌')) {
      await navigateTo('/admin/login')
    }
  }
}

// 获取API密钥列表
const fetchApiKeys = async () => {
  try {
    loading.value = true
    error.value = null

    const { getToken } = useAuth()
    const token = getToken()
    if (!token) {
      throw new Error('未找到认证令牌')
    }

    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth('/api/admin/api-keys')

    if (response.success) {
      apiKeys.value = response.data.apiKeys.map(key => ({
        ...key,
        masked: true,
        maskedKey: key.keyValue ? maskApiKey(key.keyValue) : '',
        lastUsed: key.lastUsedAt ? new Date(key.lastUsedAt).toLocaleString() : '从未使用'
      }))

      // 更新统计数据
      apiStats.value.totalKeys = apiKeys.value.length
      apiStats.value.activeKeys = apiKeys.value.filter(key => key.status === 'active').length
    }
  } catch (error) {
    console.error('获取API密钥列表失败:', error)
    error.value = '获取API密钥列表失败'
    if (error.message.includes('认证令牌')) {
      await navigateTo('/admin/login')
    }
  } finally {
    loading.value = false
  }
}

// 掩码API密钥
const maskApiKey = (keyValue) => {
  if (!keyValue || keyValue.length < 20) return keyValue
  const prefix = keyValue.substring(0, 16) // 显示前16位
  const suffix = keyValue.substring(keyValue.length - 16) // 显示后16位
  const maskedLength = keyValue.length - 32 // 中间需要掩码的长度
  const masked = '*'.repeat(Math.max(maskedLength, 8)) // 至少8个星号
  return `${prefix}${masked}${suffix}`
}

// 切换密钥可见性
const toggleKeyVisibility = (key) => {
  key.masked = !key.masked
  if (key.masked) {
    key.maskedKey = maskApiKey(key.keyValue)
  } else {
    key.maskedKey = key.keyValue
  }
}

// 删除API密钥
const deleteApiKey = async (key) => {
  if (confirm('确定要删除这个API密钥吗？删除后将无法恢复！')) {
    try {
      const { getToken } = useAuth()
      const token = getToken()
      if (!token) {
        throw new Error('未找到认证令牌')
      }

      const { apiAdminAuth } = useApi()
      const response = await apiAdminAuth(`/api/admin/api-keys/${key.id}`, {
        method: 'DELETE'
      })

      if (response.success) {
        await fetchApiKeys() // 重新获取列表
      } else {
        alert('删除失败: ' + response.message)
      }
    } catch (error) {
      console.error('删除API密钥失败:', error)
      if (error.message.includes('认证令牌')) {
        await navigateTo('/admin/login')
      } else {
        alert('删除失败，请稍后重试')
      }
    }
  }
}

// 保存API密钥
const saveApiKey = async () => {
  try {
    const { getToken } = useAuth()
    const token = getToken()
    if (!token) {
      throw new Error('未找到认证令牌')
    }

    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth('/api/admin/api-keys', {
      method: 'POST',
      body: {
        name: apiKeyForm.value.name,
        description: apiKeyForm.value.description,
        status: 'active'
      }
    })

    if (response.success) {
      generatedKey.value = response.data.keyValue
      await fetchApiKeys() // 重新获取列表
      closeModal()
    } else {
      alert('创建失败: ' + response.message)
    }
  } catch (error) {
    console.error('创建API密钥失败:', error)
    if (error.message.includes('认证令牌')) {
      await navigateTo('/admin/login')
    } else {
      alert('创建失败，请稍后重试')
    }
  }
}

// 关闭模态框
const closeModal = () => {
  showAddUploadModal.value = false
  generatedKey.value = ''
  apiKeyForm.value = {
    name: '',
    description: ''
  }
}

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    alert('API密钥已复制到剪贴板！')
  } catch (err) {
    console.error('复制失败:', err)
    alert('复制失败，请手动复制')
  }
}

// 页面挂载时获取数据
onMounted(() => {
  fetchApiKeys()
  fetchFrontendApiDocs()
  fetchUploadApiDocs()
})

// 页面标题
useHead({
  title: 'API管理 - 91JSPG.COM 管理后台'
})
</script>

<template>
  <div>
    <!-- 页面标题和操作 -->
    <div class="sm:flex sm:items-center sm:justify-between mb-8">
      <div>
        <h1 class="text-3xl font-bold text-white">分类管理</h1>
        <p class="mt-2 text-gray-400">管理视频分类，包括添加、编辑和删除分类。</p>
      </div>
      <div class="mt-4 sm:mt-0">
        <button
          @click="showAddModal = true"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          添加分类
        </button>
      </div>
    </div>

    <!-- 搜索框 -->
    <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl mb-6">
      <div class="p-6">
        <div class="max-w-md">
          <label for="search" class="block text-sm font-medium text-gray-300 mb-2">搜索分类</label>
          <div class="relative">
            <input
              id="search"
              v-model="searchQuery"
              type="text"
              placeholder="搜索分类名称..."
              class="block w-full pl-10 pr-3 py-3 border border-gray-600/50 rounded-xl leading-5 bg-gray-700/50 placeholder-gray-400 text-white focus:outline-none focus:placeholder-gray-300 focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
            >
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分类列表 -->
    <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl">
      <div class="px-6 py-4 border-b border-gray-700/50">
        <h3 class="text-lg font-medium text-white">分类列表</h3>
        <p class="mt-1 text-sm text-gray-400">共 {{ totalCategories }} 个分类</p>
      </div>

      <div class="overflow-hidden">
        <table class="min-w-full divide-y divide-gray-700/50">
          <thead class="bg-gray-700/30">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">分类名称</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">描述</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">视频数量</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">状态</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">创建时间</th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-700/50">
            <tr v-for="category in paginatedCategories" :key="category.id" class="hover:bg-gray-700/30 transition-colors">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-10 h-10 bg-orange-500/20 rounded-lg flex items-center justify-center mr-4">
                    <span class="text-orange-400 font-medium text-sm">{{ category.name.charAt(0) }}</span>
                  </div>
                  <div>
                    <div class="text-sm font-medium text-white">{{ category.name }}</div>
                    <div class="text-sm text-gray-400">{{ category.slug }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-gray-300 max-w-xs">{{ category.description }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{{ category.videoCount }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  :class="[
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    category.status === '启用' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                  ]"
                >
                  {{ category.status }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">{{ category.createdAt }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                  <button
                    @click="editCategory(category)"
                    class="text-orange-400 hover:text-orange-300 transition-colors"
                  >
                    编辑
                  </button>
                  <button
                    @click="toggleCategoryStatus(category)"
                    :class="[
                      'hover:text-white transition-colors',
                      category.status === '启用' ? 'text-red-400 hover:text-red-300' : 'text-green-400 hover:text-green-300'
                    ]"
                  >
                    {{ category.status === '启用' ? '禁用' : '启用' }}
                  </button>
                  <button
                    @click="deleteCategory(category)"
                    class="text-red-600 hover:text-red-900"
                  >
                    删除
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="bg-gray-700/30 px-4 py-3 flex items-center justify-between border-t border-gray-700/50 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <button
            @click="previousPage"
            :disabled="currentPage === 1"
            class="relative inline-flex items-center px-4 py-2 border border-gray-600/50 text-sm font-medium rounded-xl text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            上一页
          </button>
          <button
            @click="nextPage"
            :disabled="currentPage === totalPages"
            class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-600/50 text-sm font-medium rounded-xl text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            下一页
          </button>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-400">
              显示第 <span class="font-medium text-white">{{ (currentPage - 1) * pageSize + 1 }}</span> 到
              <span class="font-medium text-white">{{ Math.min(currentPage * pageSize, totalCategories) }}</span> 条，
              共 <span class="font-medium text-white">{{ totalCategories }}</span> 条记录
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-xl shadow-sm -space-x-px">
              <button
                @click="previousPage"
                :disabled="currentPage === 1"
                class="relative inline-flex items-center px-2 py-2 rounded-l-xl border border-gray-600/50 bg-gray-700/50 text-sm font-medium text-gray-400 hover:bg-gray-600/50 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </button>
              <button
                v-for="page in visiblePages"
                :key="page"
                @click="goToPage(page)"
                :class="[
                  'relative inline-flex items-center px-4 py-2 border text-sm font-medium transition-colors',
                  page === currentPage
                    ? 'z-10 bg-orange-500/20 border-orange-500/50 text-orange-400'
                    : 'bg-gray-700/50 border-gray-600/50 text-gray-400 hover:bg-gray-600/50 hover:text-white'
                ]"
              >
                {{ page }}
              </button>
              <button
                @click="nextPage"
                :disabled="currentPage === totalPages"
                class="relative inline-flex items-center px-2 py-2 rounded-r-xl border border-gray-600/50 bg-gray-700/50 text-sm font-medium text-gray-400 hover:bg-gray-600/50 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑分类模态框 -->
    <div
      v-show="showAddModal || showEditModal"
      class="fixed inset-0 bg-gray-900/75 backdrop-blur-sm overflow-y-auto h-full w-full z-50"
      @click="closeModal"
    >
      <div
        class="relative top-20 mx-auto p-6 border w-full max-w-md shadow-2xl rounded-2xl bg-gray-800/95 backdrop-blur-sm border-gray-700/50 m-4"
        @click.stop
      >
        <div class="mt-3">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-bold text-white">
              {{ showAddModal ? '添加分类' : '编辑分类' }}
            </h3>
            <button
              @click="closeModal"
              class="text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-700/50 rounded-lg"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <form @submit.prevent="saveCategory">
            <div class="space-y-6">
              <!-- 分类名称 -->
              <div>
                <label for="categoryName" class="block text-sm font-medium text-gray-300 mb-2">
                  分类名称 <span class="text-red-400">*</span>
                </label>
                <input
                  id="categoryName"
                  v-model="categoryForm.name"
                  type="text"
                  required
                  class="block w-full px-4 py-3 border border-gray-600/50 placeholder-gray-400 text-white bg-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
                  placeholder="请输入分类名称"
                >
              </div>

              <!-- 分类描述 -->
              <div>
                <label for="categoryDescription" class="block text-sm font-medium text-gray-300 mb-2">
                  分类描述
                </label>
                <textarea
                  id="categoryDescription"
                  v-model="categoryForm.description"
                  rows="4"
                  class="block w-full px-4 py-3 border border-gray-600/50 placeholder-gray-400 text-white bg-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
                  placeholder="请输入分类描述"
                ></textarea>
              </div>

              <!-- 状态 -->
              <div>
                <label for="categoryStatus" class="block text-sm font-medium text-gray-300 mb-2">
                  状态
                </label>
                <select
                  id="categoryStatus"
                  v-model="categoryForm.status"
                  class="block w-full px-4 py-3 border border-gray-600/50 text-white bg-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
                >
                  <option value="启用">启用</option>
                  <option value="禁用">禁用</option>
                </select>
              </div>
            </div>

            <div class="flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-700/50">
              <button
                type="button"
                @click="closeModal"
                class="px-6 py-3 border border-gray-600/50 rounded-xl text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-700/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200"
              >
                取消
              </button>
              <button
                type="submit"
                class="px-6 py-3 border border-transparent rounded-xl text-sm font-medium text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200 shadow-lg shadow-orange-500/25"
              >
                {{ showAddModal ? '添加' : '保存' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 设置布局和认证
definePageMeta({
  layout: 'admin',
  middleware: 'auth'
})

// 响应式数据
const searchQuery = ref('')
const showAddModal = ref(false)
const showEditModal = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const editingCategory = ref(null)
const loading = ref(false)
const categories = ref([])
const totalCategories = ref(0)

// 分类表单
const categoryForm = ref({
  name: '',
  description: '',
  status: '启用'
})

// 获取分类列表
const fetchCategories = async () => {
  try {
    loading.value = true

    const { getToken } = useAuth()
    const token = getToken()
    if (!token) {
      throw new Error('未找到认证令牌')
    }

    const params = new URLSearchParams({
      page: currentPage.value.toString(),
      limit: pageSize.value.toString()
    })

    if (searchQuery.value) {
      params.append('search', searchQuery.value)
    }

    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth(`/api/admin/categories?${params}`)

    if (response.success) {
      categories.value = response.data.categories.map(category => ({
        id: category.id,
        name: category.name,
        slug: category.slug,
        description: category.description || '',
        videoCount: category.videoCount || 0,
        status: category.status === 'active' ? '启用' : '禁用',
        createdAt: category.createdAt ? new Date(category.createdAt).toLocaleDateString() : ''
      }))

      if (response.data.pagination) {
        totalCategories.value = response.data.pagination.total
      }
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    if (error.message.includes('认证令牌')) {
      await navigateTo('/admin/login')
    }
  } finally {
    loading.value = false
  }
}

// 计算属性
const filteredCategories = computed(() => {
  return categories.value
})

const totalPages = computed(() => Math.ceil(totalCategories.value / pageSize.value))

const paginatedCategories = computed(() => {
  return categories.value
})

const visiblePages = computed(() => {
  const pages = []
  const start = Math.max(1, currentPage.value - 2)
  const end = Math.min(totalPages.value, currentPage.value + 2)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// 方法
const editCategory = (category) => {
  editingCategory.value = category
  categoryForm.value = {
    name: category.name,
    description: category.description,
    status: category.status
  }
  showEditModal.value = true
}

const deleteCategory = async (category) => {
  if (confirm(`确定要删除分类 "${category.name}" 吗？此操作将影响 ${category.videoCount} 个视频。`)) {
    try {
      const { getToken } = useAuth()
      const token = getToken()
      if (!token) {
        throw new Error('未找到认证令牌')
      }

      const { apiAdminAuth } = useApi()
      const response = await apiAdminAuth(`/api/admin/categories/${category.id}`, {
        method: 'DELETE'
      })

      if (response.success) {
        await fetchCategories()
      } else {
        alert('删除失败: ' + response.message)
      }
    } catch (error) {
      console.error('删除分类失败:', error)
      if (error.message.includes('认证令牌')) {
        await navigateTo('/admin/login')
      } else {
        alert('删除失败，请稍后重试')
      }
    }
  }
}

const toggleCategoryStatus = async (category) => {
  try {
    const { getToken } = useAuth()
    const token = getToken()
    if (!token) {
      throw new Error('未找到认证令牌')
    }

    const newStatus = category.status === '启用' ? 'inactive' : 'active'
    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth(`/api/admin/categories/${category.id}`, {
      method: 'PUT',
      body: {
        status: newStatus
      }
    })

    if (response.success) {
      category.status = category.status === '启用' ? '禁用' : '启用'
    } else {
      alert('状态更新失败: ' + response.message)
    }
  } catch (error) {
    console.error('更新分类状态失败:', error)
    if (error.message.includes('认证令牌')) {
      await navigateTo('/admin/login')
    } else {
      alert('状态更新失败，请稍后重试')
    }
  }
}

const saveCategory = async () => {
  try {
    const { getToken } = useAuth()
    const token = getToken()
    if (!token) {
      throw new Error('未找到认证令牌')
    }

    if (showAddModal.value) {
      // 添加新分类
      const { apiAdminAuth } = useApi()
      const response = await apiAdminAuth('/api/admin/categories', {
        method: 'POST',
        body: {
          name: categoryForm.value.name,
          description: categoryForm.value.description,
          status: categoryForm.value.status === '启用' ? 'active' : 'inactive'
        }
      })

      if (response.success) {
        await fetchCategories()
        closeModal()
      } else {
        alert('添加失败: ' + response.message)
      }
    } else {
      // 编辑现有分类
      if (editingCategory.value) {
        const { apiAdminAuth } = useApi()
        const response = await apiAdminAuth(`/api/admin/categories/${editingCategory.value.id}`, {
          method: 'PUT',
          body: {
            name: categoryForm.value.name,
            description: categoryForm.value.description,
            status: categoryForm.value.status === '启用' ? 'active' : 'inactive'
          }
        })

        if (response.success) {
          await fetchCategories()
          closeModal()
        } else {
          alert('更新失败: ' + response.message)
        }
      }
    }
  } catch (error) {
    console.error('保存分类失败:', error)
    if (error.message.includes('认证令牌')) {
      await navigateTo('/admin/login')
    } else {
      alert('保存失败，请稍后重试')
    }
  }
}

const closeModal = () => {
  showAddModal.value = false
  showEditModal.value = false
  editingCategory.value = null
  categoryForm.value = {
    name: '',
    description: '',
    status: '启用'
  }
}

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
    fetchCategories()
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
    fetchCategories()
  }
}

const goToPage = (page) => {
  currentPage.value = page
  fetchCategories()
}

// 搜索防抖
let searchTimeout = null
const debouncedSearch = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    currentPage.value = 1
    fetchCategories()
  }, 500)
}

// 监听搜索条件变化
watch(searchQuery, () => {
  debouncedSearch()
})

// 页面挂载时获取数据
onMounted(() => {
  fetchCategories()
})

// 页面标题
useHead({
  title: '分类管理 - 91JSPG.COM 管理后台'
})
</script>

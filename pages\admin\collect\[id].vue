<template>
  <div>
    <!-- 页面标题和返回按钮 -->
    <div class="mb-8 flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <button
          @click="$router.back()"
          class="inline-flex items-center px-3 py-2 border border-gray-600 text-sm font-medium rounded-lg text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
          返回
        </button>
        <div>
          <h1 class="text-3xl font-bold text-white">{{ resource?.name || '资源库详情' }}</h1>
          <p class="mt-2 text-gray-400">{{ resource?.url }}</p>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="flex items-center space-x-3">
        <button
          @click="testConnection"
          class="inline-flex items-center px-4 py-2 border border-blue-600 text-sm font-medium rounded-lg text-blue-400 bg-blue-600/10 hover:bg-blue-600/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          测试连接
        </button>
        <button
          @click="startCollect"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200 shadow-lg shadow-orange-500/25"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          开始采集
        </button>
        <NuxtLink
          :to="`/admin/collect/logs?sourceId=${resourceId}`"
          class="inline-flex items-center px-4 py-2 border border-green-600 text-sm font-medium rounded-lg text-green-400 bg-green-600/10 hover:bg-green-600/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          本源日志
        </NuxtLink>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
      <span class="ml-3 text-gray-400">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="bg-red-900/20 border border-red-500/50 rounded-lg p-4 mb-6">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="text-red-400 font-medium">{{ error }}</span>
        <button @click="fetchResourceDetail" class="ml-auto px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors">
          重试
        </button>
      </div>
    </div>

    <!-- 主要内容 -->
    <div v-else>
      <!-- 资源库基本信息 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- 总视频数 -->
        <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 110 2h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6H3a1 1 0 110-2h4z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-400">总视频数</p>
              <p class="text-2xl font-bold text-white">{{ stats.totalVideos.toLocaleString() }}</p>
            </div>
          </div>
        </div>

        <!-- 今日采集 -->
        <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-400">今日采集</p>
              <p class="text-2xl font-bold text-white">{{ stats.todayCollected }}</p>
              <p class="text-xs text-green-400">+{{ stats.todayIncrease }} 较昨日</p>
            </div>
          </div>
        </div>

        <!-- 成功率 -->
        <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-500/20 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-400">成功率</p>
              <p class="text-2xl font-bold text-white">{{ stats.successRate }}%</p>
            </div>
          </div>
        </div>

        <!-- 连接状态 -->
        <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div 
                :class="[
                  'w-8 h-8 rounded-lg flex items-center justify-center',
                  stats.status === 'online' ? 'bg-green-500/20' : 'bg-red-500/20'
                ]"
              >
                <svg 
                  :class="[
                    'w-5 h-5',
                    stats.status === 'online' ? 'text-green-400' : 'text-red-400'
                  ]"
                  fill="none" stroke="currentColor" viewBox="0 0 24 24"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-400">连接状态</p>
              <p 
                :class="[
                  'text-2xl font-bold',
                  stats.status === 'online' ? 'text-green-400' : 'text-red-400'
                ]"
              >
                {{ stats.status === 'online' ? '在线' : '离线' }}
              </p>
              <p class="text-xs text-gray-400">{{ stats.lastCheck }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 分类数据统计 -->
      <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl mb-8">
        <div class="px-6 py-4 border-b border-gray-700/50">
          <h3 class="text-lg font-medium text-white">分类数据统计</h3>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            <div
              v-for="category in categories"
              :key="category.type_id"
              class="bg-gray-700/30 rounded-lg p-4 hover:bg-gray-700/50 transition-colors cursor-pointer"
              @click="filterByCategory(category.type_id)"
            >
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-white font-medium">{{ category.type_name }}</h4>
                  <p class="text-sm text-gray-400">ID: {{ category.type_id }}</p>
                  <p v-if="category.type_pid > 0" class="text-xs text-gray-500">父分类: {{ category.type_pid }}</p>
                </div>
                <div class="text-right">
                  <button
                    @click.stop="filterByCategory(category.type_id)"
                    class="px-2 py-1 bg-orange-600 hover:bg-orange-700 text-white text-xs rounded transition-colors"
                  >
                    查看
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 视频数据列表 -->
      <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl mb-8">
        <div class="px-6 py-4 border-b border-gray-700/50">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-white">视频数据列表</h3>
            <div class="flex items-center space-x-3">
              <select v-model="selectedTypeId" @change="fetchVideoList" class="px-3 py-1 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-orange-500">
                <option value="">全部分类</option>
                <option v-for="category in categories" :key="category.type_id" :value="category.type_id">
                  {{ category.type_name }}
                </option>
              </select>
              <button
                @click="fetchVideoList(1)"
                class="px-3 py-1 bg-gray-600 hover:bg-gray-500 text-white text-sm rounded-lg transition-colors"
              >
                刷新
              </button>
            </div>
          </div>
        </div>
        <div class="p-6">
          <!-- 分页信息 -->
          <div v-if="videoData" class="mb-4 flex items-center justify-between text-sm text-gray-400">
            <div>
              共 {{ videoData.total }} 条数据，第 {{ videoData.page }} / {{ videoData.pagecount }} 页
            </div>
            <div class="flex items-center space-x-2">
              <button
                @click="changePage(parseInt(videoData.page) - 1)"
                :disabled="parseInt(videoData.page) <= 1"
                class="px-3 py-1 bg-gray-600 hover:bg-gray-500 disabled:bg-gray-700 disabled:cursor-not-allowed text-white text-sm rounded-lg transition-colors"
              >
                上一页
              </button>
              <span class="px-3 py-1">{{ videoData.page }}</span>
              <button
                @click="changePage(parseInt(videoData.page) + 1)"
                :disabled="parseInt(videoData.page) >= parseInt(videoData.pagecount)"
                class="px-3 py-1 bg-gray-600 hover:bg-gray-500 disabled:bg-gray-700 disabled:cursor-not-allowed text-white text-sm rounded-lg transition-colors"
              >
                下一页
              </button>
            </div>
          </div>

          <!-- 视频列表 -->
          <div v-if="videoList.length > 0" class="space-y-3">
            <div
              v-for="video in videoList"
              :key="video.vod_id"
              class="bg-gray-700/30 rounded-lg p-4 hover:bg-gray-700/50 transition-colors"
            >
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <h4 class="text-white font-medium mb-1">{{ video.vod_name }}</h4>
                  <div class="flex items-center space-x-4 text-sm text-gray-400">
                    <span>ID: {{ video.vod_id }}</span>
                    <span>分类: {{ video.type_name }}</span>
                    <span>更新时间: {{ video.vod_time }}</span>
                    <span v-if="video.vod_remarks">备注: {{ video.vod_remarks }}</span>
                  </div>
                  <div v-if="video.vod_play_from" class="mt-2">
                    <span class="text-xs text-orange-400">播放源: {{ video.vod_play_from }}</span>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <button
                    @click="viewVideoDetail(video)"
                    class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors"
                  >
                    查看详情
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-else-if="loadingVideos" class="text-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-3"></div>
            <p class="text-gray-400">加载视频数据中...</p>
          </div>

          <!-- 空状态 -->
          <div v-else class="text-center py-8">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            <h3 class="text-md font-medium text-gray-300 mb-1">暂无视频数据</h3>
            <p class="text-gray-400 text-sm">接口暂无数据或连接失败</p>
          </div>
        </div>
      </div>

      <!-- 接口信息 -->
      <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl">
        <div class="px-6 py-4 border-b border-gray-700/50">
          <h3 class="text-lg font-medium text-white">接口信息</h3>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 基本信息 -->
            <div class="space-y-4">
              <h4 class="text-md font-medium text-orange-400">基本信息</h4>
              <div class="space-y-3">
                <div class="flex justify-between items-center py-2 border-b border-gray-700/30">
                  <span class="text-sm text-gray-300">资源库名称</span>
                  <span class="text-sm text-white">{{ resource?.name }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-700/30">
                  <span class="text-sm text-gray-300">接口地址</span>
                  <span class="text-sm text-white font-mono">{{ resource?.url }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-700/30">
                  <span class="text-sm text-gray-300">接口类型</span>
                  <span class="text-sm text-white">{{ getResourceTypeName(resource?.type) }}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-700/30">
                  <span class="text-sm text-gray-300">响应时间</span>
                  <span class="text-sm text-white">{{ stats.responseTime }}ms</span>
                </div>
                <div class="flex justify-between items-center py-2">
                  <span class="text-sm text-gray-300">最后更新</span>
                  <span class="text-sm text-white">{{ resource?.lastUpdate }}</span>
                </div>
              </div>
            </div>

            <!-- 采集配置 -->
            <div class="space-y-4">
              <h4 class="text-md font-medium text-orange-400">采集配置</h4>
              <div class="space-y-3">
                <div class="flex justify-between items-center py-2 border-b border-gray-700/30">
                  <span class="text-sm text-gray-300">采集间隔</span>
                  <span class="text-sm text-white">{{ config.interval }}秒</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-700/30">
                  <span class="text-sm text-gray-300">超时时间</span>
                  <span class="text-sm text-white">{{ config.timeout }}秒</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-700/30">
                  <span class="text-sm text-gray-300">自动采集</span>
                  <span
                    :class="[
                      'text-xs px-2 py-1 rounded',
                      config.autoCollect ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'
                    ]"
                  >
                    {{ config.autoCollect ? '已启用' : '已禁用' }}
                  </span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-700/30">
                  <span class="text-sm text-gray-300">采集图片</span>
                  <span
                    :class="[
                      'text-xs px-2 py-1 rounded',
                      config.collectImages ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'
                    ]"
                  >
                    {{ config.collectImages ? '已启用' : '已禁用' }}
                  </span>
                </div>
                <div class="flex justify-between items-center py-2">
                  <span class="text-sm text-gray-300">状态</span>
                  <span
                    :class="[
                      'text-xs px-2 py-1 rounded',
                      resource?.status === 'active' ? 'bg-green-500/20 text-green-400' :
                      resource?.status === 'testing' ? 'bg-yellow-500/20 text-yellow-400' :
                      'bg-red-500/20 text-red-400'
                    ]"
                  >
                    {{ getStatusName(resource?.status) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 视频详情模态框 -->
    <div v-if="showVideoDetailModal" class="fixed inset-0 bg-gray-900/75 backdrop-blur-sm overflow-y-auto h-full w-full z-50" @click="showVideoDetailModal = false">
      <div class="relative top-4 mx-auto p-0 w-full max-w-5xl m-4" @click.stop>
        <div class="bg-gray-800/95 backdrop-blur-sm border border-gray-700/50 shadow-2xl rounded-2xl overflow-hidden">
          <!-- 模态框头部 -->
          <div class="bg-gradient-to-r from-orange-500 to-red-500 px-6 py-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                  <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-white">视频详情</h3>
                  <p class="text-white/80 text-sm">{{ selectedVideo?.vod_name || '加载中...' }}</p>
                </div>
              </div>
              <button
                @click="showVideoDetailModal = false"
                class="text-white/80 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg"
              >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
          </div>

          <!-- 模态框内容 -->
          <div class="p-6">
            <!-- 加载状态 -->
            <div v-if="selectedVideo?.loading" class="flex flex-col items-center justify-center py-12">
              <div class="relative">
                <div class="animate-spin rounded-full h-12 w-12 border-4 border-gray-600 border-t-orange-500"></div>
                <div class="absolute inset-0 rounded-full bg-gradient-to-r from-orange-500/20 to-red-500/20 animate-pulse"></div>
              </div>
              <span class="mt-4 text-gray-300 font-medium">正在获取详细信息...</span>
              <span class="mt-1 text-gray-400 text-sm">请稍候</span>
            </div>

            <!-- 视频详情内容 -->
            <div v-else-if="selectedVideo" class="space-y-8">
              <!-- 视频封面和基本信息 -->
              <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- 视频封面 -->
                <div class="lg:col-span-1">
                  <div class="relative group">
                    <div v-if="selectedVideo.vod_pic" class="relative overflow-hidden rounded-2xl shadow-2xl">
                      <img
                        :src="selectedVideo.vod_pic"
                        :alt="selectedVideo.vod_name"
                        class="w-full h-auto object-cover transition-transform duration-300 group-hover:scale-105"
                        @error="$event.target.style.display='none'"
                      >
                      <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    <div v-else class="w-full h-64 bg-gray-700/50 rounded-2xl flex items-center justify-center border-2 border-dashed border-gray-600">
                      <div class="text-center">
                        <svg class="w-12 h-12 text-gray-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <p class="text-gray-500 text-sm">暂无封面</p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 基本信息卡片 -->
                <div class="lg:col-span-2 space-y-6">
                  <!-- 标题和评分 -->
                  <div class="bg-gray-700/30 rounded-2xl p-6 border border-gray-600/30">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex-1 min-w-0 pr-4">
                        <h4 class="text-lg font-bold text-white mb-2 break-words">{{ selectedVideo.vod_name }}</h4>
                        <p v-if="selectedVideo.vod_en" class="text-gray-400 text-sm break-words">{{ selectedVideo.vod_en }}</p>
                      </div>
                      <div class="flex items-center space-x-3 flex-shrink-0">
                        <button
                          @click="openCollectCurrentModal"
                          class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 shadow-lg shadow-green-500/25"
                        >
                          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                          </svg>
                          采集当前
                        </button>
                        <div v-if="selectedVideo.vod_score" class="flex items-center space-x-2 bg-gradient-to-r from-orange-500 to-red-500 px-3 py-2 rounded-xl">
                          <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                          <span class="text-white font-bold text-sm">{{ selectedVideo.vod_score }}</span>
                        </div>
                      </div>
                    </div>

                    <!-- 标签信息 -->
                    <div class="flex flex-wrap gap-2">
                      <span class="px-3 py-1 bg-blue-500/20 text-blue-400 rounded-full text-sm font-medium border border-blue-500/30">
                        {{ selectedVideo.type_name }}
                      </span>
                      <span v-if="selectedVideo.vod_year" class="px-3 py-1 bg-green-500/20 text-green-400 rounded-full text-sm font-medium border border-green-500/30">
                        {{ selectedVideo.vod_year }}年
                      </span>
                      <span v-if="selectedVideo.vod_duration" class="px-3 py-1 bg-purple-500/20 text-purple-400 rounded-full text-sm font-medium border border-purple-500/30">
                        {{ selectedVideo.vod_duration }}
                      </span>
                      <span v-if="selectedVideo.vod_remarks" class="px-3 py-1 bg-yellow-500/20 text-yellow-400 rounded-full text-sm font-medium border border-yellow-500/30">
                        {{ selectedVideo.vod_remarks }}
                      </span>
                    </div>
                  </div>

                  <!-- 详细信息网格 -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-gray-700/30 rounded-xl p-4 border border-gray-600/30">
                      <div class="flex items-center space-x-3 mb-3">
                        <div class="w-8 h-8 bg-orange-500/20 rounded-lg flex items-center justify-center">
                          <svg class="w-4 h-4 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                          </svg>
                        </div>
                        <h5 class="text-white font-medium">视频ID</h5>
                      </div>
                      <p class="text-gray-300 font-mono">{{ selectedVideo.vod_id }}</p>
                    </div>

                    <div class="bg-gray-700/30 rounded-xl p-4 border border-gray-600/30">
                      <div class="flex items-center space-x-3 mb-3">
                        <div class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                          <svg class="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <h5 class="text-white font-medium">更新时间</h5>
                      </div>
                      <p class="text-gray-300">{{ selectedVideo.vod_time }}</p>
                    </div>

                    <div v-if="selectedVideo.vod_actor" class="bg-gray-700/30 rounded-xl p-4 border border-gray-600/30">
                      <div class="flex items-center space-x-3 mb-3">
                        <div class="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
                          <svg class="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                        </div>
                        <h5 class="text-white font-medium">演员</h5>
                      </div>
                      <p class="text-gray-300">{{ selectedVideo.vod_actor }}</p>
                    </div>

                    <div v-if="selectedVideo.vod_director" class="bg-gray-700/30 rounded-xl p-4 border border-gray-600/30">
                      <div class="flex items-center space-x-3 mb-3">
                        <div class="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center">
                          <svg class="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <h5 class="text-white font-medium">导演</h5>
                      </div>
                      <p class="text-gray-300">{{ selectedVideo.vod_director }}</p>
                    </div>

                    <div v-if="selectedVideo.vod_play_from" class="bg-gray-700/30 rounded-xl p-4 border border-gray-600/30">
                      <div class="flex items-center space-x-3 mb-3">
                        <div class="w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center">
                          <svg class="w-4 h-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.636 18.364a9 9 0 010-12.728m12.728 0a9 9 0 010 12.728m-9.9-2.829a5 5 0 010-7.07m7.072 0a5 5 0 010 7.07M13 12a1 1 0 11-2 0 1 1 0 012 0z" />
                          </svg>
                        </div>
                        <h5 class="text-white font-medium">播放源</h5>
                      </div>
                      <p class="text-gray-300">{{ selectedVideo.vod_play_from }}</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 剧情简介 -->
              <div v-if="selectedVideo.vod_content" class="bg-gray-700/30 rounded-2xl p-6 border border-gray-600/30">
                <div class="flex items-center space-x-3 mb-4">
                  <div class="w-8 h-8 bg-indigo-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h5 class="text-xl font-bold text-white">剧情简介</h5>
                </div>
                <div class="bg-gray-800/50 rounded-xl p-4 max-h-40 overflow-y-auto custom-scrollbar">
                  <div v-html="selectedVideo.vod_content" class="text-gray-300 leading-relaxed prose prose-invert max-w-none prose-sm"></div>
                </div>
              </div>

              <!-- 播放地址 -->
              <div v-if="selectedVideo.vod_play_url" class="bg-gray-700/30 rounded-2xl p-6 border border-gray-600/30">
                <div class="flex items-center justify-between mb-4">
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-cyan-500/20 rounded-lg flex items-center justify-center">
                      <svg class="w-4 h-4 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                      </svg>
                    </div>
                    <h5 class="text-xl font-bold text-white">播放地址</h5>
                  </div>
                  <button
                    @click="copyPlayUrl"
                    class="px-3 py-1 bg-cyan-500/20 hover:bg-cyan-500/30 text-cyan-400 rounded-lg text-sm font-medium transition-colors border border-cyan-500/30"
                  >
                    复制地址
                  </button>
                </div>
                <div class="bg-gray-800/50 rounded-xl p-4 max-h-40 overflow-y-auto custom-scrollbar">
                  <pre class="text-gray-300 text-xs whitespace-pre-wrap font-mono leading-relaxed">{{ selectedVideo.vod_play_url }}</pre>
                </div>
              </div>
            </div>
          </div>

          <!-- 模态框底部操作栏 -->
          <div class="bg-gray-700/30 px-6 py-4 border-t border-gray-600/30">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2 text-sm text-gray-400">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>数据来源：{{ resource?.name }}</span>
                </div>
              </div>
              <div class="flex items-center space-x-3">
                <button
                  @click="refreshVideoDetail"
                  class="inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-lg text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  刷新
                </button>
                <button
                  @click="showVideoDetailModal = false"
                  class="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200 shadow-lg shadow-orange-500/25"
                >
                  关闭
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 采集当前视频模态框 -->
    <div v-if="showCollectCurrentModal" class="fixed inset-0 bg-gray-900/75 backdrop-blur-sm overflow-y-auto h-full w-full z-50" @click="showCollectCurrentModal = false">
      <div class="relative top-20 mx-auto p-0 w-full max-w-md m-4" @click.stop>
        <div class="bg-gray-800/95 backdrop-blur-sm border border-gray-700/50 shadow-2xl rounded-2xl overflow-hidden">
          <!-- 模态框头部 -->
          <div class="bg-gradient-to-r from-green-500 to-emerald-500 px-6 py-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                  <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-white">采集当前视频</h3>
                  <p class="text-white/80 text-sm">选择目标分类</p>
                </div>
              </div>
              <button
                @click="showCollectCurrentModal = false"
                class="text-white/80 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
          </div>

          <!-- 模态框内容 -->
          <div class="p-6">
            <!-- 视频信息 -->
            <div class="bg-gray-700/30 rounded-xl p-4 mb-6 border border-gray-600/30">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                  <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <h4 class="text-white font-medium truncate">{{ selectedVideo?.vod_name }}</h4>
                  <p class="text-gray-400 text-sm">ID: {{ selectedVideo?.vod_id }}</p>
                </div>
              </div>
            </div>

            <!-- 分类选择 -->
            <div class="mb-6">
              <label class="block text-sm font-medium text-gray-300 mb-3">选择目标分类</label>
              <select
                v-model="collectCurrentConfig.targetCategoryId"
                class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
              >
                <option value="">请选择分类</option>
                <option v-for="category in localCategories" :key="category.id" :value="category.id">
                  {{ category.name }} ({{ category.videoCount }}个视频)
                </option>
              </select>
            </div>

            <!-- 采集选项 -->
            <div class="space-y-4 mb-6">
              <label class="flex items-center space-x-3">
                <input
                  type="checkbox"
                  v-model="collectCurrentConfig.collectImages"
                  class="rounded border-gray-600 text-green-500 focus:ring-green-500 focus:ring-offset-gray-800"
                >
                <span class="text-sm text-white">采集图片</span>
              </label>
              <label class="flex items-center space-x-3">
                <input
                  type="checkbox"
                  v-model="collectCurrentConfig.updateExisting"
                  class="rounded border-gray-600 text-green-500 focus:ring-green-500 focus:ring-offset-gray-800"
                >
                <span class="text-sm text-white">如果已存在则更新</span>
              </label>
              <label class="flex items-center space-x-3">
                <input
                  type="checkbox"
                  v-model="collectCurrentConfig.featured"
                  class="rounded border-gray-600 text-yellow-500 focus:ring-yellow-500 focus:ring-offset-gray-800"
                >
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-white">加入精选</span>
                  <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                </div>
              </label>
            </div>

            <!-- 操作按钮 -->
            <div class="flex items-center justify-end space-x-3">
              <button
                @click="showCollectCurrentModal = false"
                class="px-4 py-2 border border-gray-600 text-sm font-medium rounded-lg text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200"
              >
                取消
              </button>
              <button
                @click="collectCurrentVideo"
                :disabled="!collectCurrentConfig.targetCategoryId || collectingCurrent"
                class="px-6 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 shadow-lg shadow-green-500/25 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg v-if="collectingCurrent" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ collectingCurrent ? '采集中...' : '开始采集' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 开始采集配置模态框 -->
    <div v-if="showCollectConfigModal" class="fixed inset-0 bg-gray-900/75 backdrop-blur-sm overflow-y-auto h-full w-full z-50" @click="cancelCollect">
      <div class="relative top-10 mx-auto p-0 w-full max-w-4xl m-4" @click.stop>
        <div class="bg-gray-800/95 backdrop-blur-sm border border-gray-700/50 shadow-2xl rounded-2xl overflow-hidden">
          <!-- 模态框头部 -->
          <div class="bg-gradient-to-r from-orange-500 to-red-500 px-6 py-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-white">批量采集配置</h3>
                  <p class="text-white/80 text-sm">配置采集参数和分类映射</p>
                </div>
              </div>
              <button
                @click="cancelCollect"
                class="text-white/80 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg"
              >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
          </div>

          <!-- 模态框内容 -->
          <div class="p-6">
            <form @submit.prevent="confirmStartCollect" class="space-y-8">
              <!-- 分类映射配置 -->
              <div class="bg-gray-700/30 rounded-xl p-6 border border-gray-600/30">
                <div class="flex items-center space-x-3 mb-4">
                  <div class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="text-lg font-semibold text-white">分类映射配置</h4>
                    <p class="text-gray-400 text-sm">将源分类映射到本地分类</p>
                  </div>
                </div>

                <div class="space-y-3 max-h-64 overflow-y-auto">
                  <div v-for="sourceCategory in categories" :key="sourceCategory.type_id"
                       class="flex items-center space-x-4 p-4 bg-gray-800/50 rounded-xl border border-gray-600/30 hover:border-gray-500/50 transition-all duration-200">
                    <div class="flex-1 min-w-0">
                      <div class="flex items-center space-x-2">
                        <span class="text-white font-medium">{{ sourceCategory.type_name }}</span>
                        <span class="px-2 py-1 bg-gray-600/50 rounded-full text-xs text-gray-300">源分类</span>
                      </div>
                      <p class="text-gray-400 text-sm mt-1">{{ sourceCategory.type_id }} 个视频</p>
                    </div>
                    <div class="flex items-center space-x-3">
                      <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                      <select
                        v-model="collectConfig.categoryMappings[sourceCategory.type_id]"
                        class="px-4 py-2 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 min-w-[200px]"
                      >
                        <option value="">不采集此分类</option>
                        <option v-for="localCat in localCategories" :key="localCat.id" :value="localCat.id">
                          {{ localCat.name }} ({{ localCat.videoCount }}个视频)
                        </option>
                      </select>
                    </div>
                  </div>
                </div>

                <div class="mt-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                  <div class="flex items-start space-x-2">
                    <svg class="w-5 h-5 text-blue-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                      <p class="text-blue-300 text-sm font-medium">分类映射说明</p>
                      <p class="text-blue-200/80 text-xs mt-1">选择源分类对应的本地分类，未映射的分类将不会被采集</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 时间范围筛选 -->
              <div class="bg-gray-700/30 rounded-xl p-6 border border-gray-600/30">
                <div class="flex items-center space-x-3 mb-4">
                  <div class="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="text-lg font-semibold text-white">时间范围筛选</h4>
                    <p class="text-gray-400 text-sm">选择要采集的视频时间范围</p>
                  </div>
                </div>

                <div class="space-y-4">
                  <div class="grid grid-cols-2 md:grid-cols-5 gap-3">
                    <label class="flex items-center space-x-3 p-3 bg-gray-800/50 rounded-xl border border-gray-600/30 hover:border-orange-500/50 transition-all duration-200 cursor-pointer">
                      <input
                        type="radio"
                        value="all"
                        v-model="collectConfig.timeRange.timeType"
                        class="text-orange-500 focus:ring-orange-500 focus:ring-offset-gray-800"
                      >
                      <span class="text-sm text-white font-medium">全部</span>
                    </label>
                    <label class="flex items-center space-x-3 p-3 bg-gray-800/50 rounded-xl border border-gray-600/30 hover:border-orange-500/50 transition-all duration-200 cursor-pointer">
                      <input
                        type="radio"
                        value="today"
                        v-model="collectConfig.timeRange.timeType"
                        class="text-orange-500 focus:ring-orange-500 focus:ring-offset-gray-800"
                      >
                      <span class="text-sm text-white font-medium">今天</span>
                    </label>
                    <label class="flex items-center space-x-3 p-3 bg-gray-800/50 rounded-xl border border-gray-600/30 hover:border-orange-500/50 transition-all duration-200 cursor-pointer">
                      <input
                        type="radio"
                        value="week"
                        v-model="collectConfig.timeRange.timeType"
                        class="text-orange-500 focus:ring-orange-500 focus:ring-offset-gray-800"
                      >
                      <span class="text-sm text-white font-medium">本周</span>
                    </label>
                    <label class="flex items-center space-x-3 p-3 bg-gray-800/50 rounded-xl border border-gray-600/30 hover:border-orange-500/50 transition-all duration-200 cursor-pointer">
                      <input
                        type="radio"
                        value="month"
                        v-model="collectConfig.timeRange.timeType"
                        class="text-orange-500 focus:ring-orange-500 focus:ring-offset-gray-800"
                      >
                      <span class="text-sm text-white font-medium">本月</span>
                    </label>
                    <label class="flex items-center space-x-3 p-3 bg-gray-800/50 rounded-xl border border-gray-600/30 hover:border-orange-500/50 transition-all duration-200 cursor-pointer">
                      <input
                        type="radio"
                        value="custom"
                        v-model="collectConfig.timeRange.timeType"
                        class="text-orange-500 focus:ring-orange-500 focus:ring-offset-gray-800"
                      >
                      <span class="text-sm text-white font-medium">自定义</span>
                    </label>
                  </div>

                  <!-- 自定义时间范围 -->
                  <div v-if="collectConfig.timeRange.timeType === 'custom'" class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4 p-4 bg-gray-800/30 rounded-xl border border-gray-600/20">
                    <div>
                      <label class="block text-sm font-medium text-gray-300 mb-2">开始日期</label>
                      <input
                        type="date"
                        v-model="collectConfig.timeRange.startDate"
                        class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200"
                      >
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-300 mb-2">结束日期</label>
                      <input
                        type="date"
                        v-model="collectConfig.timeRange.endDate"
                        class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200"
                      >
                    </div>
                  </div>
                </div>
              </div>

              <!-- 采集选项 -->
              <div class="bg-gray-700/30 rounded-xl p-6 border border-gray-600/30">
                <div class="flex items-center space-x-3 mb-4">
                  <div class="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="text-lg font-semibold text-white">采集选项</h4>
                    <p class="text-gray-400 text-sm">配置采集行为</p>
                  </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <label class="flex items-center space-x-3 p-4 bg-gray-800/50 rounded-xl border border-gray-600/30 hover:border-green-500/50 transition-all duration-200 cursor-pointer">
                    <input
                      type="checkbox"
                      v-model="collectConfig.collectImages"
                      class="rounded border-gray-600 text-green-500 focus:ring-green-500 focus:ring-offset-gray-800"
                    >
                    <div class="flex items-center space-x-2">
                      <span class="text-sm text-white font-medium">采集图片到本地</span>
                      <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                  </label>

                  <label class="flex items-center space-x-3 p-4 bg-gray-800/50 rounded-xl border border-gray-600/30 hover:border-green-500/50 transition-all duration-200 cursor-pointer">
                    <input
                      type="checkbox"
                      v-model="collectConfig.updateExisting"
                      class="rounded border-gray-600 text-green-500 focus:ring-green-500 focus:ring-offset-gray-800"
                    >
                    <div class="flex items-center space-x-2">
                      <span class="text-sm text-white font-medium">更新已存在的视频</span>
                      <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                    </div>
                  </label>

                  <label class="flex items-center space-x-3 p-4 bg-gray-800/50 rounded-xl border border-gray-600/30 hover:border-yellow-500/50 transition-all duration-200 cursor-pointer">
                    <input
                      type="checkbox"
                      v-model="collectConfig.featured"
                      class="rounded border-gray-600 text-yellow-500 focus:ring-yellow-500 focus:ring-offset-gray-800"
                    >
                    <div class="flex items-center space-x-2">
                      <span class="text-sm text-white font-medium">标记为精选内容</span>
                      <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    </div>
                  </label>

                  <div class="p-4 bg-gray-800/50 rounded-xl border border-gray-600/30">
                    <label class="block text-sm font-medium text-gray-300 mb-2">视频状态</label>
                    <select
                      v-model="collectConfig.status"
                      class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                    >
                      <option value="active">激活状态</option>
                      <option value="inactive">非激活状态</option>
                    </select>
                  </div>
                </div>
              </div>

              <!-- 采集参数 -->
              <div class="bg-gray-700/30 rounded-xl p-6 border border-gray-600/30">
                <div class="flex items-center space-x-3 mb-4">
                  <div class="w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="text-lg font-semibold text-white">采集参数</h4>
                    <p class="text-gray-400 text-sm">配置采集性能参数</p>
                  </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">最大页数</label>
                    <input
                      type="number"
                      v-model.number="collectConfig.maxPages"
                      min="1"
                      max="100"
                      class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200"
                      placeholder="10"
                    >
                    <p class="text-xs text-gray-400 mt-1">限制采集页数</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">采集间隔(毫秒)</label>
                    <input
                      type="number"
                      v-model.number="collectConfig.interval"
                      min="100"
                      max="10000"
                      step="100"
                      class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200"
                      placeholder="1000"
                    >
                    <p class="text-xs text-gray-400 mt-1">请求间隔时间</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">请求超时(毫秒)</label>
                    <input
                      type="number"
                      v-model.number="collectConfig.timeout"
                      min="5000"
                      max="60000"
                      step="1000"
                      class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200"
                      placeholder="30000"
                    >
                    <p class="text-xs text-gray-400 mt-1">单次请求超时</p>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="flex items-center justify-end space-x-4 pt-4">
                <button
                  type="button"
                  @click="cancelCollect"
                  class="px-6 py-3 border border-gray-600 text-sm font-medium rounded-xl text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200"
                >
                  取消配置
                </button>
                <button
                  type="submit"
                  class="px-8 py-3 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200 shadow-lg shadow-orange-500/25"
                >
                  <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                    </svg>
                    <span>开始批量采集</span>
                  </div>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- 消息提示 -->
    <div v-if="showMessage" class="fixed top-4 right-4 z-50">
      <div
        :class="[
          'px-6 py-4 rounded-lg shadow-lg flex items-center space-x-3 min-w-80',
          {
            'bg-green-600 text-white': messageType === 'success',
            'bg-red-600 text-white': messageType === 'error',
            'bg-yellow-600 text-white': messageType === 'warning',
            'bg-blue-600 text-white': messageType === 'info'
          }
        ]"
      >
        <!-- 图标 -->
        <div class="flex-shrink-0">
          <svg v-if="messageType === 'success'" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
          <svg v-else-if="messageType === 'error'" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
          </svg>
          <svg v-else-if="messageType === 'warning'" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
          </svg>
          <svg v-else class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
          </svg>
        </div>

        <!-- 消息文本 -->
        <div class="flex-1">
          <p class="font-medium">{{ messageText }}</p>
        </div>

        <!-- 关闭按钮 -->
        <button
          @click="showMessage = false"
          class="flex-shrink-0 text-white hover:text-gray-200 transition-colors"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
// 页面元数据
definePageMeta({
  layout: 'admin',
  middleware: 'auth'
})

// 获取路由参数
const route = useRoute()
const resourceId = route.params.id

// 响应式数据
const loading = ref(true)
const error = ref(null)

// 资源库信息
const resource = ref(null)

// 统计数据
const stats = ref({
  totalVideos: 0,
  todayCollected: 0,
  todayIncrease: 0,
  successRate: 0,
  status: 'offline',
  lastCheck: '',
  responseTime: 0
})

// 分类数据
const categories = ref([])

// 视频数据
const videoData = ref(null)
const videoList = ref([])
const loadingVideos = ref(false)
const selectedTypeId = ref('')
const currentPage = ref(1)

// 采集配置
const config = ref({
  interval: 1,
  timeout: 30,
  autoCollect: false,
  collectImages: true
})

// 视频详情模态框
const showVideoDetailModal = ref(false)
const selectedVideo = ref(null)

// 采集当前视频模态框
const showCollectCurrentModal = ref(false)
const collectCurrentConfig = ref({
  targetCategoryId: '',
  updateExisting: false,
  collectImages: true,
  featured: false
})
const localCategories = ref([])
const collectingCurrent = ref(false)

// 开始采集配置模态框
const showCollectConfigModal = ref(false)
const collectConfig = ref({
  categoryMappings: {}, // 分类映射：源分类ID -> 本地分类ID
  timeRange: {
    timeType: 'all', // all, today, week, month, custom
    startDate: '',
    endDate: ''
  },
  // 采集选项
  collectImages: true, // 是否采集图片到本地
  updateExisting: false, // 是否更新已存在的视频
  featured: false, // 是否将采集的视频标记为精选
  status: 'active', // 采集视频的默认状态：active/inactive

  // 采集参数
  maxPages: 10, // 最大采集页数
  interval: 1000, // 采集间隔(毫秒)
  timeout: 30000 // 请求超时(毫秒)
})

// 本地分类数据已在上面定义

// 消息提示
const showMessage = ref(false)
const messageType = ref('success')
const messageText = ref('')

// 消息提示方法
const showMessageToast = (type, text) => {
  messageType.value = type
  messageText.value = text
  showMessage.value = true

  // 3秒后自动隐藏
  setTimeout(() => {
    showMessage.value = false
  }, 3000)
}

// 工具方法
const getResourceTypeName = (type) => {
  const typeMap = {
    'maccms': '苹果CMS',
    'feifei': '飞飞CMS',
    'maxcms': '马克斯CMS',
    'other': '其他'
  }
  return typeMap[type] || '未知'
}

const getStatusName = (status) => {
  const statusMap = {
    'active': '正常',
    'testing': '测试中',
    'inactive': '离线'
  }
  return statusMap[status] || '未知'
}

// API调用方法 - 通过后端获取采集源数据
const fetchResourceData = async (params = {}) => {
  if (!resource.value?.id) {
    throw new Error('资源库ID不存在')
  }

  // 调用后端API获取采集源的数据
  const response = await $fetch(`/api/collect/sources/${resource.value.id}/data`, {
    baseURL: 'http://localhost:3001',
    params
  })

  return response
}

// 获取视频列表
const fetchVideoList = async (page = 1) => {
  try {
    loadingVideos.value = true

    const params = {
      page: page,
      limit: 20
    }

    if (selectedTypeId.value) {
      params.categoryId = selectedTypeId.value
    }

    const response = await $fetch(`/api/collect/sources/${resource.value.id}/videos`, {
      baseURL: 'http://localhost:3001',
      params
    })

    console.log('API返回数据:', response)
    console.log('请求页码:', page)

    if (response.success) {
      const data = response.data
      videoData.value = {
        page: parseInt(data.page) || 1,
        pagecount: parseInt(data.pagecount) || 1,
        total: parseInt(data.total) || 0,
        limit: parseInt(data.limit) || 20
      }
      videoList.value = data.list || []
      currentPage.value = parseInt(data.page) || 1

      // 更新统计数据
      stats.value.totalVideos = parseInt(data.total) || 0
      stats.value.status = 'online'
      stats.value.lastCheck = '刚刚'
    } else {
      throw new Error(response.message || '获取数据失败')
    }
  } catch (err) {
    console.error('获取视频列表失败:', err)
    videoList.value = []
    stats.value.status = 'offline'
  } finally {
    loadingVideos.value = false
  }
}

// 分页操作
const changePage = (page) => {
  const targetPage = parseInt(page)
  console.log('分页操作 - 目标页码:', targetPage, '当前页码:', videoData.value?.page, '总页数:', videoData.value?.pagecount)

  if (targetPage >= 1 && videoData.value && targetPage <= parseInt(videoData.value.pagecount)) {
    fetchVideoList(targetPage)
  } else {
    console.warn('页码超出范围:', targetPage)
  }
}

// 查看视频详情
const viewVideoDetail = async (video) => {
  try {
    // 显示加载状态
    selectedVideo.value = { ...video, loading: true }
    showVideoDetailModal.value = true

    // 调用后端API获取完整的视频详情
    const response = await $fetch(`/api/collect/sources/${resourceId}/video-detail`, {
      method: 'GET',
      baseURL: 'http://localhost:3001',
      query: {
        videoId: video.vod_id
      }
    })

    if (response.success) {
      selectedVideo.value = {
        ...response.data.video,
        loading: false
      }
    } else {
      showMessageToast('error', '获取视频详情失败：' + response.message)
      selectedVideo.value = { ...video, loading: false }
    }

  } catch (err) {
    console.error('获取视频详情失败:', err)
    showMessageToast('error', '获取视频详情失败：' + err.message)
    selectedVideo.value = { ...video, loading: false }
  }
}

// 刷新视频详情
const refreshVideoDetail = async () => {
  if (!selectedVideo.value) return

  try {
    const currentVideo = selectedVideo.value
    selectedVideo.value = { ...currentVideo, loading: true }

    const response = await $fetch(`/api/collect/sources/${resourceId}/video-detail`, {
      method: 'GET',
      baseURL: 'http://localhost:3001',
      query: {
        videoId: currentVideo.vod_id
      }
    })

    if (response.success) {
      selectedVideo.value = {
        ...response.data.video,
        loading: false
      }
      showMessageToast('success', '视频详情已刷新')
    } else {
      showMessageToast('error', '刷新失败：' + response.message)
      selectedVideo.value = { ...currentVideo, loading: false }
    }
  } catch (err) {
    console.error('刷新视频详情失败:', err)
    showMessageToast('error', '刷新失败：' + err.message)
    selectedVideo.value = { ...selectedVideo.value, loading: false }
  }
}

// 复制播放地址
const copyPlayUrl = async () => {
  if (!selectedVideo.value?.vod_play_url) {
    showMessageToast('warning', '没有播放地址可复制')
    return
  }

  try {
    await navigator.clipboard.writeText(selectedVideo.value.vod_play_url)
    showMessageToast('success', '播放地址已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
    // 降级方案：创建临时文本域
    const textArea = document.createElement('textarea')
    textArea.value = selectedVideo.value.vod_play_url
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      showMessageToast('success', '播放地址已复制到剪贴板')
    } catch (fallbackErr) {
      showMessageToast('error', '复制失败，请手动复制')
    }
    document.body.removeChild(textArea)
  }
}

// 获取本地分类列表
const fetchLocalCategories = async () => {
  try {
    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth('/api/admin/categories?limit=100')

    if (response.success) {
      localCategories.value = response.data.categories.map(category => ({
        id: category.id,
        name: category.name,
        videoCount: category.videoCount || 0
      }))
    } else {
      showMessageToast('error', '获取本地分类失败：' + response.message)
    }
  } catch (err) {
    console.error('获取本地分类失败:', err)
    showMessageToast('error', '获取本地分类失败：' + err.message)
  }
}

// 采集当前视频
const collectCurrentVideo = async () => {
  if (!selectedVideo.value || !collectCurrentConfig.value.targetCategoryId) {
    showMessageToast('warning', '请选择目标分类')
    return
  }

  try {
    collectingCurrent.value = true

    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth('/api/admin/collect/single-video', {
      method: 'POST',
      body: {
        sourceId: resourceId,
        videoId: selectedVideo.value.vod_id,
        targetCategoryId: collectCurrentConfig.value.targetCategoryId,
        updateExisting: collectCurrentConfig.value.updateExisting,
        collectImages: collectCurrentConfig.value.collectImages,
        featured: collectCurrentConfig.value.featured,
        videoData: selectedVideo.value
      }
    })

    if (response.success) {
      showMessageToast('success', '视频采集成功')
      showCollectCurrentModal.value = false
      // 重置配置
      collectCurrentConfig.value = {
        targetCategoryId: '',
        updateExisting: false,
        collectImages: true,
        featured: false
      }
    } else {
      showMessageToast('error', '采集失败：' + response.message)
    }
  } catch (err) {
    console.error('采集视频失败:', err)
    showMessageToast('error', '采集失败：' + err.message)
  } finally {
    collectingCurrent.value = false
  }
}

// 打开采集当前模态框
const openCollectCurrentModal = async () => {
  showCollectCurrentModal.value = true
  await fetchLocalCategories()
}

// 按分类筛选
const filterByCategory = (typeId) => {
  selectedTypeId.value = typeId
  fetchVideoList(1)
}

// 操作方法
const testConnection = async () => {
  try {
    console.log('测试连接:', resource.value?.url)
    await fetchVideoList(1)
    stats.value.responseTime = Math.floor(Math.random() * 200) + 50
  } catch (err) {
    console.error('测试连接失败:', err)
    stats.value.status = 'offline'
  }
}

const startCollect = async () => {
  try {
    // 加载本地分类数据
    const response = await $fetch('/api/collect/local-categories', {
      method: 'GET',
      baseURL: 'http://localhost:3001'
    })

    if (response.success) {
      localCategories.value = response.data
    }

    // 打开采集配置模态框
    showCollectConfigModal.value = true

    // 初始化采集配置
    collectConfig.value = {
      categoryMappings: {},
      timeRange: {
        timeType: 'all',
        startDate: '',
        endDate: ''
      },
      collectImages: true,
      updateExisting: false,
      featured: false,
      status: 'active',
      maxPages: 10,
      interval: 1000,
      timeout: 30000
    }
  } catch (err) {
    console.error('加载本地分类失败:', err)
    showMessageToast('error', '加载本地分类失败：' + err.message)
  }
}

const confirmStartCollect = async () => {
  try {
    // 过滤掉空值的分类映射
    const validCategoryMappings = {}
    Object.entries(collectConfig.value.categoryMappings || {}).forEach(([sourceId, targetId]) => {
      if (targetId && targetId !== '') {
        validCategoryMappings[sourceId] = parseInt(targetId)
      }
    })

    // 验证分类映射
    if (Object.keys(validCategoryMappings).length === 0) {
      showMessageToast('warning', '请至少配置一个分类映射')
      return
    }

    // 准备提交的配置数据
    const submitConfig = {
      ...collectConfig.value,
      categoryMappings: validCategoryMappings
    }

    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth(`/api/collect/sources/${resourceId}/start`, {
      method: 'POST',
      body: submitConfig
    })

    if (response.success) {
      showMessageToast('success', '采集任务已启动！')
      showCollectConfigModal.value = false

      // 跳转到任务管理页面查看进度
      if (response.data?.taskId) {
        showMessageToast('info', '正在跳转到任务管理页面...')
        setTimeout(() => {
          navigateTo(`/admin/collect/tasks/${response.data.taskId}`)
        }, 1500)
      }
    } else {
      showMessageToast('error', '启动采集失败：' + response.message)
    }
  } catch (err) {
    console.error('开始采集失败:', err)
    showMessageToast('error', '开始采集失败：' + err.message)
  }
}

const cancelCollect = () => {
  showCollectConfigModal.value = false
}

// 获取分类和视频数据
const fetchCategoriesAndVideos = async () => {
  try {
    // 获取分类数据
    const categoryResponse = await $fetch(`/api/collect/sources/${resource.value.id}/categories`, {
      baseURL: 'http://localhost:3001'
    })

    if (categoryResponse.success) {
      categories.value = categoryResponse.data || []
    }

    // 获取视频数据
    await fetchVideoList(1)

    // 更新统计数据
    stats.value.successRate = 98.5
    stats.value.status = 'online'
    stats.value.lastCheck = '刚刚'
    stats.value.responseTime = Math.floor(Math.random() * 200) + 50
  } catch (err) {
    console.error('获取数据失败:', err)
    stats.value.status = 'offline'
    stats.value.lastCheck = '连接失败'

    // 如果API不存在，使用模拟数据
    categories.value = [
      { type_id: 1, type_name: '电影' },
      { type_id: 2, type_name: '电视剧' },
      { type_id: 3, type_name: '综艺' },
      { type_id: 4, type_name: '动漫' }
    ]
    await fetchVideoList(1)
  }
}

// 获取资源库详情
const fetchResourceDetail = async () => {
  try {
    loading.value = true
    error.value = null

    // 从后端API获取采集源详情
    const response = await $fetch(`/api/collect/sources/${resourceId}`, {
      baseURL: 'http://localhost:3001'
    })

    if (response.success) {
      resource.value = response.data
    } else {
      throw new Error(response.message || '获取资源库详情失败')
    }

    if (!resource.value) {
      throw new Error('资源库不存在')
    }

    // 初始化统计数据
    stats.value = {
      totalVideos: 0,
      todayCollected: 0,
      todayIncrease: 0,
      successRate: 0,
      status: 'offline',
      lastCheck: '未检测',
      responseTime: 0
    }

    // 获取真实的分类和视频数据
    await fetchCategoriesAndVideos()

    // 模拟采集配置
    config.value = {
      interval: 1,
      timeout: 30,
      autoCollect: resource.value.status === 'active',
      collectImages: true
    }

  } catch (err) {
    console.error('获取资源库详情失败:', err)
    error.value = err.message || '获取数据失败'
  } finally {
    loading.value = false
  }
}

// 页面挂载时获取数据
onMounted(() => {
  fetchResourceDetail()
})

// 页面标题
useHead({
  title: computed(() => `${resource.value?.name || '资源库详情'} - 91JSPG.COM 管理后台`)
})
</script>

<style scoped>
/* 自定义滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(55, 65, 81, 0.3);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* 模态框动画 */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* 卡片悬停效果 */
.info-card {
  transition: all 0.3s ease;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 渐变文本效果 */
.gradient-text {
  background: linear-gradient(135deg, #f97316, #ef4444);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 毛玻璃效果增强 */
.glass-effect {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}
</style>

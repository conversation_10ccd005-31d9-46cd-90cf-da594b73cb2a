<template>
  <div>
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-white">资源采集</h1>
      <p class="mt-2 text-gray-400">从其他影视资源站采集视频数据，支持苹果CMS、飞飞CMS等主流采集接口。</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
      <span class="ml-3 text-gray-400">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="bg-red-900/20 border border-red-500/50 rounded-lg p-4 mb-6">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="text-red-400 font-medium">{{ error }}</span>
        <button @click="fetchCollectData" class="ml-auto px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors">
          重试
        </button>
      </div>
    </div>

    <!-- 主要内容 -->
    <div v-else>
      <!-- 快速操作按钮 -->
      <div class="mb-8 flex flex-wrap gap-4">
        <button
          @click="showAddResourceModal = true"
          class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200 shadow-lg shadow-orange-500/25"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          添加资源库
        </button>

        <button
          @click="testAllResources"
          class="inline-flex items-center px-6 py-3 border border-gray-600 text-sm font-medium rounded-xl text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          测试所有资源库
        </button>

        <NuxtLink
          to="/admin/collect/tasks"
          class="inline-flex items-center px-6 py-3 border border-purple-600 text-sm font-medium rounded-xl text-purple-400 bg-purple-600/10 hover:bg-purple-600/20 hover:text-purple-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          任务管理
        </NuxtLink>

        <NuxtLink
          to="/admin/collect/logs"
          class="inline-flex items-center px-6 py-3 border border-green-600 text-sm font-medium rounded-xl text-green-400 bg-green-600/10 hover:bg-green-600/20 hover:text-green-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          查看日志
        </NuxtLink>

        <button
          @click="startBatchCollect"
          class="inline-flex items-center px-6 py-3 border border-green-600 text-sm font-medium rounded-xl text-green-300 bg-green-700/20 hover:bg-green-600/30 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          批量采集
        </button>
      </div>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- 资源库数量 -->
        <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 overflow-hidden shadow-xl rounded-2xl hover:shadow-2xl transition-all duration-300 group">
          <div class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25 group-hover:scale-110 transition-transform">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-4H5m14 8H5" />
                  </svg>
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-400 truncate">资源库数量</dt>
                  <dd class="text-2xl font-bold text-white">{{ stats.totalResources }}</dd>
                </dl>
              </div>
            </div>
          </div>
          <div class="bg-gray-700/30 px-6 py-3 border-t border-gray-700/50">
            <div class="text-sm">
              <span class="text-green-400 font-medium">{{ stats.activeResources }}</span>
              <span class="text-gray-400">个可用</span>
            </div>
          </div>
        </div>

        <!-- 今日采集数 -->
        <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 overflow-hidden shadow-xl rounded-2xl hover:shadow-2xl transition-all duration-300 group">
          <div class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25 group-hover:scale-110 transition-transform">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-400 truncate">今日采集</dt>
                  <dd class="text-2xl font-bold text-white">{{ stats.todayCollected }}</dd>
                </dl>
              </div>
            </div>
          </div>
          <div class="bg-gray-700/30 px-6 py-3 border-t border-gray-700/50">
            <div class="text-sm">
              <span class="text-green-400 font-medium">+{{ stats.todayIncrease }}</span>
              <span class="text-gray-400">较昨日</span>
            </div>
          </div>
        </div>

        <!-- 总采集数 -->
        <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 overflow-hidden shadow-xl rounded-2xl hover:shadow-2xl transition-all duration-300 group">
          <div class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25 group-hover:scale-110 transition-transform">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-400 truncate">总采集数</dt>
                  <dd class="text-2xl font-bold text-white">{{ stats.totalCollected }}</dd>
                </dl>
              </div>
            </div>
          </div>
          <div class="bg-gray-700/30 px-6 py-3 border-t border-gray-700/50">
            <div class="text-sm">
              <span class="text-orange-400 font-medium">{{ stats.successRate }}%</span>
              <span class="text-gray-400">成功率</span>
            </div>
          </div>
        </div>

        <!-- 采集状态 -->
        <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 overflow-hidden shadow-xl rounded-2xl hover:shadow-2xl transition-all duration-300 group">
          <div class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg shadow-purple-500/25 group-hover:scale-110 transition-transform">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-400 truncate">采集状态</dt>
                  <dd class="text-2xl font-bold text-white">{{ stats.collectStatus }}</dd>
                </dl>
              </div>
            </div>
          </div>
          <div class="bg-gray-700/30 px-6 py-3 border-t border-gray-700/50">
            <div class="text-sm">
              <span class="text-purple-400 font-medium">{{ stats.runningTasks }}</span>
              <span class="text-gray-400">个任务运行中</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 资源库管理 -->
      <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl mb-8">
        <div class="px-6 py-4 border-b border-gray-700/50">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-white">资源库管理</h3>
            <div class="flex items-center space-x-3">
              <select v-model="selectedResourceType" class="px-3 py-1 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-orange-500">
                <option value="">全部类型</option>
                <option value="maccms">苹果CMS</option>
                <option value="feifei">飞飞CMS</option>
                <option value="maxcms">马克斯CMS</option>
                <option value="other">其他</option>
              </select>
              <button
                @click="refreshResourceList"
                class="px-3 py-1 bg-gray-600 hover:bg-gray-500 text-white text-sm rounded-lg transition-colors"
              >
                刷新
              </button>
            </div>
          </div>
        </div>
        <div class="p-6">
          <!-- 资源库列表 -->
          <div class="space-y-4">
            <div
              v-for="resource in filteredResources"
              :key="resource.id"
              class="bg-gray-700/30 rounded-xl p-4 hover:bg-gray-700/50 transition-colors cursor-pointer"
              @click="viewResourceDetail(resource)"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <!-- 状态指示器 -->
                  <div class="flex items-center">
                    <div
                      :class="[
                        'w-3 h-3 rounded-full mr-3 shadow-sm',
                        resource.status === 'active' ? 'bg-green-400 shadow-green-400/50' :
                        resource.status === 'testing' ? 'bg-yellow-400 shadow-yellow-400/50' :
                        'bg-red-400 shadow-red-400/50'
                      ]"
                    ></div>
                    <div>
                      <h4 class="text-white font-medium">{{ resource.name }}</h4>
                      <p class="text-sm text-gray-400">{{ resource.url }}</p>
                    </div>
                  </div>

                  <!-- 资源库信息 -->
                  <div class="flex items-center space-x-6 text-sm">
                    <div class="text-center">
                      <div class="text-white font-medium">{{ resource.totalVideos }}</div>
                      <div class="text-gray-400">总视频</div>
                    </div>
                    <div class="text-center">
                      <div class="text-white font-medium">{{ resource.todayCollected }}</div>
                      <div class="text-gray-400">今日采集</div>
                    </div>
                    <div class="text-center">
                      <div class="text-white font-medium">{{ resource.lastUpdate }}</div>
                      <div class="text-gray-400">最后更新</div>
                    </div>
                    <div class="text-center">
                      <span
                        :class="[
                          'px-2 py-1 rounded text-xs font-medium',
                          resource.type === 'maccms' ? 'bg-blue-500/20 text-blue-400' :
                          resource.type === 'feifei' ? 'bg-green-500/20 text-green-400' :
                          resource.type === 'maxcms' ? 'bg-purple-500/20 text-purple-400' :
                          'bg-gray-500/20 text-gray-400'
                        ]"
                      >
                        {{ getResourceTypeName(resource.type) }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex items-center space-x-2" @click.stop>
                  <button
                    @click="testResource(resource)"
                    class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors"
                  >
                    测试
                  </button>
                  <button
                    @click="collectFromResource(resource)"
                    class="px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm rounded-lg transition-colors"
                  >
                    采集
                  </button>
                  <button
                    @click="editResource(resource)"
                    class="px-3 py-1 bg-orange-600 hover:bg-orange-700 text-white text-sm rounded-lg transition-colors"
                  >
                    编辑
                  </button>
                  <button
                    @click="deleteResource(resource)"
                    class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded-lg transition-colors"
                  >
                    删除
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="filteredResources.length === 0" class="text-center py-12">
            <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-4H5m14 8H5" />
            </svg>
            <h3 class="text-lg font-medium text-gray-300 mb-2">暂无资源库</h3>
            <p class="text-gray-400 mb-4">点击"添加资源库"按钮开始添加采集源</p>
            <button
              @click="showAddResourceModal = true"
              class="inline-flex items-center px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white text-sm rounded-lg transition-colors"
            >
              添加第一个资源库
            </button>
          </div>
        </div>
      </div>



      <!-- 添加资源库模态框 -->
      <div v-if="showAddResourceModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-gray-800 rounded-2xl p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-bold text-white">添加资源库</h3>
            <button
              @click="showAddResourceModal = false"
              class="text-gray-400 hover:text-white transition-colors"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form @submit.prevent="addResource" class="space-y-4">
            <!-- 资源库名称 -->
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">资源库名称</label>
              <input
                v-model="newResource.name"
                type="text"
                required
                placeholder="例如：某某影视资源库"
                class="w-full px-3 py-2 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
              >
            </div>

            <!-- 资源库类型 -->
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">资源库类型</label>
              <select
                v-model="newResource.type"
                required
                class="w-full px-3 py-2 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
              >
                <option value="">请选择类型</option>
                <option value="maccms">苹果CMS (MacCMS)</option>
                <option value="feifei">飞飞CMS</option>
                <option value="maxcms">马克斯CMS</option>
                <option value="other">其他</option>
              </select>
            </div>

            <!-- 接口地址 -->
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">接口地址</label>
              <input
                v-model="newResource.url"
                type="url"
                required
                placeholder="https://example.com/api/collect/vod"
                class="w-full px-3 py-2 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
              >
              <p class="text-xs text-gray-400 mt-1">请输入完整的采集接口地址</p>
            </div>

            <!-- 描述 -->
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">描述 (可选)</label>
              <textarea
                v-model="newResource.description"
                rows="3"
                placeholder="资源库描述信息..."
                class="w-full px-3 py-2 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
              ></textarea>
            </div>

            <!-- 高级设置 -->
            <div class="border-t border-gray-700 pt-4">
              <h4 class="text-sm font-medium text-gray-300 mb-3">高级设置</h4>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- 采集间隔 -->
                <div>
                  <label class="block text-sm font-medium text-gray-300 mb-2">采集间隔 (秒)</label>
                  <input
                    v-model="newResource.interval"
                    type="number"
                    min="1"
                    placeholder="1"
                    class="w-full px-3 py-2 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
                  >
                </div>

                <!-- 超时时间 -->
                <div>
                  <label class="block text-sm font-medium text-gray-300 mb-2">超时时间 (秒)</label>
                  <input
                    v-model="newResource.timeout"
                    type="number"
                    min="5"
                    placeholder="30"
                    class="w-full px-3 py-2 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
                  >
                </div>
              </div>

              <!-- 选项 -->
              <div class="mt-4 space-y-2">
                <label class="flex items-center">
                  <input
                    v-model="newResource.autoCollect"
                    type="checkbox"
                    class="mr-2 rounded border-gray-600 bg-gray-700 text-orange-500 focus:ring-orange-500"
                  >
                  <span class="text-sm text-gray-300">启用自动采集</span>
                </label>
                <label class="flex items-center">
                  <input
                    v-model="newResource.collectImages"
                    type="checkbox"
                    class="mr-2 rounded border-gray-600 bg-gray-700 text-orange-500 focus:ring-orange-500"
                  >
                  <span class="text-sm text-gray-300">同时采集图片</span>
                </label>
              </div>
            </div>

            <!-- 按钮 -->
            <div class="flex items-center justify-end space-x-3 pt-4">
              <button
                type="button"
                @click="showAddResourceModal = false"
                class="px-4 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700/50 transition-colors"
              >
                取消
              </button>
              <button
                type="button"
                @click="testNewResource"
                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                测试连接
              </button>
              <button
                type="submit"
                class="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors"
              >
                添加资源库
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- 删除确认模态框 -->
      <div v-if="showDeleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-bold text-white">确认删除</h3>
            <button
              @click="cancelDelete"
              class="text-gray-400 hover:text-white transition-colors"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <div class="mb-6">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
              </div>
              <div>
                <h4 class="text-lg font-semibold text-white mb-1">删除资源库</h4>
                <p class="text-gray-300">
                  确定要删除资源库 <span class="font-semibold text-orange-400">"{{ resourceToDelete?.name }}"</span> 吗？
                </p>
                <p class="text-sm text-gray-400 mt-2">此操作不可撤销，相关的采集记录也将被删除。</p>
              </div>
            </div>
          </div>

          <div class="flex justify-end space-x-3">
            <button
              @click="cancelDelete"
              class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
            >
              取消
            </button>
            <button
              @click="confirmDelete"
              class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
            >
              确认删除
            </button>
          </div>
        </div>
      </div>

      <!-- 消息提示 -->
      <div v-if="showMessage" class="fixed top-4 right-4 z-50">
        <div
          :class="[
            'px-6 py-4 rounded-lg shadow-lg flex items-center space-x-3 min-w-80',
            {
              'bg-green-600 text-white': messageType === 'success',
              'bg-red-600 text-white': messageType === 'error',
              'bg-yellow-600 text-white': messageType === 'warning',
              'bg-blue-600 text-white': messageType === 'info'
            }
          ]"
        >
          <!-- 图标 -->
          <div class="flex-shrink-0">
            <svg v-if="messageType === 'success'" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <svg v-else-if="messageType === 'error'" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
            </svg>
            <svg v-else-if="messageType === 'warning'" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <svg v-else class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
          </div>

          <!-- 消息文本 -->
          <div class="flex-1">
            <p class="font-medium">{{ messageText }}</p>
          </div>

          <!-- 关闭按钮 -->
          <button
            @click="showMessage = false"
            class="flex-shrink-0 text-white hover:text-gray-200 transition-colors"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
// 页面元数据
definePageMeta({
  layout: 'admin',
  middleware: 'auth'
})

// 响应式数据
const loading = ref(true)
const error = ref(null)

// 统计数据
const stats = ref({
  totalResources: 5,
  activeResources: 4,
  todayCollected: 156,
  todayIncrease: 23,
  totalCollected: 15680,
  successRate: 98.5,
  collectStatus: '正常',
  runningTasks: 2
})

// 资源库管理
const selectedResourceType = ref('')
const resources = ref([])

// 获取采集源列表
const fetchCollectSources = async () => {
  try {
    loading.value = true
    error.value = null

    const response = await $fetch('/api/collect/sources', {
      baseURL: 'http://localhost:3001'
    })

    if (response.success) {
      resources.value = response.data.map(source => ({
        id: source.id,
        name: source.name,
        url: source.url,
        type: source.type,
        status: source.status,
        totalVideos: source.totalVideos || 0,
        todayCollected: source.collectedVideos || 0,
        lastUpdate: source.lastCheckTime ? formatTime(source.lastCheckTime) : '从未检测',
        description: source.description,
        successRate: source.successRate || 0,
        responseTime: source.responseTime || 0
      }))

      // 更新统计数据
      updateStats()
    } else {
      throw new Error(response.message || '获取采集源失败')
    }
  } catch (err) {
    console.error('获取采集源失败:', err)
    error.value = err.message || '获取采集源失败'
  } finally {
    loading.value = false
  }
}



// 添加资源库模态框
const showAddResourceModal = ref(false)
const newResource = ref({
  name: '',
  type: '',
  url: '',
  description: '',
  interval: 1,
  timeout: 30,
  autoCollect: false,
  collectImages: true
})

// 删除确认模态框
const showDeleteModal = ref(false)
const resourceToDelete = ref(null)

// 消息提示
const showMessage = ref(false)
const messageType = ref('success') // success, error, warning, info
const messageText = ref('')

// 计算属性
const filteredResources = computed(() => {
  if (!selectedResourceType.value) return resources.value
  return resources.value.filter(resource => resource.type === selectedResourceType.value)
})



// 工具方法
const getResourceTypeName = (type) => {
  const typeMap = {
    'maccms': '苹果CMS',
    'feifei': '飞飞CMS',
    'maxcms': '马克斯CMS',
    'other': '其他'
  }
  return typeMap[type] || '未知'
}

const getStatusName = (status) => {
  const statusMap = {
    'active': '正常',
    'testing': '测试中',
    'inactive': '离线',
    'error': '错误'
  }
  return statusMap[status] || '未知'
}

const formatTime = (timeString) => {
  if (!timeString) return '从未'

  const time = new Date(timeString)
  const now = new Date()
  const diff = now - time

  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return `${days}天前`
}

// 更新统计数据
const updateStats = () => {
  const activeResources = resources.value.filter(r => r.status === 'active').length
  const totalCollected = resources.value.reduce((sum, r) => sum + (r.todayCollected || 0), 0)
  const avgSuccessRate = resources.value.length > 0
    ? resources.value.reduce((sum, r) => sum + (r.successRate || 0), 0) / resources.value.length
    : 0

  stats.value = {
    ...stats.value,
    totalResources: resources.value.length,
    activeResources,
    todayCollected: totalCollected,
    successRate: Math.round(avgSuccessRate * 10) / 10
  }
}

// 消息提示方法
const showMessageToast = (type, text) => {
  messageType.value = type
  messageText.value = text
  showMessage.value = true

  // 3秒后自动隐藏
  setTimeout(() => {
    showMessage.value = false
  }, 3000)
}

// 快速操作方法
const testAllResources = async () => {
  console.log('测试所有资源库')
  // 实际项目中应该调用API
}

const startBatchCollect = async () => {
  console.log('开始批量采集')
  // 实际项目中应该调用API
}

// 资源库操作方法
const viewResourceDetail = (resource) => {
  // 跳转到资源库详情页面
  navigateTo(`/admin/collect/${resource.id}`)
}

const refreshResourceList = async () => {
  await fetchCollectSources()
}

const testResource = async (resource) => {
  try {
    console.log('测试资源库:', resource.name)

    const response = await $fetch(`/api/collect/sources/${resource.id}/test`, {
      method: 'POST',
      baseURL: 'http://localhost:3001'
    })

    if (response.success) {
      // 更新资源状态
      const index = resources.value.findIndex(r => r.id === resource.id)
      if (index !== -1) {
        resources.value[index] = {
          ...resources.value[index],
          status: response.data.status === 'online' ? 'active' : 'error',
          responseTime: response.data.responseTime
        }
      }

      showMessageToast('success', '测试成功：' + response.data.message)
    } else {
      showMessageToast('error', '测试失败：' + response.message)
    }
  } catch (err) {
    console.error('测试失败:', err)
    showMessageToast('error', '测试失败：' + err.message)
  }
}

const collectFromResource = async (resource) => {
  try {
    console.log('开始采集:', resource.name)
    // 实际项目中应该调用API开始采集
  } catch (err) {
    console.error('采集失败:', err)
  }
}

const editResource = (resource) => {
  console.log('编辑资源库:', resource.name)
  // 打开编辑模态框
}

const deleteResource = (resource) => {
  resourceToDelete.value = resource
  showDeleteModal.value = true
}

const confirmDelete = async () => {
  if (!resourceToDelete.value) return

  try {
    const response = await $fetch(`/api/collect/sources/${resourceToDelete.value.id}`, {
      method: 'DELETE',
      baseURL: 'http://localhost:3001'
    })

    if (response.success) {
      // 从列表中移除
      const index = resources.value.findIndex(r => r.id === resourceToDelete.value.id)
      if (index > -1) {
        resources.value.splice(index, 1)
      }

      // 更新统计
      updateStats()

      showMessageToast('success', '删除成功！')
    } else {
      showMessageToast('error', '删除失败：' + response.message)
    }
  } catch (err) {
    console.error('删除失败:', err)
    showMessageToast('error', '删除失败：' + err.message)
  } finally {
    showDeleteModal.value = false
    resourceToDelete.value = null
  }
}

const cancelDelete = () => {
  showDeleteModal.value = false
  resourceToDelete.value = null
}



// 添加资源库方法
const addResource = async () => {
  try {
    console.log('添加资源库:', newResource.value)

    const response = await $fetch('/api/collect/sources', {
      method: 'POST',
      baseURL: 'http://localhost:3001',
      body: {
        name: newResource.value.name,
        url: newResource.value.url,
        type: newResource.value.type,
        description: newResource.value.description,
        autoCollect: newResource.value.autoCollect ? 1 : 0,
        collectImages: newResource.value.collectImages ? 1 : 0,
        collectInterval: newResource.value.interval || 60
      }
    })

    if (response.success) {
      showMessageToast('success', '添加成功！')
      showAddResourceModal.value = false
      resetNewResource()
      await fetchCollectSources() // 刷新列表
    } else {
      showMessageToast('error', '添加失败：' + response.message)
    }
  } catch (err) {
    console.error('添加失败:', err)
    showMessageToast('error', '添加失败：' + err.message)
  }
}

const resetNewResource = () => {
  newResource.value = {
    name: '',
    type: 'maccms',
    url: '',
    description: '',
    interval: 1,
    timeout: 30,
    autoCollect: false,
    collectImages: true
  }
}

const testNewResource = async () => {
  try {
    if (!newResource.value.url) {
      showMessageToast('warning', '请先输入资源库URL')
      return
    }

    console.log('测试新资源库连接:', newResource.value.url)

    // 这里可以添加URL格式验证和连接测试
    const response = await fetch(newResource.value.url + '?ac=list&pg=1')

    if (response.ok) {
      const data = await response.json()
      if (data.code === 1) {
        showMessageToast('success', '连接测试成功！检测到 ' + (data.total || 0) + ' 条数据')
      } else {
        showMessageToast('warning', '连接成功但数据格式异常：' + (data.msg || '未知错误'))
      }
    } else {
      showMessageToast('error', '连接测试失败，请检查URL是否正确')
    }
  } catch (err) {
    console.error('测试失败:', err)
    showMessageToast('error', '连接测试失败：' + err.message)
  }
}

// 页面挂载时获取数据
onMounted(() => {
  loading.value = false
})

// 页面挂载时获取数据
onMounted(() => {
  fetchCollectSources()
})

// 页面标题
useHead({
  title: '资源采集 - 91JSPG.COM 管理后台'
})
</script>

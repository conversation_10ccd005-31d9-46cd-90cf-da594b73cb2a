<template>
  <div>
    <!-- 页面标题 -->
    <div class="mb-8">
      <div class="flex items-center space-x-4 mb-4">
        <NuxtLink to="/admin/collect" class="text-gray-400 hover:text-white transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
        </NuxtLink>
        <h1 class="text-3xl font-bold text-white">采集日志</h1>
        <div class="ml-auto flex items-center space-x-4">
          <!-- 实时状态指示器 -->
          <div v-if="logs.some(log => log.status === 'running')" class="flex items-center space-x-2 px-3 py-1 bg-blue-500/20 border border-blue-500/30 rounded-lg">
            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span class="text-xs text-blue-400">实时更新中</span>
          </div>

          <button
            @click="refreshLogs"
            class="inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-xl text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            刷新
          </button>
        </div>
      </div>
      <p class="mt-2 text-gray-400">查看所有采集操作的详细日志和执行记录。</p>
    </div>

    <!-- 筛选器 -->
    <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl p-6 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">状态筛选</label>
          <select 
            v-model="filters.status" 
            @change="loadLogs"
            class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            <option value="">全部状态</option>
            <option value="running">运行中</option>
            <option value="success">成功</option>
            <option value="failed">失败</option>
            <option value="stopped">已停止</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">类型筛选</label>
          <select 
            v-model="filters.type" 
            @change="loadLogs"
            class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            <option value="">全部类型</option>
            <option value="manual">手动采集</option>
            <option value="auto">自动采集</option>
            <option value="test">测试采集</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">采集源</label>
          <select 
            v-model="filters.sourceId" 
            @change="loadLogs"
            class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            <option value="">全部采集源</option>
            <option v-for="source in collectSources" :key="source.id" :value="source.id">
              {{ source.name }}
            </option>
          </select>
        </div>
        <div class="flex items-end">
          <button
            @click="clearFilters"
            class="w-full px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
          >
            清除筛选
          </button>
        </div>
      </div>
    </div>

    <!-- 日志列表 -->
    <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-700">
        <h2 class="text-lg font-semibold text-white">采集日志</h2>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
        <span class="ml-3 text-gray-300">加载中...</span>
      </div>

      <!-- 日志表格 -->
      <div v-else-if="logs.length > 0" class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-700">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">日志信息</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">采集源</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">状态</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">采集统计</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">时间信息</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-700">
            <tr v-for="log in logs" :key="log.id" class="hover:bg-gray-750 transition-colors">
              <td class="px-6 py-4">
                <div>
                  <div class="text-sm font-medium text-white">日志 #{{ log.id }}</div>
                  <div class="text-xs text-gray-400">{{ getTypeText(log.type) }}</div>
                </div>
              </td>
              <td class="px-6 py-4">
                <div>
                  <div class="text-sm text-white">{{ log.sourceName }}</div>
                  <div class="text-xs text-gray-400">ID: {{ log.sourceId }}</div>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="flex items-center space-x-2">
                  <span :class="getStatusClass(log.status)" class="px-2 py-1 text-xs font-medium rounded-full">
                    {{ getStatusText(log.status) }}
                  </span>
                  <!-- 运行中状态显示动画 -->
                  <div v-if="log.status === 'running'" class="flex items-center space-x-1">
                    <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                    <span class="text-xs text-blue-400">实时更新中</span>
                  </div>
                  <!-- 显示进度百分比 -->
                  <span v-if="log.status === 'running' && log.progress" class="text-xs text-gray-400">
                    {{ log.progress }}%
                  </span>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-gray-300">
                  <div>发现: {{ log.totalFound || 0 }}个</div>
                  <div class="text-green-400">采集: {{ log.totalCollected || 0 }}个</div>
                  <div class="text-yellow-400">跳过: {{ log.totalSkipped || 0 }}个</div>
                  <div class="text-red-400">失败: {{ log.totalFailed || 0 }}个</div>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-gray-300">
                  <div>开始: {{ formatTime(log.startTime) }}</div>
                  <div v-if="log.endTime">结束: {{ formatTime(log.endTime) }}</div>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="flex items-center space-x-2">
                  <button
                    @click="viewLogDetail(log)"
                    class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors"
                  >
                    查看详情
                  </button>
                  <button
                    v-if="log.status === 'failed' && log.errorMessage"
                    @click="viewError(log)"
                    class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors"
                  >
                    查看错误
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 空状态 -->
      <div v-else class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-300">暂无日志</h3>
        <p class="mt-1 text-sm text-gray-500">还没有任何采集日志记录</p>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.totalPages > 1" class="px-6 py-4 border-t border-gray-700">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-400">
            共 {{ pagination.total }} 条记录，第 {{ pagination.page }} / {{ pagination.totalPages }} 页
          </div>
          <div class="flex items-center space-x-2">
            <button
              @click="changePage(pagination.page - 1)"
              :disabled="pagination.page <= 1"
              class="px-3 py-1 bg-gray-600 hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-white text-sm rounded transition-colors"
            >
              上一页
            </button>
            <button
              @click="changePage(pagination.page + 1)"
              :disabled="pagination.page >= pagination.totalPages"
              class="px-3 py-1 bg-gray-600 hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-white text-sm rounded transition-colors"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志详情模态框 -->
    <div v-if="showDetailModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-gray-800 rounded-2xl p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-xl font-bold text-white">采集日志详情</h3>
          <button
            @click="showDetailModal = false"
            class="text-gray-400 hover:text-white transition-colors"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div v-if="selectedLog" class="space-y-6">
          <!-- 基本信息 -->
          <div class="bg-gray-900 rounded-lg p-4">
            <h4 class="text-lg font-semibold text-white mb-4">基本信息</h4>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-400">采集源：</span>
                <span class="text-white">{{ selectedLog.sourceName }}</span>
              </div>
              <div>
                <span class="text-gray-400">采集类型：</span>
                <span class="text-white">{{ getTypeText(selectedLog.type) }}</span>
              </div>
              <div>
                <span class="text-gray-400">状态：</span>
                <span :class="getStatusClass(selectedLog.status)">{{ getStatusText(selectedLog.status) }}</span>
              </div>
              <div>
                <span class="text-gray-400">耗时：</span>
                <span class="text-white">{{ selectedLog.duration || 0 }}秒</span>
              </div>
              <div>
                <span class="text-gray-400">开始时间：</span>
                <span class="text-white">{{ formatTime(selectedLog.startTime) }}</span>
              </div>
              <div>
                <span class="text-gray-400">结束时间：</span>
                <span class="text-white">{{ selectedLog.endTime ? formatTime(selectedLog.endTime) : '未结束' }}</span>
              </div>
            </div>
          </div>

          <!-- 统计信息 -->
          <div class="bg-gray-900 rounded-lg p-4">
            <h4 class="text-lg font-semibold text-white mb-4">统计信息</h4>
            <div class="grid grid-cols-4 gap-4">
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-400">{{ selectedLog.totalFound || 0 }}</div>
                <div class="text-sm text-gray-400">发现</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-green-400">{{ selectedLog.totalCollected || 0 }}</div>
                <div class="text-sm text-gray-400">采集</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-yellow-400">{{ selectedLog.totalSkipped || 0 }}</div>
                <div class="text-sm text-gray-400">跳过</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-red-400">{{ selectedLog.totalFailed || 0 }}</div>
                <div class="text-sm text-gray-400">失败</div>
              </div>
            </div>
          </div>

          <!-- 采集参数 -->
          <div v-if="selectedLog.collectParams" class="bg-gray-900 rounded-lg p-4">
            <h4 class="text-lg font-semibold text-white mb-4">采集参数</h4>
            <pre class="text-gray-300 text-sm whitespace-pre-wrap bg-gray-800 rounded p-3 overflow-x-auto">{{ JSON.stringify(selectedLog.collectParams, null, 2) }}</pre>
          </div>

          <!-- 错误信息 -->
          <div v-if="selectedLog.errorMessage" class="bg-gray-900 rounded-lg p-4">
            <h4 class="text-lg font-semibold text-red-400 mb-4">错误信息</h4>
            <pre class="text-red-400 text-sm whitespace-pre-wrap bg-gray-800 rounded p-3">{{ selectedLog.errorMessage }}</pre>
          </div>
        </div>

        <div class="flex justify-end mt-6">
          <button
            @click="showDetailModal = false"
            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
          >
            关闭
          </button>
        </div>
      </div>
    </div>

    <!-- 错误详情模态框 -->
    <div v-if="showErrorModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-gray-800 rounded-2xl p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-xl font-bold text-white">错误详情</h3>
          <button
            @click="showErrorModal = false"
            class="text-gray-400 hover:text-white transition-colors"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div v-if="selectedLog" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">错误信息</label>
            <div class="text-white bg-gray-700 p-4 rounded-lg">
              {{ selectedLog.errorMessage }}
            </div>
          </div>
        </div>

        <div class="flex justify-end mt-6">
          <button
            @click="showErrorModal = false"
            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 页面元数据
definePageMeta({
  layout: 'admin',
  middleware: 'auth'
})

// 响应式数据
const loading = ref(false)
const logs = ref([])
const collectSources = ref([])
const showErrorModal = ref(false)
const showDetailModal = ref(false)
const selectedLog = ref(null)
const pagination = ref({
  page: 1,
  limit: 10,
  total: 0,
  totalPages: 0
})

// 筛选器
const filters = ref({
  status: '',
  type: '',
  sourceId: ''
})

// 获取URL参数
const route = useRoute()
if (route.query.sourceId) {
  filters.value.sourceId = route.query.sourceId
}

// 实时更新定时器
let refreshTimer = null

// 页面加载时获取数据
onMounted(() => {
  loadLogs()
  loadCollectSources()
  startRealTimeUpdates()
})

// 页面卸载时清理定时器
onUnmounted(() => {
  stopRealTimeUpdates()
})

// 开始实时更新
function startRealTimeUpdates() {
  // 每5秒刷新一次数据
  refreshTimer = setInterval(() => {
    // 只有当有运行中的日志时才自动刷新
    const hasRunningLogs = logs.value.some(log => log.status === 'running')
    if (hasRunningLogs) {
      loadLogs()
    }
  }, 5000)
}

// 停止实时更新
function stopRealTimeUpdates() {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 加载日志列表
async function loadLogs() {
  try {
    loading.value = true
    const query = new URLSearchParams({
      page: pagination.value.page,
      limit: pagination.value.limit,
      ...filters.value
    })

    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth(`/api/collect/logs?${query}`)

    if (response.code === 200) {
      logs.value = response.data.list || []
      pagination.value = {
        page: response.data.page || 1,
        limit: response.data.limit || 10,
        total: response.data.total || 0,
        totalPages: response.data.totalPages || 0
      }
    }
  } catch (error) {
    console.error('加载日志列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载采集源列表
async function loadCollectSources() {
  try {
    const response = await $fetch('/api/collect/sources')
    if (response.code === 200) {
      collectSources.value = response.data || []
    }
  } catch (error) {
    console.error('加载采集源失败:', error)
  }
}

// 刷新日志列表
function refreshLogs() {
  pagination.value.page = 1
  loadLogs()
}

// 清除筛选
function clearFilters() {
  filters.value = {
    status: '',
    type: '',
    sourceId: ''
  }
  pagination.value.page = 1
  loadLogs()
}

// 分页
function changePage(page) {
  if (page >= 1 && page <= pagination.value.totalPages) {
    pagination.value.page = page
    loadLogs()
  }
}

// 查看日志详情
function viewLogDetail(log) {
  selectedLog.value = log
  showDetailModal.value = true
}

// 查看错误详情
function viewError(log) {
  selectedLog.value = log
  showErrorModal.value = true
}

// 获取类型文本
function getTypeText(type) {
  const types = {
    manual: '手动采集',
    auto: '自动采集',
    test: '测试采集'
  }
  return types[type] || type
}

// 获取状态文本
function getStatusText(status) {
  const statuses = {
    running: '运行中',
    success: '已完成',
    failed: '失败',
    stopped: '已停止'
  }
  return statuses[status] || status
}

// 获取状态样式
function getStatusClass(status) {
  const classes = {
    running: 'bg-blue-500/20 text-blue-400 border border-blue-500/30',
    success: 'bg-green-500/20 text-green-400 border border-green-500/30',
    failed: 'bg-red-500/20 text-red-400 border border-red-500/30',
    stopped: 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
  }
  return classes[status] || 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
}

// 格式化时间
function formatTime(time) {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN')
}
</script>

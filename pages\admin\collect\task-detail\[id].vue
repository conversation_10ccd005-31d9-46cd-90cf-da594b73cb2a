<template>
  <div>
    <div class="mb-8">
      <div class="flex items-center space-x-4 mb-4">
        <NuxtLink to="/admin/collect/tasks" class="text-gray-400 hover:text-white transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
        </NuxtLink>
        <h1 class="text-3xl font-bold text-white">
          {{ task ? `任务详情 - ${task.taskName}` : '任务详情' }}
        </h1>
      </div>
      <p class="mt-2 text-gray-400">查看采集任务的详细信息和执行历史。</p>
    </div>

    <div v-if="loading" class="flex items-center justify-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
      <span class="ml-3 text-gray-300">加载中...</span>
    </div>

    <div v-else-if="task" class="space-y-6">
      <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl p-6">
        <h2 class="text-xl font-bold text-white mb-4">基本信息</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">任务名称</label>
            <div class="text-white">{{ task.taskName }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">任务类型</label>
            <div class="text-white">{{ getTaskTypeText(task.taskType) }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">采集源</label>
            <div class="text-white">{{ task.sourceName }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">状态</label>
            <span :class="getStatusClass(task.status)" class="px-2 py-1 text-xs font-medium rounded-full">
              {{ getStatusText(task.status) }}
            </span>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">创建时间</label>
            <div class="text-white">{{ formatTime(task.createdAt) }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">最后执行</label>
            <div class="text-white">{{ formatTime(task.lastRunTime) }}</div>
          </div>
        </div>
      </div>

      <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl p-6">
        <h2 class="text-xl font-bold text-white mb-4">任务配置</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">执行间隔</label>
            <div class="text-white">{{ task.interval || '单次执行' }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">下次执行时间</label>
            <div class="text-white">{{ formatTime(task.nextRunTime) || '无' }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">采集URL</label>
            <div class="text-white text-sm break-all">{{ task.collectUrl || '无' }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">采集参数</label>
            <div class="text-white text-sm">{{ task.collectParams || '无' }}</div>
          </div>
        </div>
      </div>

      <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-xl font-bold text-white">操作</h2>
        </div>
        <div class="flex flex-wrap gap-3">
          <button v-if="task.status === 'running'" @click="stopTask" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
            停止任务
          </button>
          <button v-else @click="startTask" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
            启动任务
          </button>
          <NuxtLink :to="`/admin/collect/logs?taskId=${task.id}`" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
            查看日志
          </NuxtLink>
          <button @click="refreshTask" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
            刷新
          </button>
        </div>
      </div>
    </div>

    <div v-else class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-300">任务不存在</h3>
      <p class="mt-1 text-sm text-gray-500">找不到指定的任务</p>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin',
  middleware: 'auth'
})

const route = useRoute()
const taskId = computed(() => route.params.id)

const loading = ref(false)
const task = ref(null)

console.log('🚀 任务详情页面加载，任务ID:', taskId.value)

onMounted(() => {
  loadTask()
})

// 监听路由变化，重新加载任务详情
watch(taskId, (newTaskId) => {
  if (newTaskId) {
    console.log('路由变化，重新加载任务:', newTaskId)
    loadTask()
  }
}, { immediate: false })

async function loadTask() {
  try {
    loading.value = true
    const currentTaskId = taskId.value
    
    console.log('开始加载任务详情:', currentTaskId)
    
    const response = await $fetch(`/api/collect/tasks/${currentTaskId}`)
    
    console.log('任务详情API响应:', response)
    
    if (response.success) {
      task.value = response.data
      console.log('任务详情加载成功:', task.value)
    } else {
      console.error('API返回失败:', response.message)
      task.value = null
    }
  } catch (error) {
    console.error('加载任务详情失败:', error)
    task.value = null
  } finally {
    loading.value = false
  }
}

// 停止任务
async function stopTask() {
  try {
    const response = await $fetch(`/api/collect/tasks/${taskId.value}/stop`, {
      method: 'POST'
    })
    
    if (response.success) {
      await loadTask() // 重新加载任务信息
      alert('任务已停止')
    } else {
      alert('停止任务失败: ' + response.message)
    }
  } catch (error) {
    console.error('停止任务失败:', error)
    alert('停止任务失败')
  }
}

// 启动任务
async function startTask() {
  try {
    const response = await $fetch(`/api/collect/tasks/${taskId.value}/start`, {
      method: 'POST'
    })
    
    if (response.success) {
      await loadTask() // 重新加载任务信息
      alert('任务已启动')
    } else {
      alert('启动任务失败: ' + response.message)
    }
  } catch (error) {
    console.error('启动任务失败:', error)
    alert('启动任务失败')
  }
}

// 刷新任务信息
function refreshTask() {
  loadTask()
}

// 工具函数
function getTaskTypeText(type) {
  const types = {
    once: '单次任务',
    daily: '每日任务',
    weekly: '每周任务',
    monthly: '每月任务'
  }
  return types[type] || type
}

function getStatusText(status) {
  const statuses = {
    active: '已完成',
    running: '运行中',
    paused: '已暂停',
    inactive: '失败',
    completed: '已完成',
    failed: '失败',
    stopped: '已停止'
  }
  return statuses[status] || status
}

function getStatusClass(status) {
  const classes = {
    active: 'bg-green-500/20 text-green-400 border border-green-500/30',
    running: 'bg-blue-500/20 text-blue-400 border border-blue-500/30',
    paused: 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30',
    inactive: 'bg-red-500/20 text-red-400 border border-red-500/30',
    completed: 'bg-green-500/20 text-green-400 border border-green-500/30',
    failed: 'bg-red-500/20 text-red-400 border border-red-500/30',
    stopped: 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
  }
  return classes[status] || 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
}

function formatTime(timeString) {
  if (!timeString) return '无'
  try {
    return new Date(timeString).toLocaleString('zh-CN')
  } catch (error) {
    return timeString
  }
}
</script>

<template>
  <div>
    <div class="mb-8">
      <div class="flex items-center space-x-4 mb-4">
        <NuxtLink to="/admin/collect" class="text-gray-400 hover:text-white transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
        </NuxtLink>
        <h1 class="text-3xl font-bold text-white">采集任务管理</h1>
        <div class="ml-auto flex items-center space-x-4">
          <!-- 实时状态指示器 -->
          <div v-if="tasks.some(task => task.status === 'running')" class="flex items-center space-x-2 px-3 py-1 bg-blue-500/20 border border-blue-500/30 rounded-lg">
            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span class="text-xs text-blue-400">任务执行中</span>
          </div>

          <button @click="testFunction" class="inline-flex items-center px-4 py-2 border border-green-600 text-sm font-medium rounded-xl text-green-300 bg-green-700/50 hover:bg-green-600/50 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200">
            🧪 测试
          </button>

          <NuxtLink to="/admin/collect/task-detail/13" class="inline-flex items-center px-4 py-2 border border-purple-600 text-sm font-medium rounded-xl text-purple-300 bg-purple-700/50 hover:bg-purple-600/50 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200">
            🔗 直接链接测试
          </NuxtLink>

          <button @click="refreshTasks" class="inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-xl text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            刷新
          </button>
        </div>
      </div>
      <p class="mt-2 text-gray-400">查看和管理所有采集任务的执行状态。</p>
    </div>

    <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl p-6 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">状态筛选</label>
          <select v-model="filters.status" @change="loadTasks" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-orange-500">
            <option value="">全部状态</option>
            <option value="active">活跃</option>
            <option value="running">运行中</option>
            <option value="paused">暂停</option>
            <option value="inactive">停用</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">任务类型</label>
          <select v-model="filters.taskType" @change="loadTasks" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-orange-500">
            <option value="">全部类型</option>
            <option value="once">单次任务</option>
            <option value="daily">每日任务</option>
            <option value="weekly">每周任务</option>
            <option value="monthly">每月任务</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">采集源</label>
          <select v-model="filters.sourceId" @change="loadTasks" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-orange-500">
            <option value="">全部采集源</option>
            <option v-for="source in collectSources" :key="source.id" :value="source.id">{{ source.name }}</option>
          </select>
        </div>
        <div class="flex items-end">
          <button @click="clearFilters" class="w-full px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">清除筛选</button>
        </div>
      </div>
    </div>

    <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-700">
        <h2 class="text-lg font-semibold text-white">任务列表</h2>
      </div>

      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
        <span class="ml-3 text-gray-300">加载中...</span>
      </div>

      <div v-else-if="tasks.length > 0" class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-700">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">任务信息</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">采集源</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">状态</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">执行统计</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">时间信息</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-700">
            <tr v-for="task in tasks" :key="task.id" class="hover:bg-gray-750 transition-colors">
              <td class="px-6 py-4">
                <div>
                  <div class="text-sm font-medium text-white">{{ task.taskName }}</div>
                  <div class="text-sm text-gray-400">ID: {{ task.id }}</div>
                  <div class="text-xs text-gray-500">{{ getTaskTypeText(task.taskType) }}</div>
                </div>
              </td>
              <td class="px-6 py-4">
                <div>
                  <div class="text-sm text-white">{{ task.sourceName }}</div>
                  <div class="text-xs text-gray-400">{{ task.sourceType }}</div>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="flex items-center space-x-2">
                  <span :class="getStatusClass(task.status)" class="px-2 py-1 text-xs font-medium rounded-full">{{ getStatusText(task.status) }}</span>
                  <!-- 运行中状态显示动画 -->
                  <div v-if="task.status === 'running'" class="flex items-center space-x-1">
                    <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                    <span class="text-xs text-blue-400">执行中</span>
                  </div>
                  <!-- 显示进度百分比 -->
                  <span v-if="task.status === 'running' && task.progress" class="text-xs text-gray-400">
                    {{ task.progress }}%
                  </span>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-gray-300">
                  <div>总执行: {{ task.runCount }}次</div>
                  <div class="text-green-400">成功: {{ task.successCount }}次</div>
                  <div class="text-red-400">失败: {{ task.failCount }}次</div>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-gray-300">
                  <div v-if="task.lastRunTime">最后执行: {{ formatTime(task.lastRunTime) }}</div>
                  <div v-if="task.nextRunTime">下次执行: {{ formatTime(task.nextRunTime) }}</div>
                  <div class="text-xs text-gray-500">创建: {{ formatTime(task.createdAt) }}</div>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="flex items-center space-x-2">
                  <button @click.stop="viewTaskDetail(task)" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors">查看详情</button>
                  <button @click="viewTaskLogs(task)" class="px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded transition-colors">查看日志</button>
                  <button v-if="task.status === 'running'" @click="stopTask(task)" class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors">停止任务</button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div v-else class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-300">暂无任务</h3>
        <p class="mt-1 text-sm text-gray-500">还没有创建任何采集任务</p>
      </div>

      <div v-if="pagination.totalPages > 1" class="px-6 py-4 border-t border-gray-700">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-400">共 {{ pagination.total }} 条记录，第 {{ pagination.page }} / {{ pagination.totalPages }} 页</div>
          <div class="flex items-center space-x-2">
            <button @click="changePage(pagination.page - 1)" :disabled="pagination.page <= 1" class="px-3 py-1 bg-gray-600 hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-white text-sm rounded transition-colors">上一页</button>
            <button @click="changePage(pagination.page + 1)" :disabled="pagination.page >= pagination.totalPages" class="px-3 py-1 bg-gray-600 hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-white text-sm rounded transition-colors">下一页</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'admin',
  middleware: 'auth'
})

// 简单的消息提示函数
const showMessageToast = (type, message) => {
  console.log(`[${type.toUpperCase()}] ${message}`)
  // 这里可以集成实际的toast组件
  alert(`${type}: ${message}`)
}

const loading = ref(false)
const tasks = ref([])
const collectSources = ref([])
const pagination = ref({
  page: 1,
  limit: 10,
  total: 0,
  totalPages: 0
})

const filters = ref({
  status: '',
  taskType: '',
  sourceId: ''
})

// 自动刷新定时器
let refreshTimer = null

onMounted(() => {
  loadTasks()
  loadCollectSources()
  startRealTimeUpdates()
})

onUnmounted(() => {
  stopRealTimeUpdates()
})

// 开始实时更新
function startRealTimeUpdates() {
  // 每5秒刷新一次数据
  refreshTimer = setInterval(() => {
    // 只有当有运行中的任务时才自动刷新
    const hasRunningTasks = tasks.value.some(task => task.status === 'running')
    if (hasRunningTasks) {
      loadTasks()
    }
  }, 5000)
}

// 停止实时更新
function stopRealTimeUpdates() {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

async function loadTasks() {
  try {
    loading.value = true
    const query = new URLSearchParams({
      page: pagination.value.page,
      limit: pagination.value.limit,
      ...filters.value
    })

    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth(`/api/collect/tasks?${query}`)

    if (response.code === 200) {
      tasks.value = response.data.list || []
      pagination.value = {
        page: response.data.page || 1,
        limit: response.data.limit || 10,
        total: response.data.total || 0,
        totalPages: response.data.totalPages || 0
      }
    }
  } catch (error) {
    console.error('加载任务列表失败:', error)
  } finally {
    loading.value = false
  }
}

async function loadCollectSources() {
  try {
    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth('/api/collect/sources')
    if (response.code === 200) {
      collectSources.value = response.data || []
    }
  } catch (error) {
    console.error('加载采集源失败:', error)
  }
}

// 停止任务
async function stopTask(task) {
  try {
    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth(`/api/collect/tasks/${task.id}/stop`, {
      method: 'POST'
    })

    if (response.success) {
      showMessageToast('success', '任务已停止')
      loadTasks() // 重新加载任务列表
    } else {
      showMessageToast('error', '停止任务失败：' + response.message)
    }
  } catch (error) {
    console.error('停止任务失败:', error)
    showMessageToast('error', '停止任务失败')
  }
}

// 获取任务实时状态
async function getTaskRealTimeStatus(taskId) {
  try {
    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth(`/api/collect/tasks/${taskId}/status`)
    return response.data || null
  } catch (error) {
    console.error('获取任务状态失败:', error)
    return null
  }
}

function refreshTasks() {
  pagination.value.page = 1
  loadTasks()
}

function clearFilters() {
  filters.value = {
    status: '',
    taskType: '',
    sourceId: ''
  }
  pagination.value.page = 1
  loadTasks()
}

function changePage(page) {
  if (page >= 1 && page <= pagination.value.totalPages) {
    pagination.value.page = page
    loadTasks()
  }
}

async function viewTaskDetail(task) {
  console.log('🔍 点击查看详情按钮被触发!')
  console.log('📋 任务信息:', task)

  // 使用新的路由路径，避免与 /admin/collect/[id] 冲突
  const targetUrl = `/admin/collect/task-detail/${task.id}`
  console.log('🔗 目标URL:', targetUrl)

  try {
    // 直接使用 window.location 进行跳转
    console.log('🌐 使用 window.location 跳转...')
    if (import.meta.client) {
      window.location.href = targetUrl
      console.log('✅ window.location 调用成功')
    }
  } catch (error) {
    console.error('❌ 跳转失败:', error)
  }
}

function viewTaskLogs(task) {
  navigateTo(`/admin/collect/logs?taskId=${task.id}`)
}

// 测试函数
async function testFunction() {
  console.log('🧪 测试按钮被点击!')
  alert('测试按钮工作正常！JavaScript 功能正常。')

  // 测试 navigateTo 函数
  console.log('🔗 测试 navigateTo 函数...')
  try {
    // 测试跳转到新的路径
    console.log('🚀 测试: 使用 window.location...')
    window.location.href = '/admin/collect/task-detail/13'
    console.log('✅ window.location 测试成功')
  } catch (error) {
    console.error('❌ 测试失败:', error)
    alert('测试失败: ' + error.message)
  }
}

function getTaskTypeText(type) {
  const types = {
    once: '单次任务',
    daily: '每日任务',
    weekly: '每周任务',
    monthly: '每月任务'
  }
  return types[type] || type
}

function getStatusText(status) {
  const statuses = {
    active: '已完成',
    running: '运行中',
    paused: '已暂停',
    inactive: '失败',
    completed: '已完成',
    failed: '失败',
    stopped: '已停止'
  }
  return statuses[status] || status
}

function getStatusClass(status) {
  const classes = {
    active: 'bg-green-500/20 text-green-400 border border-green-500/30',
    running: 'bg-blue-500/20 text-blue-400 border border-blue-500/30',
    paused: 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30',
    inactive: 'bg-red-500/20 text-red-400 border border-red-500/30',
    completed: 'bg-green-500/20 text-green-400 border border-green-500/30',
    failed: 'bg-red-500/20 text-red-400 border border-red-500/30',
    stopped: 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
  }
  return classes[status] || 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
}

function formatTime(time) {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN')
}
</script>

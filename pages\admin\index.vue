<template>
  <div>
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-white">仪表盘</h1>
      <p class="mt-2 text-gray-400">欢迎回来，管理员！这里是系统概览。</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
      <span class="ml-3 text-gray-400">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="bg-red-900/20 border border-red-500/50 rounded-lg p-4 mb-6">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="text-red-400 font-medium">{{ error }}</span>
        <button @click="fetchDashboardData" class="ml-auto px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors">
          重试
        </button>
      </div>
    </div>

    <!-- 主要内容 -->
    <div v-else>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- 总视频数 -->
      <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 overflow-hidden shadow-xl rounded-2xl hover:shadow-2xl transition-all duration-300 group">
        <div class="p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25 group-hover:scale-110 transition-transform">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-400 truncate">总视频数</dt>
                <dd class="text-2xl font-bold text-white">{{ stats.totalVideos }}</dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-gray-700/30 px-6 py-3 border-t border-gray-700/50">
          <div class="text-sm">
            <span class="text-green-400 font-medium">+12%</span>
            <span class="text-gray-400">较上月</span>
          </div>
        </div>
      </div>

      <!-- 总分类数 -->
      <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 overflow-hidden shadow-xl rounded-2xl hover:shadow-2xl transition-all duration-300 group">
        <div class="p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25 group-hover:scale-110 transition-transform">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-4H5m14 8H5" />
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-400 truncate">总分类数</dt>
                <dd class="text-2xl font-bold text-white">{{ stats.totalCategories }}</dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-gray-700/30 px-6 py-3 border-t border-gray-700/50">
          <div class="text-sm">
            <span class="text-green-400 font-medium">+3</span>
            <span class="text-gray-400">新增分类</span>
          </div>
        </div>
      </div>

      <!-- 今日观看 -->
      <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 overflow-hidden shadow-xl rounded-2xl hover:shadow-2xl transition-all duration-300 group">
        <div class="p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25 group-hover:scale-110 transition-transform">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-400 truncate">今日观看</dt>
                <dd class="text-2xl font-bold text-white">{{ stats.todayViews }}</dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-gray-700/30 px-6 py-3 border-t border-gray-700/50">
          <div class="text-sm">
            <span class="text-red-400 font-medium">-5%</span>
            <span class="text-gray-400">较昨日</span>
          </div>
        </div>
      </div>

      <!-- 管理员数 -->
      <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 overflow-hidden shadow-xl rounded-2xl hover:shadow-2xl transition-all duration-300 group">
        <div class="p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg shadow-purple-500/25 group-hover:scale-110 transition-transform">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-400 truncate">管理员数</dt>
                <dd class="text-2xl font-bold text-white">{{ stats.totalAdmins }}</dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-gray-700/30 px-6 py-3 border-t border-gray-700/50">
          <div class="text-sm">
            <span class="text-purple-400 font-medium">{{ stats.activeAdmins }}</span>
            <span class="text-gray-400">在线</span>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- 最近上传的视频 -->
      <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl">
        <div class="px-6 py-4 border-b border-gray-700/50">
          <h3 class="text-lg font-medium text-white">最近上传的视频</h3>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div
              v-for="video in recentVideos"
              :key="video.id"
              class="flex items-center space-x-4 p-3 rounded-xl hover:bg-gray-700/30 transition-colors"
            >
              <div class="w-16 h-12 bg-gray-700/50 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-white truncate">{{ video.title }}</p>
                <p class="text-sm text-gray-400">{{ video.duration }} • {{ video.uploadTime }}</p>
              </div>
              <div class="flex items-center space-x-2">
                <span
                  :class="[
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    video.status === '已发布' ? 'bg-green-500/20 text-green-400' :
                    video.status === '审核中' ? 'bg-yellow-500/20 text-yellow-400' :
                    'bg-gray-500/20 text-gray-400'
                  ]"
                >
                  {{ video.status }}
                </span>
              </div>
            </div>
          </div>
          <div class="mt-6">
            <NuxtLink
              to="/admin/videos"
              class="text-sm font-medium text-orange-400 hover:text-orange-300 transition-colors"
            >
              查看所有视频 →
            </NuxtLink>
          </div>
        </div>
      </div>

      <!-- 系统状态 -->
      <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl">
        <div class="px-6 py-4 border-b border-gray-700/50">
          <h3 class="text-lg font-medium text-white">系统状态</h3>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <!-- 服务器状态 -->
            <div class="flex items-center justify-between p-3 rounded-xl hover:bg-gray-700/30 transition-colors">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-green-400 rounded-full mr-3 shadow-sm shadow-green-400/50"></div>
                <span class="text-sm font-medium text-white">服务器状态</span>
              </div>
              <span class="text-sm text-green-400 font-medium">正常</span>
            </div>

            <!-- 数据库状态 -->
            <div class="flex items-center justify-between p-3 rounded-xl hover:bg-gray-700/30 transition-colors">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-green-400 rounded-full mr-3 shadow-sm shadow-green-400/50"></div>
                <span class="text-sm font-medium text-white">数据库状态</span>
              </div>
              <span class="text-sm text-green-400 font-medium">正常</span>
            </div>

            <!-- 存储空间 -->
            <div class="flex items-center justify-between p-3 rounded-xl hover:bg-gray-700/30 transition-colors">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-yellow-400 rounded-full mr-3 shadow-sm shadow-yellow-400/50"></div>
                <span class="text-sm font-medium text-white">存储空间</span>
              </div>
              <span class="text-sm text-yellow-400 font-medium">75% 已使用</span>
            </div>

            <!-- API状态 -->
            <div class="flex items-center justify-between p-3 rounded-xl hover:bg-gray-700/30 transition-colors">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-green-400 rounded-full mr-3 shadow-sm shadow-green-400/50"></div>
                <span class="text-sm font-medium text-white">API状态</span>
              </div>
              <span class="text-sm text-green-400 font-medium">正常</span>
            </div>
          </div>

          <!-- 系统信息 -->
          <div class="mt-6 pt-6 border-t border-gray-700/50">
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-400">系统版本:</span>
                <span class="ml-2 font-medium text-white">v1.0.0</span>
              </div>
              <div>
                <span class="text-gray-400">运行时间:</span>
                <span class="ml-2 font-medium text-white">15天</span>
              </div>
              <div>
                <span class="text-gray-400">CPU使用率:</span>
                <span class="ml-2 font-medium text-white">45%</span>
              </div>
              <div>
                <span class="text-gray-400">内存使用:</span>
                <span class="ml-2 font-medium text-white">2.1GB</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div> <!-- 结束主要内容 -->
  </div>
</template>

<script setup>
// 设置布局和认证
definePageMeta({
  layout: 'admin',
  middleware: 'auth'
})

// 统计数据
const stats = ref({
  totalVideos: 0,
  totalCategories: 0,
  todayViews: 0,
  totalAdmins: 0,
  activeAdmins: 0
})

// 加载状态
const loading = ref(true)
const error = ref(null)

// 最近上传的视频
const recentVideos = ref([])

// 系统信息
const systemInfo = ref({
  version: '',
  uptime: 0,
  environment: '',
  nodeVersion: '',
  platform: '',
  memory: { used: 0, total: 0 }
})

// 获取仪表盘数据
const fetchDashboardData = async () => {
  try {
    loading.value = true
    error.value = null

    // 从localStorage获取token
    const token = localStorage.getItem('admin_token')
    if (!token) {
      throw new Error('未找到认证令牌')
    }

    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth('/api/admin/dashboard')

    if (response.success) {
      // 更新统计数据
      stats.value = {
        totalVideos: response.data.stats.totalVideos,
        totalCategories: response.data.stats.totalCategories,
        todayViews: response.data.stats.todayViews,
        totalAdmins: response.data.stats.totalAdmins,
        activeAdmins: response.data.stats.activeAdmins
      }

      // 更新最近视频
      recentVideos.value = response.data.recentActivity.map(video => ({
        id: video.id,
        title: video.title,
        duration: video.duration,
        uploadTime: formatUploadTime(video.uploadTime),
        status: video.status === 'active' ? '已发布' : '审核中',
        views: video.views
      }))

      // 更新系统信息
      systemInfo.value = response.data.systemInfo
    }
  } catch (err) {
    console.error('获取仪表盘数据失败:', err)
    error.value = err.message || '获取数据失败'
  } finally {
    loading.value = false
  }
}

// 格式化上传时间
const formatUploadTime = (dateString) => {
  const now = new Date()
  const uploadDate = new Date(dateString)
  const diffMs = now - uploadDate
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffHours / 24)

  if (diffDays > 0) {
    return `${diffDays}天前`
  } else if (diffHours > 0) {
    return `${diffHours}小时前`
  } else {
    return '刚刚'
  }
}

// 格式化运行时间
const formatUptime = (seconds) => {
  const days = Math.floor(seconds / (24 * 60 * 60))
  const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60))
  const minutes = Math.floor((seconds % (60 * 60)) / 60)

  if (days > 0) {
    return `${days}天 ${hours}小时`
  } else if (hours > 0) {
    return `${hours}小时 ${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

// 页面挂载时获取数据
onMounted(() => {
  fetchDashboardData()
})

// 页面标题
useHead({
  title: '仪表盘 - 91JSPG.COM 管理后台'
})
</script>

<template>
  <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-gray-900 to-gray-800 py-12 px-4 sm:px-6 lg:px-8">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl"></div>
      <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-red-500/10 rounded-full blur-3xl"></div>
    </div>
    
    <div class="relative max-w-md w-full space-y-8">
      <!-- Logo 和标题 -->
      <div class="text-center">
        <div class="mx-auto w-20 h-20 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center shadow-2xl shadow-orange-500/25">
          <span class="text-white font-bold text-3xl">J</span>
        </div>
        <h2 class="mt-6 text-3xl font-extrabold text-white">
          管理员登录
        </h2>
        <p class="mt-2 text-sm text-gray-400">
          91JSPG.COM 后台管理系统
        </p>
      </div>

      <!-- 登录表单 -->
      <form class="mt-8 space-y-6" @submit.prevent="handleLogin">
        <div class="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50 shadow-2xl">
          <div class="space-y-6">
            <!-- 用户名输入框 -->
            <div>
              <label for="username" class="block text-sm font-medium text-gray-300 mb-2">
                用户名
              </label>
              <input
                id="username"
                v-model="loginForm.username"
                name="username"
                type="text"
                required
                class="appearance-none relative block w-full px-4 py-3 border border-gray-600/50 placeholder-gray-400 text-white bg-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
                placeholder="请输入用户名"
              >
            </div>

            <!-- 密码输入框 -->
            <div>
              <label for="password" class="block text-sm font-medium text-gray-300 mb-2">
                密码
              </label>
              <div class="relative">
                <input
                  id="password"
                  v-model="loginForm.password"
                  name="password"
                  :type="showPassword ? 'text' : 'password'"
                  required
                  class="appearance-none relative block w-full px-4 py-3 pr-12 border border-gray-600/50 placeholder-gray-400 text-white bg-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
                  placeholder="请输入密码"
                >
                <button
                  type="button"
                  @click="togglePasswordVisibility"
                  class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-orange-400 transition-colors"
                >
                  <svg
                    v-if="showPassword"
                    class="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  <svg
                    v-else
                    class="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                  </svg>
                </button>
              </div>
            </div>

            <!-- 验证码输入框 -->
            <div>
              <label for="captcha" class="block text-sm font-medium text-gray-300 mb-2">
                验证码
              </label>
              <div class="flex space-x-3">
                <input
                  id="captcha"
                  v-model="loginForm.captcha"
                  name="captcha"
                  type="text"
                  required
                  class="flex-1 appearance-none relative block px-4 py-3 border border-gray-600/50 placeholder-gray-400 text-white bg-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
                  placeholder="请输入验证码"
                >
                <div
                  @click="refreshCaptcha"
                  class="w-28 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center cursor-pointer hover:from-orange-600 hover:to-red-600 transition-all duration-200 shadow-lg shadow-orange-500/25"
                >
                  <span class="text-lg font-mono text-white select-none font-bold">{{ captchaCode }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 记住我 -->
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <input
              id="remember-me"
              v-model="loginForm.rememberMe"
              name="remember-me"
              type="checkbox"
              class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-600 bg-gray-700 rounded"
            >
            <label for="remember-me" class="ml-2 block text-sm text-gray-300">
              记住我
            </label>
          </div>
          <div class="text-sm">
            <a href="#" class="font-medium text-orange-400 hover:text-orange-300 transition-colors">
              忘记密码？
            </a>
          </div>
        </div>

        <!-- 错误提示 -->
        <div v-if="errorMessage" class="bg-red-500/10 border border-red-500/20 rounded-xl p-4 backdrop-blur-sm">
          <div class="flex">
            <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
            <p class="ml-2 text-sm text-red-300">{{ errorMessage }}</p>
          </div>
        </div>

        <!-- 登录按钮 -->
        <div>
          <button
            type="submit"
            :disabled="isLoading"
            class="group relative w-full flex justify-center py-4 px-4 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg shadow-orange-500/25 hover:shadow-orange-500/40"
          >
            <span v-if="isLoading" class="absolute left-0 inset-y-0 flex items-center pl-3">
              <svg class="h-5 w-5 text-white animate-spin" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </span>
            {{ isLoading ? '登录中...' : '登录' }}
          </button>
        </div>
      </form>

      <!-- 底部信息 -->
      <div class="text-center">
        <p class="text-xs text-gray-500">
          © 2024 91JSPG.COM. 保留所有权利.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
// 设置布局为空（不使用默认布局）
definePageMeta({
  layout: false
})

// 响应式数据
const loginForm = ref({
  username: '',
  password: '',
  captcha: '',
  rememberMe: false
})

const showPassword = ref(false)
const isLoading = ref(false)
const errorMessage = ref('')
const captchaCode = ref('')

// 生成验证码
const generateCaptcha = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// 刷新验证码
const refreshCaptcha = () => {
  captchaCode.value = generateCaptcha()
}

// 切换密码可见性
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// 处理登录
const handleLogin = async () => {
  errorMessage.value = ''
  
  // 验证表单
  if (!loginForm.value.username || !loginForm.value.password || !loginForm.value.captcha) {
    errorMessage.value = '请填写完整的登录信息'
    return
  }

  // 验证验证码
  if (loginForm.value.captcha.toUpperCase() !== captchaCode.value) {
    errorMessage.value = '验证码错误'
    refreshCaptcha()
    loginForm.value.captcha = ''
    return
  }

  isLoading.value = true

  try {
    // 调用后端登录API
    const { apiAdmin } = useApi()
    const response = await apiAdmin('/api/admin/login', {
      method: 'POST',
      body: {
        username: loginForm.value.username,
        password: loginForm.value.password
      }
    })

    if (response.success) {
      // 保存登录状态和token
      const { setAuth } = useAuth()
      setAuth(response.data.token, response.data.admin)

      // 跳转到管理后台
      await navigateTo('/admin')
    } else {
      errorMessage.value = response.message || '登录失败'
      refreshCaptcha()
      loginForm.value.captcha = ''
    }
  } catch (error) {
    console.error('登录错误:', error)
    if (error.data && error.data.message) {
      errorMessage.value = error.data.message
    } else {
      errorMessage.value = '登录失败，请稍后重试'
    }
    refreshCaptcha()
    loginForm.value.captcha = ''
  } finally {
    isLoading.value = false
  }
}

// 页面加载时生成验证码
onMounted(() => {
  refreshCaptcha()
})
</script>

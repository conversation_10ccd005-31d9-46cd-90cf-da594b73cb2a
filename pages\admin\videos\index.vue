<template>
  <div>
    <!-- 页面标题和操作 -->
    <div class="sm:flex sm:items-center sm:justify-between mb-8">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">视频管理</h1>
        <p class="mt-1 text-sm text-gray-600">管理所有视频内容，包括上传、编辑和删除。</p>
      </div>
      <div class="mt-4 sm:mt-0">
        <button
          @click="showAddModal = true"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          添加视频
        </button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl mb-6">
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- 搜索框 -->
          <div class="md:col-span-2">
            <label for="search" class="block text-sm font-medium text-gray-300 mb-2">搜索视频</label>
            <div class="relative">
              <input
                id="search"
                v-model="searchQuery"
                type="text"
                placeholder="搜索标题、演员或编号..."
                class="block w-full pl-10 pr-3 py-3 border border-gray-600/50 rounded-xl leading-5 bg-gray-700/50 placeholder-gray-400 text-white focus:outline-none focus:placeholder-gray-300 focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
              >
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>

          <!-- 分类筛选 -->
          <div>
            <label for="category" class="block text-sm font-medium text-gray-300 mb-2">分类</label>
            <select
              id="category"
              v-model="selectedCategory"
              class="block w-full pl-3 pr-10 py-3 text-base border border-gray-600/50 bg-gray-700/50 text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm rounded-xl"
            >
              <option value="">所有分类</option>
              <option v-for="category in categories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>

          <!-- 状态筛选 -->
          <div>
            <label for="status" class="block text-sm font-medium text-gray-300 mb-2">状态</label>
            <select
              id="status"
              v-model="selectedStatus"
              class="block w-full pl-3 pr-10 py-3 text-base border border-gray-600/50 bg-gray-700/50 text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm rounded-xl"
            >
              <option value="">所有状态</option>
              <option value="已发布">已发布</option>
              <option value="审核中">审核中</option>
              <option value="草稿">草稿</option>
              <option value="已下架">已下架</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
      <span class="ml-3 text-gray-400">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="bg-red-900/20 border border-red-500/50 rounded-lg p-4 mb-6">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="text-red-400 font-medium">{{ error }}</span>
        <button @click="fetchVideos" class="ml-auto px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors">
          重试
        </button>
      </div>
    </div>

    <!-- 视频列表 -->
    <div v-else class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 shadow-xl rounded-2xl">
      <div class="px-6 py-4 border-b border-gray-700/50">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-white">视频列表</h3>
          <div class="flex items-center space-x-2">
            <span class="text-sm text-gray-400">共 {{ pagination.totalItems }} 个视频</span>
            <div class="flex items-center space-x-1">
              <button
                @click="viewMode = 'grid'"
                :class="[
                  'p-2 rounded-lg transition-colors',
                  viewMode === 'grid' ? 'bg-orange-500/20 text-orange-400' : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                ]"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
              </button>
              <button
                @click="viewMode = 'list'"
                :class="[
                  'p-2 rounded-lg transition-colors',
                  viewMode === 'list' ? 'bg-orange-500/20 text-orange-400' : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                ]"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="p-6">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <div
            v-for="video in paginatedVideos"
            :key="video.id"
            class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl overflow-hidden hover:scale-105 hover:shadow-xl hover:shadow-orange-500/10 transition-all duration-200"
          >
            <!-- 视频缩略图 -->
            <div class="aspect-video bg-gray-700/50 flex items-center justify-center relative group">
              <img
                v-if="video.coverUrl"
                :src="video.coverUrl"
                :alt="video.title"
                class="w-full h-full object-cover"
              >
              <div v-else class="flex items-center justify-center">
                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </div>
              <!-- 播放按钮覆盖层 -->
              <div class="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                <div class="w-12 h-12 bg-orange-500/80 rounded-full flex items-center justify-center">
                  <svg class="w-6 h-6 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </div>
              </div>
            </div>

            <!-- 视频信息 -->
            <div class="p-4">
              <h4 class="text-sm font-medium text-white line-clamp-2 mb-2">{{ video.title }}</h4>
              <div class="flex items-center justify-between text-xs text-gray-400 mb-2">
                <span>{{ video.duration }}</span>
                <span>{{ video.views }} 次观看</span>
              </div>
              <div class="flex items-center justify-between">
                <span
                  :class="[
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    video.status === '已发布' ? 'bg-green-500/20 text-green-400' :
                    video.status === '审核中' ? 'bg-yellow-500/20 text-yellow-400' :
                    video.status === '草稿' ? 'bg-gray-500/20 text-gray-400' :
                    'bg-red-500/20 text-red-400'
                  ]"
                >
                  {{ video.status }}
                </span>
                <div class="flex items-center space-x-1">
                  <button
                    @click="editVideo(video)"
                    class="p-1 text-gray-400 hover:text-orange-400 transition-colors"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    @click="deleteVideo(video)"
                    class="p-1 text-gray-400 hover:text-red-400 transition-colors"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-else class="overflow-hidden">
        <table class="min-w-full divide-y divide-gray-700/50">
          <thead class="bg-gray-700/30">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">视频</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">分类</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">状态</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">观看次数</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">上传时间</th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-700/50">
            <tr v-for="video in paginatedVideos" :key="video.id" class="hover:bg-gray-700/30 transition-colors">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-16 h-12 bg-gray-700/50 rounded-lg flex items-center justify-center mr-4 overflow-hidden">
                    <img
                      v-if="video.coverUrl"
                      :src="video.coverUrl"
                      :alt="video.title"
                      class="w-full h-full object-cover"
                    >
                    <svg v-else class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <div class="text-sm font-medium text-white max-w-xs truncate">{{ video.title }}</div>
                    <div class="text-sm text-gray-400">{{ video.duration }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{{ video.category }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  :class="[
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    video.status === '已发布' ? 'bg-green-500/20 text-green-400' :
                    video.status === '审核中' ? 'bg-yellow-500/20 text-yellow-400' :
                    video.status === '草稿' ? 'bg-gray-500/20 text-gray-400' :
                    'bg-red-500/20 text-red-400'
                  ]"
                >
                  {{ video.status }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{{ video.views }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">{{ video.uploadTime }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                  <button
                    @click="editVideo(video)"
                    class="text-orange-400 hover:text-orange-300 transition-colors"
                  >
                    编辑
                  </button>
                  <button
                    @click="deleteVideo(video)"
                    class="text-red-400 hover:text-red-300 transition-colors"
                  >
                    删除
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="bg-gray-700/30 px-4 py-3 flex items-center justify-between border-t border-gray-700/50 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <button
            @click="previousPage"
            :disabled="currentPage === 1"
            class="relative inline-flex items-center px-4 py-2 border border-gray-600/50 text-sm font-medium rounded-xl text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            上一页
          </button>
          <button
            @click="nextPage"
            :disabled="currentPage === totalPages"
            class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-600/50 text-sm font-medium rounded-xl text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            下一页
          </button>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-400">
              显示第 <span class="font-medium text-white">{{ (pagination.currentPage - 1) * pagination.itemsPerPage + 1 }}</span> 到
              <span class="font-medium text-white">{{ Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems) }}</span> 条，
              共 <span class="font-medium text-white">{{ pagination.totalItems }}</span> 条记录
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-xl shadow-sm -space-x-px">
              <button
                @click="previousPage"
                :disabled="currentPage === 1"
                class="relative inline-flex items-center px-2 py-2 rounded-l-xl border border-gray-600/50 bg-gray-700/50 text-sm font-medium text-gray-400 hover:bg-gray-600/50 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </button>
              <button
                v-for="page in visiblePages"
                :key="page"
                @click="goToPage(page)"
                :class="[
                  'relative inline-flex items-center px-4 py-2 border text-sm font-medium transition-colors',
                  page === currentPage
                    ? 'z-10 bg-orange-500/20 border-orange-500/50 text-orange-400'
                    : 'bg-gray-700/50 border-gray-600/50 text-gray-400 hover:bg-gray-600/50 hover:text-white'
                ]"
              >
                {{ page }}
              </button>
              <button
                @click="nextPage"
                :disabled="currentPage === totalPages"
                class="relative inline-flex items-center px-2 py-2 rounded-r-xl border border-gray-600/50 bg-gray-700/50 text-sm font-medium text-gray-400 hover:bg-gray-600/50 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑视频模态框 -->
    <div
      v-show="showAddModal || showEditModal"
      class="fixed inset-0 bg-gray-900/75 backdrop-blur-sm overflow-y-auto h-full w-full z-50"
      @click="closeModal"
    >
      <div
        class="relative top-10 mx-auto p-6 border w-full max-w-4xl shadow-2xl rounded-2xl bg-gray-800/95 backdrop-blur-sm border-gray-700/50 m-4"
        @click.stop
      >
        <div class="mt-3">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-2xl font-bold text-white">
              {{ showAddModal ? '添加视频' : '编辑视频' }}
            </h3>
            <button
              @click="closeModal"
              class="text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-700/50 rounded-lg"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form @submit.prevent="saveVideo">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <!-- 左侧表单 -->
              <div class="space-y-6">
                <!-- 视频标题 -->
                <div>
                  <label for="videoTitle" class="block text-sm font-medium text-gray-300 mb-2">
                    视频标题 <span class="text-red-400">*</span>
                  </label>
                  <input
                    id="videoTitle"
                    v-model="videoForm.title"
                    type="text"
                    required
                    class="block w-full px-4 py-3 border border-gray-600/50 placeholder-gray-400 text-white bg-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
                    placeholder="请输入视频标题"
                  >
                </div>

                <!-- 观看次数 -->
                <div>
                  <label for="videoViews" class="block text-sm font-medium text-gray-300 mb-2">
                    观看次数
                  </label>
                  <input
                    id="videoViews"
                    v-model="videoForm.views"
                    type="number"
                    min="0"
                    class="block w-full px-4 py-3 border border-gray-600/50 placeholder-gray-400 text-white bg-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
                    placeholder="请输入观看次数"
                  >
                </div>

                <!-- 分类 -->
                <div>
                  <label for="videoCategory" class="block text-sm font-medium text-gray-300 mb-2">
                    分类 <span class="text-red-400">*</span>
                  </label>
                  <select
                    id="videoCategory"
                    v-model="videoForm.category"
                    required
                    class="block w-full px-4 py-3 border border-gray-600/50 text-white bg-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
                  >
                    <option value="">请选择分类</option>
                    <option v-for="category in categories" :key="category.id" :value="category.id">
                      {{ category.name }}
                    </option>
                  </select>
                </div>

                <!-- 时长 -->
                <div>
                  <label for="videoDuration" class="block text-sm font-medium text-gray-300 mb-2">
                    时长
                  </label>
                  <input
                    id="videoDuration"
                    v-model="videoForm.duration"
                    type="text"
                    class="block w-full px-4 py-3 border border-gray-600/50 placeholder-gray-400 text-white bg-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
                    placeholder="例如: 1:57:47"
                  >
                </div>

                <!-- 状态 -->
                <div>
                  <label for="videoStatus" class="block text-sm font-medium text-gray-300 mb-2">
                    状态
                  </label>
                  <select
                    id="videoStatus"
                    v-model="videoForm.status"
                    class="block w-full px-4 py-3 border border-gray-600/50 text-white bg-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
                  >
                    <option value="已发布">已发布</option>
                    <option value="审核中">审核中</option>
                    <option value="草稿">草稿</option>
                    <option value="已下架">已下架</option>
                  </select>
                </div>
              </div>

              <!-- 右侧表单 -->
              <div class="space-y-6">
                <!-- 视频链接 -->
                <div>
                  <label for="videoUrl" class="block text-sm font-medium text-gray-300 mb-2">
                    视频链接 <span class="text-red-400">*</span>
                  </label>
                  <input
                    id="videoUrl"
                    v-model="videoForm.videoUrl"
                    type="url"
                    required
                    class="block w-full px-4 py-3 border border-gray-600/50 placeholder-gray-400 text-white bg-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
                    placeholder="请输入视频链接 (OSS存储地址)"
                  >
                  <p class="mt-1 text-xs text-gray-500">请输入完整的视频文件URL地址</p>
                </div>

                <!-- 封面链接 -->
                <div>
                  <label for="coverUrl" class="block text-sm font-medium text-gray-300 mb-2">
                    封面链接
                  </label>
                  <input
                    id="coverUrl"
                    v-model="videoForm.coverUrl"
                    type="url"
                    class="block w-full px-4 py-3 border border-gray-600/50 placeholder-gray-400 text-white bg-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
                    placeholder="请输入封面图片链接"
                  >
                  <p class="mt-1 text-xs text-gray-500">建议尺寸 16:9，支持 JPG, PNG 格式</p>

                  <!-- 封面预览 -->
                  <div v-if="videoForm.coverUrl" class="mt-3">
                    <div class="aspect-video bg-gray-700/50 rounded-xl overflow-hidden">
                      <img
                        :src="videoForm.coverUrl"
                        alt="封面预览"
                        class="w-full h-full object-cover"
                        @error="handleImageError"
                      >
                    </div>
                    <p class="mt-1 text-xs text-gray-500">封面预览</p>
                  </div>
                </div>

                <!-- 标签 -->
                <div>
                  <label for="videoTags" class="block text-sm font-medium text-gray-300 mb-2">
                    标签
                  </label>
                  <input
                    id="videoTags"
                    v-model="videoForm.tags"
                    type="text"
                    class="block w-full px-4 py-3 border border-gray-600/50 placeholder-gray-400 text-white bg-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
                    placeholder="用逗号分隔多个标签"
                  >
                </div>
              </div>

              <!-- 描述 (跨两列) -->
              <div class="lg:col-span-2">
                <label for="videoDescription" class="block text-sm font-medium text-gray-300 mb-2">
                  视频描述
                </label>
                <textarea
                  id="videoDescription"
                  v-model="videoForm.description"
                  rows="4"
                  class="block w-full px-4 py-3 border border-gray-600/50 placeholder-gray-400 text-white bg-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 sm:text-sm"
                  placeholder="请输入视频描述"
                ></textarea>
              </div>
            </div>

            <div class="flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-700/50">
              <button
                type="button"
                @click="closeModal"
                class="px-6 py-3 border border-gray-600/50 rounded-xl text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-700/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200"
              >
                取消
              </button>
              <button
                type="submit"
                class="px-6 py-3 border border-transparent rounded-xl text-sm font-medium text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200 shadow-lg shadow-orange-500/25"
              >
                {{ showAddModal ? '添加视频' : '保存修改' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 设置布局和认证
definePageMeta({
  layout: 'admin',
  middleware: 'auth'
})

// 响应式数据
const searchQuery = ref('')
const selectedCategory = ref('')
const selectedStatus = ref('')
const viewMode = ref('grid')
const showAddModal = ref(false)
const showEditModal = ref(false)
const currentPage = ref(1)
const pageSize = ref(12)
const editingVideo = ref(null)

// 视频表单数据
const videoForm = ref({
  title: '',
  videoUrl: '',
  coverUrl: '',
  category: '',
  duration: '',
  status: '已发布',
  views: 0,
  tags: '',
  description: ''
})

// 视频数据
const videos = ref([])
const loading = ref(true)
const error = ref(null)
const pagination = ref({
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
  itemsPerPage: 20
})

// 分类数据
const categories = ref([])

// 获取分类列表
const fetchCategories = async () => {
  try {
    const { getToken } = useAuth()
    const token = getToken()
    if (!token) {
      throw new Error('未找到认证令牌')
    }

    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth('/api/categories')

    if (response.success) {
      // 处理不同的返回格式
      if (Array.isArray(response.data)) {
        // 没有分页参数时，直接返回数组
        categories.value = response.data
      } else {
        // 有分页参数时，返回对象格式
        categories.value = response.data.categories || []
      }
    }
  } catch (err) {
    console.error('获取分类列表失败:', err)
  }
}

// 获取视频列表
const fetchVideos = async () => {
  try {
    loading.value = true
    error.value = null

    const { getToken } = useAuth()
    const token = getToken()
    if (!token) {
      throw new Error('未找到认证令牌')
    }

    // 构建查询参数
    const params = new URLSearchParams({
      page: currentPage.value.toString(),
      limit: pagination.value.itemsPerPage.toString()
    })

    if (searchQuery.value) {
      params.append('search', searchQuery.value)
    }
    if (selectedCategory.value) {
      params.append('category_id', selectedCategory.value)
    }
    if (selectedStatus.value) {
      params.append('status', selectedStatus.value === '已发布' ? 'active' : 'inactive')
    }

    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth(`/api/admin/videos?${params}`)

    if (response.success) {
      videos.value = response.data.videos.map(video => ({
        id: video.id,
        title: video.title,
        category: video.categoryName || '未分类',
        duration: video.duration || '0:00:00',
        status: video.status === 'active' ? '已发布' : '审核中',
        views: video.views?.toLocaleString() || '0',
        uploadTime: formatDate(video.createdAt),
        coverUrl: video.coverUrl || '',
        videoUrl: video.videoUrl || '',
        description: video.description || '',
        tags: video.tags || [],
        featured: video.featured || false
      }))

      pagination.value = {
        currentPage: response.data.pagination.page,
        totalPages: response.data.pagination.totalPages,
        totalItems: response.data.pagination.total,
        itemsPerPage: response.data.pagination.limit
      }
    }
  } catch (err) {
    console.error('获取视频列表失败:', err)
    error.value = err.message || '获取视频列表失败'
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toISOString().split('T')[0]
}

// 搜索功能
const performSearch = () => {
  currentPage.value = 1
  fetchVideos()
}

// 防抖搜索
let searchTimeout = null
const debouncedSearch = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    performSearch()
  }, 500) // 500ms 防抖
}

// 监听搜索条件变化
watch([searchQuery, selectedCategory, selectedStatus], () => {
  debouncedSearch()
})

// 计算属性 - 现在直接使用从API获取的数据
const filteredVideos = computed(() => {
  return videos.value
})

const totalPages = computed(() => pagination.value.totalPages)

const paginatedVideos = computed(() => {
  return filteredVideos.value
})

const visiblePages = computed(() => {
  const pages = []
  const start = Math.max(1, currentPage.value - 2)
  const end = Math.min(totalPages.value, currentPage.value + 2)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// 方法
const editVideo = (video) => {
  editingVideo.value = video
  videoForm.value = {
    title: video.title,
    videoUrl: video.videoUrl || '',
    coverUrl: video.coverUrl || '',
    category: video.category,
    duration: video.duration,
    status: video.status,
    views: parseInt(video.views.replace(/,/g, '')) || 0,
    tags: '',
    description: ''
  }
  showEditModal.value = true
}

const deleteVideo = async (video) => {
  if (!confirm(`确定要删除视频 "${video.title}" 吗？`)) {
    return
  }

  try {
    const { getToken } = useAuth()
    const token = getToken()
    if (!token) {
      throw new Error('未找到认证令牌')
    }

    const { apiAdminAuth } = useApi()
    const response = await apiAdminAuth(`/api/admin/videos/${video.id}`, {
      method: 'DELETE'
    })

    if (response.success) {
      // 重新获取视频列表
      await fetchVideos()
      alert('视频删除成功')
    }
  } catch (err) {
    console.error('删除视频失败:', err)
    alert(err.message || '删除视频失败')
  }
}

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
    fetchVideos()
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
    fetchVideos()
  }
}

const goToPage = (page) => {
  currentPage.value = page
  fetchVideos()
}

// 保存视频
const saveVideo = async () => {
  try {
    const { getToken } = useAuth()
    const token = getToken()
    if (!token) {
      throw new Error('未找到认证令牌')
    }

    const videoData = {
      title: videoForm.value.title,
      description: videoForm.value.description,
      cover_url: videoForm.value.coverUrl,
      video_url: videoForm.value.videoUrl,
      category_id: videoForm.value.category ? parseInt(videoForm.value.category) : null,
      tags: videoForm.value.tags ? videoForm.value.tags.split(',').map(tag => tag.trim()) : [],
      duration: videoForm.value.duration,
      status: videoForm.value.status === '已发布' ? 'active' : 'inactive',
      featured: videoForm.value.featured || false
    }

    const { apiAdminAuth } = useApi()
    let response
    if (!editingVideo.value) {
      // 添加新视频
      response = await apiAdminAuth('/api/admin/videos', {
        method: 'POST',
        body: videoData
      })
    } else {
      // 编辑现有视频
      response = await apiAdminAuth(`/api/admin/videos/${editingVideo.value.id}`, {
        method: 'PUT',
        body: videoData
      })
    }

    if (response.success) {
      closeModal()
      await fetchVideos()
      alert(editingVideo.value ? '视频更新成功' : '视频添加成功')
    }
  } catch (err) {
    console.error('保存视频失败:', err)
    alert(err.message || '保存视频失败')
  }
}

// 关闭模态框
const closeModal = () => {
  showAddModal.value = false
  showEditModal.value = false
  editingVideo.value = null
  videoForm.value = {
    title: '',
    videoUrl: '',
    coverUrl: '',
    category: '',
    duration: '',
    status: '已发布',
    views: 0,
    tags: '',
    description: ''
  }
}

// 图片加载错误处理
const handleImageError = (event) => {
  event.target.style.display = 'none'
}

// 页面挂载时获取数据
onMounted(() => {
  fetchCategories()
  fetchVideos()
})

// 页面标题
useHead({
  title: '视频管理 - 91JSPG.COM 管理后台'
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>

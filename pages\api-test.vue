<template>
  <div class="bg-gray-900 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-white mb-8">API 测试页面</h1>
      
      <!-- 测试按钮 -->
      <div class="bg-gray-800 rounded-lg p-6 mb-8">
        <h2 class="text-xl font-bold text-white mb-4">API 测试</h2>
        <div class="space-y-4">
          <button 
            @click="testPlatforms"
            class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mr-4"
          >
            测试获取平台列表
          </button>
          <button 
            @click="testMangguopaiRooms"
            class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors mr-4"
          >
            测试获取芒果派房间列表
          </button>
          <button 
            @click="clearResults"
            class="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            清空结果
          </button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="bg-gray-800 rounded-lg p-6 mb-8">
        <div class="flex items-center">
          <div class="w-6 h-6 border-2 border-orange-500 border-t-transparent rounded-full animate-spin mr-3"></div>
          <span class="text-white">正在请求API...</span>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="error" class="bg-red-900/20 border border-red-500 rounded-lg p-6 mb-8">
        <h3 class="text-red-400 font-bold mb-2">错误信息</h3>
        <p class="text-red-300">{{ error }}</p>
      </div>

      <!-- API 结果 -->
      <div v-if="result" class="bg-gray-800 rounded-lg p-6">
        <h3 class="text-white font-bold mb-4">API 响应结果</h3>
        <div class="bg-gray-900 rounded p-4 overflow-auto">
          <pre class="text-green-400 text-sm">{{ JSON.stringify(result, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// API 工具
const { apiUser } = useApi()

// 响应式数据
const loading = ref(false)
const error = ref(null)
const result = ref(null)

// 测试获取平台列表
const testPlatforms = async () => {
  try {
    loading.value = true
    error.value = null
    result.value = null
    
    console.log('🚀 测试获取平台列表...')
    const response = await apiUser('/api/live/platforms')
    
    result.value = response
    console.log('✅ 平台列表获取成功:', response)
    
  } catch (err) {
    console.error('❌ 获取平台列表失败:', err)
    error.value = err.message || '获取平台列表失败'
  } finally {
    loading.value = false
  }
}

// 测试获取芒果派房间列表
const testMangguopaiRooms = async () => {
  try {
    loading.value = true
    error.value = null
    result.value = null
    
    console.log('🚀 测试获取芒果派房间列表...')
    const response = await apiUser('/api/live/mangguopai/rooms')
    
    result.value = response
    console.log('✅ 芒果派房间列表获取成功:', response)
    
  } catch (err) {
    console.error('❌ 获取芒果派房间列表失败:', err)
    error.value = err.message || '获取芒果派房间列表失败'
  } finally {
    loading.value = false
  }
}

// 清空结果
const clearResults = () => {
  result.value = null
  error.value = null
}

// 页面元数据
useHead({
  title: 'API 测试页面',
  meta: [
    { name: 'description', content: 'API 接口测试页面' }
  ]
})
</script>

<style scoped>
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>

<template>
  <div class="bg-gray-900 min-h-screen">
    <!-- 主内容区 - 70%宽度布局 -->
    <main class="w-full px-4 lg:px-8 py-6">
      <div class="w-full lg:w-[70%] mx-auto">
      <!-- 面包屑导航 -->
      <nav class="mb-6">
        <ol class="flex items-center space-x-2 text-sm">
          <li>
            <NuxtLink :to="$localePath('/')" class="text-gray-400 hover:text-orange-400 transition-colors">
              {{ $t('categories.detail.breadcrumb.home') }}
            </NuxtLink>
          </li>
          <li class="text-gray-600">/</li>
          <li>
            <NuxtLink :to="$localePath('/categories')" class="text-gray-400 hover:text-orange-400 transition-colors">
              {{ $t('categories.detail.breadcrumb.categories') }}
            </NuxtLink>
          </li>
          <li class="text-gray-600">/</li>
          <li>
            <span class="text-orange-400">{{ category?.name || $t('categories.detail.breadcrumb.loading') }}</span>
          </li>
        </ol>
      </nav>

      <!-- 加载状态 -->
      <div v-if="loading" class="animate-pulse">
        <div class="h-8 bg-gray-700 rounded mb-4 w-1/3"></div>
        <div class="h-4 bg-gray-700 rounded mb-8 w-2/3"></div>
        <div class="grid video-grid gap-5">
          <div v-for="i in 12" :key="i" class="bg-gray-800/60 rounded-xl overflow-hidden">
            <div class="aspect-video bg-gray-700"></div>
            <div class="p-4">
              <div class="h-4 bg-gray-700 rounded mb-2"></div>
              <div class="h-3 bg-gray-700 rounded w-2/3"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="text-center py-16">
        <div class="text-6xl mb-4">😞</div>
        <h2 class="text-2xl font-bold text-white mb-2">{{ $t('categories.detail.error.title') }}</h2>
        <p class="text-gray-400 mb-6">{{ error }}</p>
        <button @click="fetchCategoryVideos(1)" class="px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors">
          {{ $t('categories.detail.error.retry') }}
        </button>
      </div>

      <!-- 分类内容 -->
      <div v-else-if="category">
        <!-- 分类标题 -->
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-white mb-2">{{ category.name }}</h1>
          <p v-if="category.description" class="text-gray-400 mb-4">{{ category.description }}</p>
          <div class="flex items-center space-x-4 text-sm text-gray-400">
            <span>{{ $t('categories.detail.stats.totalVideos', { count: videos.length }) }}</span>
            <span>{{ $t('categories.detail.stats.lastUpdated') }}{{ formatDate(category.updatedAt) }}</span>
          </div>
        </div>

        <!-- 排序和筛选 -->
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center space-x-4">
            <select
              v-model="sortBy"
              class="bg-gray-800 text-white border border-gray-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-orange-500"
            >
              <option value="latest">{{ $t('categories.detail.sort.latest') }}</option>
              <option value="popular">{{ $t('categories.detail.sort.popular') }}</option>
              <option value="rating">{{ $t('categories.detail.sort.rating') }}</option>
              <option value="views">{{ $t('categories.detail.sort.views') }}</option>
            </select>
          </div>
          <div class="text-sm text-gray-400">
            <span v-if="pagination.total > 0">
              {{ $t('categories.detail.stats.showingResults', {
                start: (pagination.page - 1) * pagination.limit + 1,
                end: Math.min(pagination.page * pagination.limit, pagination.total),
                total: pagination.total
              }) }}
            </span>
            <span v-else>{{ $t('categories.detail.stats.noResults') }}</span>
          </div>
        </div>

        <!-- 视频列表 -->
        <div v-if="videos.length > 0" class="grid video-grid gap-5">
          <div 
            v-for="video in videos" 
            :key="video.id"
            class="group bg-gray-800/60 backdrop-blur-sm rounded-xl overflow-hidden hover:bg-gray-700/60 transition-all duration-500 cursor-pointer hover:scale-[1.03] hover:shadow-xl hover:shadow-orange-500/10"
          >
            <div class="aspect-video bg-gradient-to-br from-gray-700 via-gray-800 to-gray-900 flex items-center justify-center relative overflow-hidden">
              <!-- 图片加载组件 -->
              <div v-if="video.coverUrl" class="w-full h-full relative">
                <!-- 骨架屏 -->
                <div
                  v-show="imageLoadingStates[video.id] !== false"
                  class="absolute inset-0 bg-gradient-to-br from-gray-600 via-gray-700 to-gray-800 animate-pulse flex items-center justify-center"
                >
                  <div class="text-center">
                    <div class="w-12 h-12 border-4 border-gray-500 border-t-orange-500 rounded-full animate-spin mb-3"></div>
                    <div class="text-gray-400 text-xs">{{ $t('categories.detail.loading') }}</div>
                  </div>
                </div>

                <!-- 实际图片 -->
                <img
                  :src="video.coverUrl"
                  :alt="video.title"
                  class="w-full h-full object-cover transition-opacity duration-300"
                  :class="{ 'opacity-0': imageLoadingStates[video.id] !== false }"
                  @load="handleImageLoad(video.id)"
                  @error="handleImageError(video.id)"
                />
              </div>

              <!-- 无图片时的占位符 -->
              <div v-else class="w-full h-full flex items-center justify-center">
                <span class="text-white font-medium text-center p-3 text-sm leading-tight">{{ video.title }}</span>
              </div>
              
              <!-- 时长标签 -->
              <div v-if="video.duration" class="absolute bottom-2 right-2 bg-black/80 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-lg">
                {{ video.duration }}
              </div>
              <!-- 评分标签 -->
              <div v-if="video.rating" class="absolute top-2 right-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-lg font-bold">
                {{ video.rating }}
              </div>
              <!-- 播放按钮 -->
              <div class="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                <NuxtLink
                  :to="$localePath(`/play/${video.id}`)"
                  class="w-14 h-14 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center hover:scale-110 transition-all duration-300 shadow-xl shadow-orange-500/50"
                >
                  <svg class="w-6 h-6 text-white ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </NuxtLink>
              </div>
            </div>
            <div class="p-4">
              <h3 class="text-white font-medium text-sm mb-2 line-clamp-2 leading-tight">{{ video.title }}</h3>
              <div class="flex items-center justify-between text-xs">
                <span class="text-gray-400 truncate mr-2">{{ video.categoryName || $t('categories.detail.uncategorized') }}</span>
                <span v-if="video.rating" class="text-orange-400 font-medium">⭐ {{ video.rating }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页组件 -->
        <div v-if="!loading && !error && videos.length > 0 && pagination.totalPages > 1" class="flex justify-center mt-12">
          <nav class="flex items-center space-x-2">
            <!-- 上一页 -->
            <button
              @click="changePage(pagination.page - 1)"
              :disabled="pagination.page <= 1"
              class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-800 border border-gray-700 rounded-lg hover:bg-gray-700 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {{ $t('categories.pagination.previous') }}
            </button>

            <!-- 页码 -->
            <template v-for="page in getPageNumbers()" :key="page">
              <button
                v-if="page !== '...'"
                @click="changePage(page)"
                :class="[
                  'px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                  page === pagination.page
                    ? 'text-white bg-orange-500 border border-orange-500'
                    : 'text-gray-300 bg-gray-800 border border-gray-700 hover:bg-gray-700 hover:text-white'
                ]"
              >
                {{ page }}
              </button>
              <span v-else class="px-3 py-2 text-sm text-gray-500">...</span>
            </template>

            <!-- 下一页 -->
            <button
              @click="changePage(pagination.page + 1)"
              :disabled="pagination.page >= pagination.totalPages"
              class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-800 border border-gray-700 rounded-lg hover:bg-gray-700 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {{ $t('categories.pagination.next') }}
            </button>
          </nav>
        </div>

        <!-- 空状态 -->
        <div v-else-if="!loading && !error && videos.length === 0" class="text-center py-16">
          <div class="text-6xl mb-4">📹</div>
          <h2 class="text-2xl font-bold text-white mb-2">{{ $t('categories.detail.empty.title') }}</h2>
          <p class="text-gray-400">{{ $t('categories.detail.empty.description') }}</p>
        </div>
      </div>
      </div>
    </main>
  </div>
</template>

<script setup>
// 获取路由参数和多语言
const route = useRoute()
const categoryId = route.params.id
const { t, locale } = useI18n()

// 响应式数据
const category = ref(null)
const videos = ref([])
const loading = ref(true)
const error = ref(null)
const sortBy = ref('latest')
const pagination = ref({
  page: 1,
  limit: 24,
  total: 0,
  totalPages: 0
})

// 图片加载状态管理
const imageLoadingStates = ref({})

// 处理图片加载完成
const handleImageLoad = (videoId) => {
  imageLoadingStates.value[videoId] = false
}

// 处理图片加载错误
const handleImageError = (videoId) => {
  imageLoadingStates.value[videoId] = false
  console.warn(`${t('categories.detail.error.imageLoadFailed')}: 视频ID ${videoId}`)
}

// 初始化图片加载状态
const initImageLoadingStates = (videos) => {
  videos.forEach(video => {
    if (video.coverUrl) {
      imageLoadingStates.value[video.id] = true
    }
  })
}

// SEO优化
const { setCategorySEO } = useSEO()

// 监听分类数据变化，更新SEO
watch(category, (newCategory) => {
  if (newCategory) {
    setCategorySEO(newCategory.name, {
      id: newCategory.id,
      image: newCategory.coverUrl
    })
  }
}, { immediate: true })

// 获取分类和视频数据
const fetchCategoryVideos = async (page = 1) => {
  try {
    loading.value = true
    error.value = null

    // 使用新的分类视频API
    const { apiUser } = useApi()
    const response = await apiUser(`/api/categories/${categoryId}/videos`, {
      query: {
        page,
        limit: pagination.value.limit,
        sort: sortBy.value
      }
    })

    if (response.success) {
      category.value = response.data.category
      videos.value = response.data.videos
      pagination.value = response.data.pagination
      // 初始化图片加载状态
      initImageLoadingStates(response.data.videos)
    }
    
    // 更新页面标题
    if (category.value) {
      useHead({
        title: computed(() => `${category.value.name} ${t('categories.detail.meta.titleSuffix')}`),
        meta: [
          {
            name: 'description',
            content: computed(() => category.value.description || t('categories.detail.meta.descriptionTemplate', { name: category.value.name }))
          },
          {
            name: 'keywords',
            content: computed(() => t('categories.detail.meta.keywordsTemplate', { name: category.value.name }))
          }
        ]
      })
    }
    
  } catch (err) {
    console.error('获取分类视频失败:', err)
    error.value = t('categories.detail.error.loadFailed')
  } finally {
    loading.value = false
  }
}

// 格式化观看次数
const formatViews = (views) => {
  if (views >= 1000000) {
    return (views / 1000000).toFixed(1) + 'M'
  } else if (views >= 1000) {
    return (views / 1000).toFixed(1) + 'K'
  }
  return views?.toString() || '0'
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString(locale.value, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 页面加载时获取数据
onMounted(() => {
  fetchCategoryVideos()
})

// 切换页面
const changePage = (page) => {
  if (page >= 1 && page <= pagination.value.totalPages) {
    fetchCategoryVideos(page)
  }
}

// 生成页码数组
const getPageNumbers = () => {
  const current = pagination.value.page
  const total = pagination.value.totalPages
  const pages = []

  if (total <= 7) {
    // 如果总页数小于等于7，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // 总是显示第一页
    pages.push(1)

    if (current <= 4) {
      // 当前页在前面
      for (let i = 2; i <= 5; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    } else if (current >= total - 3) {
      // 当前页在后面
      pages.push('...')
      for (let i = total - 4; i <= total; i++) {
        pages.push(i)
      }
    } else {
      // 当前页在中间
      pages.push('...')
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    }
  }

  return pages
}

// 监听排序变化
watch(sortBy, () => {
  pagination.value.page = 1 // 重置到第一页
  fetchCategoryVideos(1)
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>

<template>
  <div class="bg-gray-900 min-h-screen">
    <!-- 主内容区 - 70%宽度布局 -->
    <main class="w-full px-4 lg:px-8 py-6">
      <div class="w-full lg:w-[70%] mx-auto">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-white mb-2">{{ $t('categories.title') }}</h1>
        <p class="text-gray-400">{{ $t('categories.subtitle') }}</p>
        <div v-if="!loading && pagination.total > 0" class="text-sm text-gray-500 mt-2">
          {{ $t('categories.totalCount', { count: pagination.total }) }}
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="grid grid-cols-2 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4 md:gap-6">
        <div v-for="i in 8" :key="i" class="bg-gray-800/60 rounded-xl overflow-hidden animate-pulse">
          <div class="p-4 md:p-6">
            <div class="w-12 h-12 md:w-16 md:h-16 bg-gray-700 rounded-full mb-3 md:mb-4 mx-auto"></div>
            <div class="h-6 bg-gray-700 rounded mb-3"></div>
            <div class="h-4 bg-gray-700 rounded mb-2"></div>
            <div class="h-4 bg-gray-700 rounded w-2/3 mb-4"></div>
            <div class="flex justify-between">
              <div class="h-4 bg-gray-700 rounded w-1/3"></div>
              <div class="h-4 bg-gray-700 rounded w-1/4"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="text-center py-16">
        <div class="text-6xl mb-4">😞</div>
        <h2 class="text-2xl font-bold text-white mb-2">{{ $t('categories.error.title') }}</h2>
        <p class="text-gray-400 mb-6">{{ error }}</p>
        <button @click="fetchCategories(1)" class="px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors">
          {{ $t('categories.error.retry') }}
        </button>
      </div>

      <!-- 分类列表 -->
      <div v-else class="grid grid-cols-2 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4 md:gap-6">
        <div 
          v-for="category in categories" 
          :key="category.id"
          class="group bg-gray-800/60 backdrop-blur-sm rounded-xl overflow-hidden hover:bg-gray-700/60 transition-all duration-500 cursor-pointer hover:scale-[1.02] hover:shadow-xl hover:shadow-orange-500/10"
        >
          <NuxtLink :to="$localePath(`/categories/${category.id}`)" class="block h-full">
            <!-- 分类卡片内容 -->
            <div class="p-4 md:p-6 h-full flex flex-col justify-between">
              <!-- 分类图标和标题 -->
              <div class="text-center mb-3 md:mb-4">
                <div class="w-12 h-12 md:w-16 md:h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mb-3 md:mb-4 mx-auto group-hover:scale-110 transition-transform">
                  <svg class="w-6 h-6 md:w-8 md:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                  </svg>
                </div>
                <h3 class="text-white font-bold text-lg md:text-xl mb-2">{{ category.name }}</h3>
                <p v-if="category.description" class="text-gray-400 text-sm mb-4 line-clamp-2">
                  {{ category.description }}
                </p>
              </div>

              <!-- 分类统计信息 -->
              <div class="flex items-center justify-between">
                <span class="text-orange-400 font-medium text-sm">
                  {{ $t('categories.videoCount', { count: category.videoCount || 0 }) }}
                </span>
                <span class="text-gray-500 text-xs">
                  {{ formatDate(category.updatedAt) }}
                </span>
              </div>
            </div>
          </NuxtLink>
        </div>
      </div>

      <!-- 分页组件 -->
      <div v-if="!loading && !error && categories.length > 0 && pagination.totalPages > 1" class="flex justify-center mt-12">
        <nav class="flex items-center space-x-2">
          <!-- 上一页 -->
          <button
            @click="changePage(pagination.page - 1)"
            :disabled="pagination.page <= 1"
            class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-800 border border-gray-700 rounded-lg hover:bg-gray-700 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {{ $t('categories.pagination.previous') }}
          </button>

          <!-- 页码 -->
          <template v-for="page in getPageNumbers()" :key="page">
            <button
              v-if="page !== '...'"
              @click="changePage(page)"
              :class="[
                'px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                page === pagination.page
                  ? 'text-white bg-orange-500 border border-orange-500'
                  : 'text-gray-300 bg-gray-800 border border-gray-700 hover:bg-gray-700 hover:text-white'
              ]"
            >
              {{ page }}
            </button>
            <span v-else class="px-3 py-2 text-sm text-gray-500">...</span>
          </template>

          <!-- 下一页 -->
          <button
            @click="changePage(pagination.page + 1)"
            :disabled="pagination.page >= pagination.totalPages"
            class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-800 border border-gray-700 rounded-lg hover:bg-gray-700 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {{ $t('categories.pagination.next') }}
          </button>
        </nav>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && !error && categories.length === 0" class="text-center py-16">
        <div class="text-6xl mb-4">📂</div>
        <h2 class="text-2xl font-bold text-white mb-2">{{ $t('categories.empty.title') }}</h2>
        <p class="text-gray-400">{{ $t('categories.empty.description') }}</p>
      </div>
      </div>
    </main>
  </div>
</template>

<script setup>
// 页面元数据
const { t } = useI18n()
useHead({
  title: computed(() => t('categories.meta.title')),
  meta: [
    { name: 'description', content: computed(() => t('categories.meta.description')) },
    { name: 'keywords', content: computed(() => t('categories.meta.keywords')) }
  ]
})

// 响应式数据
const categories = ref([])
const loading = ref(true)
const error = ref(null)
const pagination = ref({
  page: 1,
  limit: 12,
  total: 0,
  totalPages: 0
})

// 获取分类列表
const fetchCategories = async (page = 1) => {
  try {
    loading.value = true
    error.value = null

    const { apiUser } = useApi()
    const response = await apiUser('/api/categories', {
      query: {
        page,
        limit: pagination.value.limit
      }
    })

    if (response.success) {
      if (response.data.categories) {
        // 分页格式
        categories.value = response.data.categories
        pagination.value = response.data.pagination
      } else {
        // 简单列表格式（兼容旧版本）
        categories.value = response.data
      }
    } else {
      error.value = t('categories.error.loadFailed')
    }
  } catch (err) {
    console.error('获取分类列表失败:', err)
    error.value = t('categories.error.loadFailed')
  } finally {
    loading.value = false
  }
}

// 切换页面
const changePage = (page) => {
  if (page >= 1 && page <= pagination.value.totalPages) {
    fetchCategories(page)
  }
}

// 生成页码数组
const getPageNumbers = () => {
  const current = pagination.value.page
  const total = pagination.value.totalPages
  const pages = []

  if (total <= 7) {
    // 如果总页数小于等于7，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // 总是显示第一页
    pages.push(1)

    if (current <= 4) {
      // 当前页在前面
      for (let i = 2; i <= 5; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    } else if (current >= total - 3) {
      // 当前页在后面
      pages.push('...')
      for (let i = total - 4; i <= total; i++) {
        pages.push(i)
      }
    } else {
      // 当前页在中间
      pages.push('...')
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    }
  }

  return pages
}

// 格式化日期
const { locale } = useI18n()
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString(locale.value, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 页面加载时获取数据
onMounted(() => {
  fetchCategories()
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>

<template>
  <div class="bg-gray-900 min-h-screen">
    <!-- 服务关闭通知横幅 -->
    <div v-if="isServiceClosed" class="bg-red-600 text-white py-3">
      <div class="w-full lg:w-[70%] mx-auto px-4 lg:px-8">
        <div class="flex items-center justify-center space-x-3">
          <span class="text-xl">🔒</span>
          <span class="font-medium">采集服务暂时关闭维护中</span>
          <span class="text-red-200">|</span>
          <span class="text-sm text-red-200">API接口暂时不可用，请稍后再试</span>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <main class="w-full px-4 lg:px-8 py-6">
      <div class="w-full lg:w-[70%] mx-auto">

        <!-- 服务关闭时显示的内容 -->
        <div v-if="isServiceClosed">
          <!-- 页面标题区域 - 关闭状态 -->
          <section class="mb-8">
            <div class="relative bg-gradient-to-r from-gray-600 to-gray-800 rounded-2xl overflow-hidden shadow-2xl">
              <div class="aspect-[3/1] flex items-center justify-center relative">
                <!-- 背景图案 -->
                <div class="absolute inset-0 bg-gradient-to-br from-gray-500/20 to-gray-700/20"></div>
                <div class="absolute top-4 right-4 w-20 h-20 bg-white/5 rounded-full blur-xl"></div>
                <div class="absolute bottom-4 left-4 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>

                <!-- 标题内容 -->
                <div class="relative z-10 text-center text-white">
                  <h1 class="text-3xl md:text-4xl font-bold mb-2 flex items-center justify-center">
                    <span class="text-gray-400 mr-3">🔒</span>
                    采集服务暂时关闭
                  </h1>
                  <p class="text-lg md:text-xl opacity-90">采集功能暂时关闭维护中，请稍后再试</p>

                  <!-- 维护状态信息 -->
                  <div class="mt-4 inline-flex items-center space-x-4 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                    <div class="flex items-center space-x-2">
                      <div class="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
                      <span class="text-sm">服务暂时关闭</span>
                    </div>
                    <div class="w-px h-4 bg-white/30"></div>
                    <div class="flex items-center space-x-2">
                      <div class="w-2 h-2 bg-yellow-400 rounded-full"></div>
                      <span class="text-sm">维护中</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- 维护通知 -->
          <section class="mb-12">
            <div class="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50 text-center">
              <div class="mb-6">
                <div class="w-20 h-20 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span class="text-3xl">🔧</span>
                </div>
                <h2 class="text-2xl font-bold text-white mb-2">
                  采集功能暂时关闭
                </h2>
                <p class="text-gray-400">
                  我们正在对采集系统进行维护升级，暂时关闭对外采集服务。<br>
                  如有紧急需求，请联系管理员。
                </p>
              </div>

              <div class="bg-gray-700/30 rounded-xl p-6">
                <h3 class="text-lg font-semibold text-white mb-3">维护期间说明</h3>
                <div class="grid md:grid-cols-2 gap-4 text-sm text-gray-300">
                  <div class="flex items-center space-x-2">
                    <span class="text-red-400">●</span>
                    <span>采集接口暂时不可用</span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="text-red-400">●</span>
                    <span>API文档暂时关闭</span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="text-yellow-400">●</span>
                    <span>预计维护时间：待定</span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="text-green-400">●</span>
                    <span>其他功能正常使用</span>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- 联系信息 -->
          <section class="mb-8">
            <div class="bg-gray-800/30 rounded-xl p-6 text-center">
              <h3 class="text-lg font-semibold text-white mb-3">需要帮助？</h3>
              <p class="text-gray-400 text-sm mb-4">
                如果您有紧急的采集需求或其他问题，请通过以下方式联系我们：
              </p>
              <div class="flex justify-center space-x-4">
                <button class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                  联系管理员
                </button>
                <button class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm transition-colors" @click="$router.push('/')">
                  返回首页
                </button>
              </div>
            </div>
          </section>
        </div>

        <!-- 正常服务时显示的内容 -->
        <div v-else>
          <!-- 页面标题区域 - 正常状态 -->
          <section class="mb-8">
            <div class="relative bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl overflow-hidden shadow-2xl">
              <div class="aspect-[3/1] flex items-center justify-center relative">
                <!-- 背景图案 -->
                <div class="absolute inset-0 bg-gradient-to-br from-orange-400/20 to-red-600/20"></div>
                <div class="absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
                <div class="absolute bottom-4 left-4 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>

                <!-- 标题内容 -->
                <div class="relative z-10 text-center text-white">
                  <h1 class="text-3xl md:text-4xl font-bold mb-2 flex items-center justify-center">
                    <span class="text-yellow-300 mr-3">🔗</span>
                    {{ $t('collect.title') }}
                  </h1>
                  <p class="text-lg md:text-xl opacity-90">{{ $t('collect.subtitle') }}</p>

                <!-- 统计信息 -->
                <div v-if="!loading && stats.totalVideos > 0" class="mt-4 inline-flex items-center space-x-4 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2">
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span class="text-sm">{{ $t('collect.stats.videoResources', { count: stats.totalVideos + '+' }) }}</span>
                  </div>
                  <div class="w-px h-4 bg-white/30"></div>
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                    <span class="text-sm">{{ $t('collect.stats.categories', { count: stats.totalCategories }) }}</span>
                  </div>
                </div>
              </div>

              <!-- 装饰元素 -->
              <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
            </div>
          </div>
        </section>

        <!-- 功能特色 -->
        <section class="mb-12">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h2 class="text-2xl font-bold text-white mb-2 flex items-center">
                <span class="text-orange-500 mr-3">⚡</span>
                {{ $t('collect.coreFeatures.apiInfo.title') }}
              </h2>
              <p class="text-gray-400 text-sm">{{ $t('collect.subtitle') }}</p>
            </div>
          </div>

          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="bg-gray-800/60 rounded-xl p-4 text-center">
              <div class="text-green-400 text-sm font-medium">{{ $t('collect.banner.features.compatible') }}</div>
              <div class="text-xs text-gray-400 mt-1">Apple CMS</div>
            </div>
            <div class="bg-gray-800/60 rounded-xl p-4 text-center">
              <div class="text-blue-400 text-sm font-medium">{{ $t('collect.banner.features.unlimited') }}</div>
              <div class="text-xs text-gray-400 mt-1">{{ $t('collect.coreFeatures.apiInfo.unlimited') }}</div>
            </div>
            <div class="bg-gray-800/60 rounded-xl p-4 text-center">
              <div class="text-purple-400 text-sm font-medium">{{ $t('collect.banner.features.service') }}</div>
              <div class="text-xs text-gray-400 mt-1">{{ $t('collect.serviceGuarantee.service247.description') }}</div>
            </div>
            <div class="bg-gray-800/60 rounded-xl p-4 text-center">
              <div class="text-yellow-400 text-sm font-medium">{{ $t('collect.banner.features.free') }}</div>
              <div class="text-xs text-gray-400 mt-1">{{ $t('collect.serviceGuarantee.completelyFree.description') }}</div>
            </div>
          </div>
        </section>

        <!-- 公告信息 -->
        <section class="mb-12">
          <div class="bg-gray-800/60 rounded-xl p-6 border border-gray-700">
            <div class="flex items-start space-x-4">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"></path>
                  </svg>
                </div>
              </div>
              <div class="flex-1">
                <div class="flex items-center space-x-3 mb-3">
                  <h3 class="text-lg font-bold text-white">{{ $t('collect.announcement.title') }}</h3>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-400">
                    <span class="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse"></span>
                    {{ $t('collect.announcement.status') }}
                  </span>
                </div>
                <div class="space-y-3">
                  <div class="bg-gray-700/50 rounded-lg p-3">
                    <div class="flex items-center space-x-2 mb-2">
                      <span class="text-xs bg-orange-500/20 text-orange-400 px-2 py-1 rounded">{{ $t('collect.announcement.launch.date') }}</span>
                      <span class="text-orange-400 font-medium text-sm">{{ $t('collect.announcement.launch.title') }}</span>
                    </div>
                    <p class="text-gray-300 text-sm">{{ $t('collect.announcement.launch.content') }}</p>
                  </div>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div class="bg-gray-700/50 rounded-lg p-3">
                      <div class="text-blue-400 font-medium mb-1 text-sm">{{ $t('collect.announcement.features.title') }}</div>
                      <p class="text-gray-300 text-xs">{{ $t('collect.announcement.features.content') }}</p>
                    </div>
                    <div class="bg-gray-700/50 rounded-lg p-3">
                      <div class="text-purple-400 font-medium mb-1 text-sm">{{ $t('collect.announcement.support.title') }}</div>
                      <p class="text-gray-300 text-xs">{{ $t('collect.announcement.support.content') }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- 核心功能展示 -->
        <section class="mb-12">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h2 class="text-2xl font-bold text-white mb-2 flex items-center">
                <span class="text-orange-500 mr-3">🔧</span>
                {{ $t('collect.coreFeatures.title') }}
              </h2>
              <p class="text-gray-400 text-sm">{{ $t('collect.coreFeatures.subtitle') }}</p>
            </div>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 接口信息 -->
            <div class="bg-gray-800/60 rounded-xl p-6">
              <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                  </svg>
                </div>
                <h3 class="text-lg font-bold text-white ml-3">{{ $t('collect.coreFeatures.apiInfo.title') }}</h3>
              </div>
              <div class="space-y-3">
                <div class="bg-gray-700/50 rounded-lg p-3">
                  <div class="flex justify-between items-center">
                    <span class="text-gray-300 text-sm">{{ $t('collect.coreFeatures.apiInfo.version') }}</span>
                    <span class="text-orange-400 font-medium text-sm">v1.0</span>
                  </div>
                </div>
                <div class="bg-gray-700/50 rounded-lg p-3">
                  <div class="flex justify-between items-center">
                    <span class="text-gray-300 text-sm">{{ $t('collect.coreFeatures.apiInfo.compatibility') }}</span>
                    <span class="text-green-400 font-medium text-sm">Apple CMS v10</span>
                  </div>
                </div>
                <div class="bg-gray-700/50 rounded-lg p-3">
                  <div class="flex justify-between items-center">
                    <span class="text-gray-300 text-sm">{{ $t('collect.coreFeatures.apiInfo.outputFormat') }}</span>
                    <div class="flex space-x-2">
                      <span class="text-blue-400 text-xs bg-blue-500/20 px-2 py-1 rounded">JSON</span>
                      <span class="text-purple-400 text-xs bg-purple-500/20 px-2 py-1 rounded">XML</span>
                    </div>
                  </div>
                </div>
                <div class="bg-gray-700/50 rounded-lg p-3">
                  <div class="flex justify-between items-center">
                    <span class="text-gray-300 text-sm">{{ $t('collect.coreFeatures.apiInfo.rateLimit') }}</span>
                    <span class="text-green-400 font-medium text-sm">{{ $t('collect.coreFeatures.apiInfo.unlimited') }}</span>
                  </div>
                </div>
                <div class="grid grid-cols-2 gap-3">
                  <div class="bg-gray-700/50 rounded-lg p-3 text-center">
                    <div class="text-lg font-bold text-purple-400">{{ stats.totalVideos }}+</div>
                    <div class="text-xs text-gray-400">{{ $t('collect.coreFeatures.apiInfo.totalVideos') }}</div>
                  </div>
                  <div class="bg-gray-700/50 rounded-lg p-3 text-center">
                    <div class="text-lg font-bold text-orange-400">{{ stats.totalCategories }}</div>
                    <div class="text-xs text-gray-400">{{ $t('collect.coreFeatures.apiInfo.totalCategories') }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 快速开始 -->
            <div class="bg-gray-800/60 rounded-xl p-6">
              <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl flex items-center justify-center">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                </div>
                <h3 class="text-lg font-bold text-white ml-3">{{ $t('collect.coreFeatures.quickStart.title') }}</h3>
              </div>
              <div class="space-y-4">
                <div class="bg-gray-700/50 rounded-lg p-3">
                  <div class="flex items-center mb-2">
                    <span class="w-6 h-6 bg-orange-500 text-white text-xs font-bold rounded-lg flex items-center justify-center mr-2">1</span>
                    <h4 class="font-medium text-white text-sm">{{ $t('collect.coreFeatures.quickStart.step1') }}</h4>
                  </div>
                  <div class="bg-gray-900/50 rounded p-2">
                    <code class="text-green-400 text-xs break-all">{{ baseUrl }}/api/collect/vod?ac=list</code>
                  </div>
                </div>
                <div class="bg-gray-700/50 rounded-lg p-3">
                  <div class="flex items-center mb-2">
                    <span class="w-6 h-6 bg-green-500 text-white text-xs font-bold rounded-lg flex items-center justify-center mr-2">2</span>
                    <h4 class="font-medium text-white text-sm">{{ $t('collect.coreFeatures.quickStart.step2') }}</h4>
                  </div>
                  <div class="bg-gray-900/50 rounded p-2">
                    <code class="text-green-400 text-xs break-all">{{ baseUrl }}/api/collect/vod?ac=detail&ids=1,2,3</code>
                  </div>
                </div>
                <div class="bg-gray-700/50 rounded-lg p-3">
                  <div class="flex items-center mb-2">
                    <span class="w-6 h-6 bg-purple-500 text-white text-xs font-bold rounded-lg flex items-center justify-center mr-2">3</span>
                    <h4 class="font-medium text-white text-sm">{{ $t('collect.coreFeatures.quickStart.step3') }}</h4>
                  </div>
                  <div class="bg-gray-900/50 rounded p-2">
                    <code class="text-green-400 text-xs break-all">{{ baseUrl }}/api/collect/vod?ac=list&wd=keyword</code>
                  </div>
                </div>
              </div>
            </div>

            <!-- 实时统计 -->
            <div class="bg-gray-800/60 rounded-xl p-6">
              <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                  </svg>
                </div>
                <h3 class="text-lg font-bold text-white ml-3">{{ $t('collect.coreFeatures.realTimeStats.title') }}</h3>
              </div>
              <div class="grid grid-cols-2 gap-3">
                <div class="bg-orange-500/20 rounded-lg p-4 text-center">
                  <div class="text-2xl font-bold text-orange-400 mb-1">{{ stats.totalVideos }}</div>
                  <div class="text-xs text-gray-300">{{ $t('collect.coreFeatures.realTimeStats.totalVideosLabel') }}</div>
                </div>
                <div class="bg-green-500/20 rounded-lg p-4 text-center">
                  <div class="text-2xl font-bold text-green-400 mb-1">{{ stats.totalCategories }}</div>
                  <div class="text-xs text-gray-300">{{ $t('collect.coreFeatures.realTimeStats.categoriesLabel') }}</div>
                </div>
                <div class="bg-purple-500/20 rounded-lg p-4 text-center">
                  <div class="text-2xl font-bold text-purple-400 mb-1">24/7</div>
                  <div class="text-xs text-gray-300">{{ $t('collect.coreFeatures.realTimeStats.onlineService') }}</div>
                </div>
                <div class="bg-yellow-500/20 rounded-lg p-4 text-center">
                  <div class="text-2xl font-bold text-yellow-400 mb-1">FREE</div>
                  <div class="text-xs text-gray-300">{{ $t('collect.coreFeatures.realTimeStats.freeService') }}</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- 接口示例演示 -->
        <section class="mb-12">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h2 class="text-2xl font-bold text-white mb-2 flex items-center">
                <span class="text-blue-500 mr-3">💻</span>
                {{ $t('collect.demo.title') }}
              </h2>
              <p class="text-gray-400 text-sm">{{ $t('collect.demo.subtitle') }}</p>
            </div>
            <div class="flex items-center space-x-2 bg-green-500/20 rounded-full px-3 py-1">
              <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span class="text-green-400 text-sm font-medium">{{ $t('collect.demo.realTimeData') }}</span>
            </div>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- JSON示例 -->
            <div class="bg-gray-800/60 rounded-xl p-6">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold">{ }</span>
                  </div>
                  <h3 class="text-lg font-bold text-white">{{ $t('collect.demo.json.title') }}</h3>
                </div>
                <button
                  @click="loadJsonExample"
                  class="bg-orange-500 text-white px-3 py-1 rounded-lg text-sm hover:bg-orange-600 transition-colors"
                >
                  {{ $t('collect.demo.json.refresh') }}
                </button>
              </div>
              <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                <pre class="text-green-400 text-sm font-mono"><code>{{ jsonExample }}</code></pre>
              </div>
              <div class="mt-3 p-2 bg-gray-700/50 rounded">
                <div class="text-xs text-gray-300">
                  <span class="text-orange-400 font-medium">{{ $t('collect.demo.json.requestUrl') }}:</span>
                  <code class="text-green-400">{{ baseUrl }}/api/collect/vod?ac=list&pg=1</code>
                </div>
              </div>
            </div>

            <!-- XML示例 -->
            <div class="bg-gray-800/60 rounded-xl p-6">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold">&lt;/&gt;</span>
                  </div>
                  <h3 class="text-lg font-bold text-white">{{ $t('collect.demo.xml.title') }}</h3>
                </div>
                <button
                  @click="loadXmlExample"
                  class="bg-purple-500 text-white px-3 py-1 rounded-lg text-sm hover:bg-purple-600 transition-colors"
                >
                  {{ $t('collect.demo.xml.refresh') }}
                </button>
              </div>
              <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                <pre class="text-yellow-400 text-sm font-mono"><code>{{ xmlExample }}</code></pre>
              </div>
              <div class="mt-3 p-2 bg-gray-700/50 rounded">
                <div class="text-xs text-gray-300">
                  <span class="text-orange-400 font-medium">{{ $t('collect.demo.xml.requestUrl') }}:</span>
                  <code class="text-yellow-400">{{ baseUrl }}/api/collect/vod?ac=list&at=xml&pg=1</code>
                </div>
              </div>
            </div>
          </div>

          <!-- 在线测试工具 -->
          <div class="bg-gray-800/60 rounded-xl p-6">
            <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
              <svg class="w-5 h-5 text-orange-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              {{ $t('collect.onlineTestTool.title') }}
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
              <a
                :href="`${baseUrl}/api/collect/vod?ac=list&pg=1`"
                target="_blank"
                class="bg-orange-500 text-white px-3 py-2 rounded-lg text-center text-sm hover:bg-orange-600 transition-colors"
              >
                {{ $t('collect.onlineTestTool.testListApi') }}
              </a>
              <a
                :href="`${baseUrl}/api/collect/vod?ac=detail&ids=1,2,3`"
                target="_blank"
                class="bg-green-500 text-white px-3 py-2 rounded-lg text-center text-sm hover:bg-green-600 transition-colors"
              >
                {{ $t('collect.onlineTestTool.testDetailApi') }}
              </a>
              <a
                :href="`${baseUrl}/api/collect/vod?ac=list&wd=action`"
                target="_blank"
                class="bg-purple-500 text-white px-3 py-2 rounded-lg text-center text-sm hover:bg-purple-600 transition-colors"
              >
                {{ $t('collect.onlineTestTool.testSearchApi') }}
              </a>
              <a
                :href="`${baseUrl}/api/collect/vod?ac=list&at=xml&pg=1`"
                target="_blank"
                class="bg-blue-500 text-white px-3 py-2 rounded-lg text-center text-sm hover:bg-blue-600 transition-colors"
              >
                {{ $t('collect.onlineTestTool.testXmlFormat') }}
              </a>
            </div>
          </div>
        </section>

        <!-- 接口文档 -->
        <section class="mb-12">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h2 class="text-2xl font-bold text-white mb-2 flex items-center">
                <span class="text-orange-500 mr-3">📚</span>
                {{ $t('collect.documentation.title') }}
              </h2>
              <p class="text-gray-400 text-sm">{{ $t('collect.documentation.subtitle') }}</p>
            </div>
          </div>

          <div class="bg-gray-800/60 rounded-xl p-6">
            <h3 class="text-lg font-semibold text-white mb-4">{{ $t('collect.documentation.apiTitle') }}</h3>

            <!-- 接口地址 -->
            <div class="mb-6">
              <h4 class="font-medium text-white mb-3">{{ $t('collect.documentation.apiAddress') }}</h4>
              <div class="bg-gray-900 rounded-lg p-3">
                <code class="text-orange-400 font-mono">{{ baseUrl }}/api/collect/vod</code>
                <button
                  @click="copyToClipboard(`${baseUrl}/api/collect/vod`)"
                  class="ml-3 text-gray-400 hover:text-orange-400 transition-colors"
                  :title="$t('collect.actions.copyAddress')"
                >
                  <svg class="w-4 h-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                  </svg>
                </button>
              </div>
            </div>

            <!-- 主要参数 -->
            <div class="mb-6">
              <h4 class="font-medium text-white mb-3">{{ $t('collect.documentation.requestParams') }}</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-gray-700/50 rounded-lg p-3">
                  <div class="flex justify-between items-center">
                    <span class="text-orange-400 font-medium">ac</span>
                    <span class="text-gray-300 text-sm">{{ $t('collect.apiParams.descriptions.ac') }}</span>
                  </div>
                </div>
                <div class="bg-gray-700/50 rounded-lg p-3">
                  <div class="flex justify-between items-center">
                    <span class="text-orange-400 font-medium">pg</span>
                    <span class="text-gray-300 text-sm">{{ $t('collect.apiParams.descriptions.pg') }}</span>
                  </div>
                </div>
                <div class="bg-gray-700/50 rounded-lg p-3">
                  <div class="flex justify-between items-center">
                    <span class="text-orange-400 font-medium">wd</span>
                    <span class="text-gray-300 text-sm">{{ $t('collect.apiParams.descriptions.wd') }}</span>
                  </div>
                </div>
                <div class="bg-gray-700/50 rounded-lg p-3">
                  <div class="flex justify-between items-center">
                    <span class="text-orange-400 font-medium">t</span>
                    <span class="text-gray-300 text-sm">{{ $t('collect.apiParams.descriptions.t') }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 示例请求 -->
            <div class="mb-6">
              <h4 class="font-medium text-white mb-3">{{ $t('collect.examples.title') }}</h4>
              <div class="space-y-3">
                <div class="bg-gray-900 rounded-lg p-3">
                  <div class="text-sm text-gray-300 mb-1">{{ $t('collect.examples.getVideoList') }}</div>
                  <code class="text-green-400 text-sm break-all">{{ baseUrl }}/api/collect/vod?ac=list&pg=1</code>
                </div>
                <div class="bg-gray-900 rounded-lg p-3">
                  <div class="text-sm text-gray-300 mb-1">{{ $t('collect.examples.getVideoDetails') }}</div>
                  <code class="text-green-400 text-sm break-all">{{ baseUrl }}/api/collect/vod?ac=detail&ids=1,2,3</code>
                </div>
                <div class="bg-gray-900 rounded-lg p-3">
                  <div class="text-sm text-gray-300 mb-1">{{ $t('collect.examples.searchVideos') }}</div>
                  <code class="text-green-400 text-sm break-all">{{ baseUrl }}/api/collect/vod?ac=list&wd=action</code>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- 分类列表 -->
        <section class="mb-12">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h2 class="text-2xl font-bold text-white mb-2 flex items-center">
                <span class="text-orange-500 mr-3">🎬</span>
                {{ $t('collect.categories.title') }}
              </h2>
              <p class="text-gray-400 text-sm">{{ $t('collect.categories.subtitle') }}</p>
            </div>
            <span class="bg-orange-500/20 text-orange-400 text-xs font-medium px-3 py-1 rounded-full">
              {{ $t('collect.categories.categoryCount', { count: categories.length }) }}
            </span>
          </div>

          <!-- 分类列表 -->
          <div v-if="categories.length > 0" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
            <div
              v-for="category in categories"
              :key="category.id"
              class="bg-gray-800/60 rounded-xl p-4 text-center hover:bg-gray-700/60 transition-colors"
            >
              <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center mb-3 mx-auto">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
              </div>
              <div class="text-white font-medium mb-1 text-sm">{{ category.name }}</div>
              <div class="text-xs text-orange-400 mb-2">ID: {{ category.id }}</div>
              <div class="text-xs text-gray-400 mb-3">{{ $t('collect.categories.videoCount', { count: category.videoCount || 0 }) }}</div>
              <a
                :href="`${baseUrl}/api/collect/vod?ac=list&t=${category.id}&pg=1`"
                target="_blank"
                class="inline-block text-xs bg-orange-500 text-white px-2 py-1 rounded hover:bg-orange-600 transition-colors"
              >
                {{ $t('collect.documentation.testCollection') }}
              </a>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else-if="!loading" class="text-center py-12">
            <div class="w-16 h-16 bg-gray-700/50 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
            </div>
            <p class="text-gray-400 text-sm">{{ $t('collect.loading.loadFailed') }}</p>
            <p class="text-gray-500 text-xs mt-2">{{ $t('collect.categories.subtitle') }}</p>
          </div>

          <!-- 加载状态 -->
          <div v-else class="text-center py-12">
            <div class="w-16 h-16 bg-gray-700/50 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
              <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
            </div>
            <p class="text-gray-400 text-sm">{{ $t('collect.loading.loading') }}</p>
          </div>
        </section>
        </div>
        <!-- 正常服务内容结束 -->

      </div>
    </main>
  </div>
</template>

<script setup>
// 导入网站配置
import { siteConfig } from '~/config/site.js'

// 采集服务控制开关 - 设置为 true 表示服务关闭，false 表示服务正常
const isServiceClosed = ref(true)

// 动态SEO配置
const pageTitle = computed(() => {
  return isServiceClosed.value
    ? `采集服务暂时关闭 | ${siteConfig.basic.siteName}`
    : `API采集接口 | ${siteConfig.basic.siteName}`
})

const pageDescription = computed(() => {
  return isServiceClosed.value
    ? '采集功能暂时关闭维护中，请稍后再试'
    : '提供视频数据采集API接口，支持JSON和XML格式，方便第三方应用集成'
})

// 基本SEO配置
useHead({
  title: pageTitle,
  meta: [
    { name: 'description', content: pageDescription },
    { name: 'keywords', content: 'API,采集接口,视频数据,JSON,XML,数据接口' },
    { name: 'author', content: siteConfig.basic.siteName },
    { name: 'robots', content: isServiceClosed.value ? 'noindex, nofollow' : 'index, follow' }
  ]
})

// Reactive data
const baseUrl = ref('')
const loading = ref(true)
const stats = ref({
  totalVideos: 0,
  totalCategories: 0
})
const categories = ref([])
const jsonExample = ref('')
const xmlExample = ref('')



// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    console.log('复制成功')
  } catch (err) {
    console.error('复制失败', err)
  }
}

// 加载JSON示例数据
const loadJsonExample = async () => {
  try {
    jsonExample.value = '加载中...'
    console.log('开始加载JSON示例数据...')
    const response = await $fetch('/api/collect/vod?ac=list&pg=1')
    console.log('JSON示例响应:', response)
    jsonExample.value = JSON.stringify(response, null, 2)
  } catch (error) {
    console.error('加载失败', error)
    jsonExample.value = `// 加载失败
{
  "code": 1,
  "msg": "Data List",
  "page": 1,
  "pagecount": 10,
  "limit": "20",
  "total": 150,
  "list": [
    {
      "vod_id": 1,
      "vod_name": "Sample Video Title",
      "type_id": 1,
      "type_name": "Action",
      "vod_en": "example-video",
      "vod_time": "2025-07-24 19:00:00",
      "vod_remarks": "HD",
      "vod_play_from": "default"
    }
  ],
  "class": [
    {
      "type_id": 1,
      "type_name": "Action"
    }
  ]
}`
  }
}

// 加载XML示例数据
const loadXmlExample = async () => {
  try {
    xmlExample.value = '加载中...'
    console.log('开始加载XML示例数据...')
    const response = await fetch('/api/collect/vod?ac=list&at=xml&pg=1')
    console.log('XML响应状态:', response.status)
    const xmlText = await response.text()
    console.log('XML示例响应:', xmlText.substring(0, 200) + '...')
    xmlExample.value = xmlText
  } catch (error) {
    console.error('加载失败', error)
    xmlExample.value = `<?xml version="1.0" encoding="utf-8"?>
<rss version="2.0">
<channel>
<title>91JSPG.COM</title>
<link>https://91jspg.com</link>
<description>API采集接口</description>
<lastBuildDate>2025-07-24 19:00:00</lastBuildDate>
<generator>91JSPG.COM</generator>
<item>
<title><![CDATA[Sample Video Title]]></title>
<link>https://91jspg.com/play/1</link>
<description><![CDATA[This is a sample video description]]></description>
<pubDate>2025-07-24 19:00:00</pubDate>
<category>Action</category>
</item>
</channel>
</rss>`
  }
}

// Fetch data
onMounted(async () => {
  // Set base URL - 使用真实的API地址供用户采集使用
  const { $config } = useNuxtApp()
  baseUrl.value = $config.public.realApiUrl.replace('/api', '') // 移除/api后缀，因为模板中会添加

  // 初始化示例数据
  jsonExample.value = '正在加载示例数据...'
  xmlExample.value = '正在加载示例数据...'

  try {
    // 获取统计信息
    console.log('开始获取API数据...')
    const videoStatsResponse = await $fetch('/api/videos/stats')
    const categoryResponse = await $fetch('/api/categories')

    console.log('视频统计响应:', videoStatsResponse)
    console.log('分类响应:', categoryResponse)

    // 正确处理API响应格式
    stats.value = {
      totalVideos: videoStatsResponse?.success ? (videoStatsResponse.data?.total || 0) : 0,
      totalCategories: categoryResponse?.success ? (categoryResponse.data?.length || 0) : 0
    }

    categories.value = categoryResponse?.success ? (categoryResponse.data || []) : []

    console.log('处理后的统计数据:', stats.value)
    console.log('处理后的分类数据:', categories.value)

    // Load example data
    await loadJsonExample()
    await loadXmlExample()
  } catch (error) {
    console.error('Failed to fetch data:', error)
    // 设置默认统计值，但不显示假的分类数据
    stats.value = {
      totalVideos: 0,
      totalCategories: 0
    }
    categories.value = []

    // 加载默认示例数据
    await loadJsonExample()
    await loadXmlExample()
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
/* 自定义样式 */
code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 代码块样式 */
pre {
  max-height: 300px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #4a5568 #1a202c;
}

pre::-webkit-scrollbar {
  width: 6px;
}

pre::-webkit-scrollbar-track {
  background: #1a202c;
}

pre::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 3px;
}

pre::-webkit-scrollbar-thumb:hover {
  background: #718096;
}

/* 表格样式 */
.table-container {
  max-height: 400px;
  overflow-y: auto;
}

/* 响应式网格 */
@media (max-width: 768px) {
  .grid-responsive {
    grid-template-columns: 1fr;
  }
}

/* 视频网格样式 */
.video-grid {
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

@media (min-width: 640px) {
  .video-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (min-width: 1024px) {
  .video-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

@media (min-width: 1280px) {
  .video-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>

<template>
  <div class="bg-gray-900 min-h-screen">
    <!-- 主要内容区域 -->
    <main class="w-full px-4 lg:px-8 py-6">
      <div class="w-full lg:w-[70%] mx-auto">
        <!-- 图片广告 -->
        <section class="mb-8">
          <div class="relative bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl overflow-hidden shadow-2xl">
            <div class="aspect-[3/1] flex items-center justify-center relative">
              <!-- 广告背景图案 -->
              <div class="absolute inset-0 bg-gradient-to-br from-orange-400/20 to-red-600/20"></div>
              <div class="absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
              <div class="absolute bottom-4 left-4 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>

              <!-- 广告内容 -->
              <div class="relative z-10 text-center text-white">
                <h2 class="text-3xl md:text-4xl font-bold mb-2">{{ $t('home.bannerTitle') }}</h2>
                <p class="text-lg md:text-xl opacity-90">{{ $t('home.bannerSubtitle') }}</p>
                <div class="mt-4 flex items-center justify-center space-x-4 text-sm opacity-80">
                  <span>{{ $t('home.bannerFeatures.0') }}</span>
                  <span>{{ $t('home.bannerFeatures.1') }}</span>
                  <span>{{ $t('home.bannerFeatures.2') }}</span>
                </div>
              </div>

              <!-- 装饰元素 -->
              <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
            </div>
          </div>
        </section>

      <!-- 最近更新 -->
      <section class="mb-12">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h2 class="text-2xl font-bold text-white mb-2 flex items-center">
              <span class="text-green-500 mr-3">🆕</span>
              {{ $t('home.latest') }}
            </h2>
            <p class="text-gray-400 text-sm">{{ $t('home.latestDesc') }}</p>
          </div>
          <NuxtLink
            :to="localePath('/categories')"
            class="text-orange-500 hover:text-orange-400 font-medium transition-colors text-sm flex items-center space-x-1 group"
          >
            <span>{{ $t('common.more') }}</span>
            <svg class="w-4 h-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </NuxtLink>
        </div>
        
        <!-- 加载状态 -->
        <div v-if="loading" class="grid video-grid gap-5">
          <div v-for="i in 24" :key="i" class="bg-gray-800/60 rounded-xl overflow-hidden animate-pulse">
            <div class="aspect-video bg-gray-700"></div>
            <div class="p-4">
              <div class="h-4 bg-gray-700 rounded mb-2"></div>
              <div class="h-3 bg-gray-700 rounded w-2/3"></div>
            </div>
          </div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="text-center py-8">
          <p class="text-red-400">{{ error }}</p>
          <button @click="fetchLatestVideos" class="mt-4 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors">
            {{ $t('home.retry') }}
          </button>
        </div>

        <!-- 视频列表 -->
        <div v-else class="grid video-grid gap-5">
          <div
            v-for="video in recentVideos"
            :key="video.id"
            class="group bg-gray-800/60 backdrop-blur-sm rounded-xl overflow-hidden hover:bg-gray-700/60 transition-all duration-500 cursor-pointer hover:scale-[1.03] hover:shadow-xl hover:shadow-orange-500/10"
          >
            <div class="aspect-video bg-gradient-to-br from-gray-700 via-gray-800 to-gray-900 flex items-center justify-center relative overflow-hidden">
              <!-- 图片加载组件 -->
              <div v-if="video.coverUrl" class="w-full h-full relative">
                <!-- 骨架屏 -->
                <div
                  v-show="imageLoadingStates[video.id] !== false"
                  class="absolute inset-0 bg-gradient-to-br from-gray-600 via-gray-700 to-gray-800 animate-pulse flex items-center justify-center"
                >
                  <div class="text-center">
                    <div class="w-12 h-12 border-4 border-gray-500 border-t-orange-500 rounded-full animate-spin mb-3"></div>
                    <div class="text-gray-400 text-xs">{{ $t('home.loadingImage') }}</div>
                  </div>
                </div>

                <!-- 实际图片 -->
                <img
                  :src="video.coverUrl"
                  :alt="video.title"
                  class="w-full h-full object-cover transition-opacity duration-300"
                  :class="{ 'opacity-0': imageLoadingStates[video.id] !== false }"
                  @load="handleImageLoad(video.id)"
                  @error="handleImageError(video.id)"
                />
              </div>

              <!-- 无图片时的占位符 -->
              <div v-else class="w-full h-full flex items-center justify-center">
                <span class="text-white font-medium text-center p-3 text-sm leading-tight">{{ video.title }}</span>
              </div>

              <!-- 时长标签 -->
              <div v-if="video.duration" class="absolute bottom-2 right-2 bg-black/80 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-lg">
                {{ video.duration }}
              </div>
              <!-- 评分标签 -->
              <div v-if="video.rating" class="absolute top-2 right-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-lg font-bold">
                {{ video.rating }}
              </div>
              <!-- 播放按钮 -->
              <div class="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                <NuxtLink
                  :to="`/play/${video.id}`"
                  class="w-14 h-14 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center hover:scale-110 transition-all duration-300 shadow-xl shadow-orange-500/50"
                  :title="$t('home.playVideo')"
                >
                  <svg class="w-6 h-6 text-white ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </NuxtLink>
              </div>
            </div>
            <div class="p-4">
              <h3 class="text-white font-medium text-sm mb-2 line-clamp-2 leading-tight">{{ video.title }}</h3>
              <div class="flex items-center justify-between text-xs">
                <span class="text-gray-400 truncate mr-2">{{ video.categoryName || $t('home.uncategorized') }}</span>
                <span v-if="video.rating" class="text-orange-400 font-medium">⭐ {{ video.rating }}</span>
              </div>
            </div>
          </div>
        </div>
      </section>



      </div>
    </main>
  </div>
</template>

<script setup>
// 导入网站配置
import { siteConfig } from '~/config/site.js'

// i18n相关
const { locale, t } = useI18n()
const localePath = useLocalePath()

// 网站配置
const siteName = siteConfig.basic.siteName
const siteDescription = siteConfig.basic.siteDescription
const keywords = siteConfig.basic.keywords

// 页面元数据由 setHomeSEO() 统一管理





// 响应式数据
const recentVideos = ref([])
const loading = ref(true)
const error = ref(null)

// 图片加载状态管理
const imageLoadingStates = ref({})

// 处理图片加载完成
const handleImageLoad = (videoId) => {
  imageLoadingStates.value[videoId] = false
}

// 处理图片加载错误
const handleImageError = (videoId) => {
  imageLoadingStates.value[videoId] = false
  console.warn(`${t('home.imageLoadError')}: 视频ID ${videoId}`)
}

// 初始化图片加载状态
const initImageLoadingStates = (videos) => {
  videos.forEach(video => {
    if (video.coverUrl) {
      imageLoadingStates.value[video.id] = true
    }
  })
}

// 获取最新视频
const fetchLatestVideos = async () => {
  try {
    const { apiUser } = useApi()
    const response = await apiUser('/api/videos/featured/latest', {
      query: { limit: 24 }
    })

    if (response.success) {
      recentVideos.value = response.data
      // 初始化图片加载状态
      initImageLoadingStates(response.data)
    }
  } catch (err) {
    console.error('获取最新视频失败:', err)
    error.value = t('home.errorLoadingVideos')
  }
}



// 格式化观看次数
const formatViews = (views) => {
  if (views >= 1000000) {
    return (views / 1000000).toFixed(1) + 'M'
  } else if (views >= 1000) {
    return (views / 1000).toFixed(1) + 'K'
  }
  return views.toString()
}

// 基本SEO配置
useHead({
  title: `${siteConfig.basic.siteName} - 在线影视观看平台`,
  meta: [
    { name: 'description', content: siteConfig.basic.siteDescription },
    { name: 'keywords', content: siteConfig.basic.keywords },
    { name: 'author', content: siteConfig.basic.siteName }
  ]
})

// 页面加载时获取数据
onMounted(async () => {
  try {
    loading.value = true
    await fetchLatestVideos()
  } catch (err) {
    console.error('页面数据加载失败:', err)
    error.value = t('home.errorLoadingPage')
  } finally {
    loading.value = false
  }
})


</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

/* 移除重复的 video-grid 样式定义，使用全局样式 */
</style>

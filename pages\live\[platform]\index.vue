<template>
  <div class="bg-gray-900 min-h-screen">
    <!-- 主内容区 - 70%宽度布局 -->
    <main class="w-full px-4 lg:px-8 py-6">
      <div class="w-full lg:w-[70%] mx-auto">

        <!-- 面包屑导航 -->
        <nav class="mb-6">
          <div class="flex items-center space-x-2 text-sm">
            <NuxtLink
              to="/live"
              class="text-gray-400 hover:text-orange-400 transition-colors flex items-center"
            >
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
              </svg>
              直播平台
            </NuxtLink>
            <span class="text-gray-600">/</span>
            <span class="text-white font-medium">
              {{ platformInfo?.name || route.params.platform }}
            </span>
          </div>
        </nav>

        <!-- 页面头部横幅 -->
        <section class="mb-8">
          <div class="relative bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl overflow-hidden shadow-2xl">
            <div class="aspect-[3/1] flex items-center justify-center relative">
              <!-- 背景装饰 -->
              <div class="absolute inset-0 bg-gradient-to-br from-orange-400/20 to-red-600/20"></div>
              <div class="absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
              <div class="absolute bottom-4 left-4 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>

              <!-- 头部内容 -->
              <div class="relative z-10 text-center text-white">
                <h1 class="text-2xl md:text-3xl font-bold mb-2">
                  {{ platformInfo?.name || route.params.platform }} 直播间
                </h1>
                <p class="text-lg opacity-90 mb-4">
                  {{ rooms.length }} 个房间正在直播
                </p>
                <div class="flex items-center justify-center space-x-6 text-sm opacity-80">
                  <span>📺 实时直播</span>
                  <span>🔥 高清画质</span>
                  <span>👥 {{ formatViews(totalViewers) }} 观看</span>
                </div>
              </div>

              <!-- 装饰元素 -->
              <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
            </div>
          </div>
        </section>
        <!-- 房间列表标题 -->
        <section class="mb-8">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h2 class="text-2xl font-bold text-white mb-2 flex items-center">
                <span class="text-orange-500 mr-3">🏠</span>
                直播房间
              </h2>
              <p class="text-gray-400 text-sm">选择你感兴趣的直播房间开始观看</p>
            </div>
            <div v-if="!loading && rooms.length > 0" class="text-orange-500 font-medium text-sm">
              {{ rooms.length }} 个房间在线
            </div>
          </div>



          <!-- ✨ 优雅的加载骨架屏 -->
          <div v-if="loading" class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6">
            <div v-for="i in 10" :key="i" class="skeleton-card">
              <!-- 骨架卡片 -->
              <div class="bg-gradient-to-br from-gray-800/40 to-gray-900/40 backdrop-blur-sm border border-gray-700/30 rounded-2xl p-3 md:p-5 relative overflow-hidden">

                <!-- 流光动画背景 -->
                <div class="absolute inset-0 -translate-x-full animate-shimmer bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>

                <!-- 骨架图片区域 -->
                <div class="relative aspect-square rounded-xl mb-4 bg-gradient-to-br from-gray-700/50 to-gray-800/50 overflow-hidden">
                  <!-- 图片加载指示器 -->
                  <div class="absolute inset-0 flex items-center justify-center">
                    <div class="w-12 h-12 rounded-full border-2 border-gray-600/30 border-t-orange-500/50 animate-spin"></div>
                  </div>

                  <!-- 装饰性元素 -->
                  <div class="absolute top-3 left-3 w-8 h-4 bg-gray-600/40 rounded-full"></div>
                  <div class="absolute bottom-3 right-3 w-6 h-4 bg-gray-600/40 rounded"></div>
                </div>

                <!-- 骨架文字区域 -->
                <div class="space-y-3">
                  <!-- 标题骨架 -->
                  <div class="space-y-2">
                    <div class="h-4 bg-gradient-to-r from-gray-700/60 to-gray-600/60 rounded animate-pulse"></div>
                    <div class="h-4 bg-gradient-to-r from-gray-700/60 to-gray-600/60 rounded w-4/5 animate-pulse" style="animation-delay: 0.1s;"></div>
                  </div>

                  <!-- 信息骨架 -->
                  <div class="flex items-center justify-between">
                    <div class="h-3 bg-gradient-to-r from-gray-700/60 to-gray-600/60 rounded w-2/3 animate-pulse" style="animation-delay: 0.2s;"></div>
                    <div class="h-3 bg-gradient-to-r from-gray-700/60 to-gray-600/60 rounded w-6 animate-pulse" style="animation-delay: 0.3s;"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="error" class="flex flex-col items-center justify-center py-20">
            <div class="w-24 h-24 bg-red-500/10 rounded-full flex items-center justify-center mb-6">
              <svg class="w-12 h-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-white mb-2">加载失败</h3>
            <p class="text-gray-400 mb-6 text-center max-w-md">{{ error }}</p>
            <button
              @click="fetchRooms"
              class="px-6 py-3 bg-gradient-to-r from-orange-500 to-red-500 text-white font-medium rounded-xl hover:from-orange-600 hover:to-red-600 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              重新加载
            </button>
          </div>

          <!-- 空状态 -->
          <div v-else-if="!rooms.length" class="flex flex-col items-center justify-center py-20">
            <div class="w-24 h-24 bg-gray-700/30 rounded-full flex items-center justify-center mb-6">
              <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-white mb-2">暂无直播房间</h3>
            <p class="text-gray-400 text-center max-w-md">该平台当前没有可用的直播房间，请稍后再试</p>
          </div>

          <!-- 🚀 房间列表 - 完美设计 -->
          <div v-else class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6">
            <div
              v-for="room in rooms"
              :key="room.id"
              @click="openRoom(room)"
              class="group cursor-pointer"
            >
              <!-- 卡片容器 -->
              <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-3 md:p-5 transition-all duration-500 hover:bg-gray-700/50 hover:border-orange-500/50 hover:scale-[1.02] hover:shadow-2xl hover:shadow-orange-500/10">

                <!-- 图片容器 -->
                <div class="relative aspect-square rounded-xl overflow-hidden mb-4 bg-gray-700">

                  <!-- 图片加载状态 -->
                  <div v-if="room.imageLoading" class="absolute inset-0 flex items-center justify-center">
                    <div class="w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin"></div>
                  </div>

                  <!-- 图片加载失败 -->
                  <div v-else-if="room.imageError" class="absolute inset-0 flex flex-col items-center justify-center text-gray-400">
                    <svg class="w-12 h-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                    </svg>
                    <span class="text-xs">加载失败</span>
                  </div>

                  <!-- 实际图片 -->
                  <img
                    v-else-if="room.thumbnail"
                    :src="room.thumbnail"
                    :alt="room.title"
                    class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                    @load="handleImageLoad(room)"
                    @error="handleImageError(room)"
                    @loadstart="handleImageLoadStart(room)"
                  />

                  <!-- 默认状态 -->
                  <div v-else class="absolute inset-0 flex items-center justify-center text-gray-400">
                    <svg class="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                    </svg>
                  </div>

                  <!-- 直播状态标签 -->
                  <div class="absolute top-3 left-3">
                    <div class="flex items-center px-2 py-1 bg-red-500 text-white text-xs font-medium rounded-full shadow-lg">
                      <div class="w-2 h-2 bg-white rounded-full mr-1.5 animate-pulse"></div>
                      LIVE
                    </div>
                  </div>

                  <!-- 观看人数 -->
                  <div class="absolute bottom-3 right-3">
                    <div class="flex items-center px-2 py-1 bg-black/70 backdrop-blur-sm text-white text-xs font-medium rounded-lg">
                      <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                      </svg>
                      {{ formatViews(room.viewerCount || 0) }}
                    </div>
                  </div>

                  <!-- 悬停播放按钮 -->
                  <div class="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                    <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center transform scale-75 group-hover:scale-100 transition-transform duration-300">
                      <svg class="w-8 h-8 text-white ml-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M8 5v10l7-5-7-5z"/>
                      </svg>
                    </div>
                  </div>
                </div>

                <!-- 房间信息 -->
                <div class="space-y-3">
                  <!-- 房间标题 -->
                  <h3 class="font-bold text-white text-sm leading-tight line-clamp-2 group-hover:text-orange-400 transition-colors duration-300">
                    {{ room.title || room.streamerName }}
                  </h3>

                  <!-- 主播信息 -->
                  <div class="flex items-center justify-between">
                    <span class="text-gray-400 text-xs truncate">{{ room.streamerName }}</span>
                    <div v-if="room.rating" class="flex items-center text-yellow-400 text-xs font-medium">
                      <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                      </svg>
                      {{ room.rating }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="!loading && rooms.length === 0 && !error" class="text-center py-16">
            <div class="text-6xl mb-4">📺</div>
            <h2 class="text-2xl font-bold text-white mb-2">暂无直播房间</h2>
            <p class="text-gray-400">该平台当前没有正在直播的房间</p>
          </div>
        </section>
      </div>
    </main>


  </div>
</template>

<script setup>
// 获取路由参数
const route = useRoute()
const platform = route.params.platform

// 导入网站配置
import { siteConfig } from '~/config/site.js'

// 基本SEO配置
useHead({
  title: `${platform} 直播间 | ${siteConfig.basic.siteName}`,
  meta: [
    { name: 'description', content: `观看 ${platform} 平台的精彩直播内容，高清画质，实时互动` },
    { name: 'keywords', content: `${platform},直播,在线观看,高清直播,实时直播` },
    { name: 'author', content: siteConfig.basic.siteName }
  ]
})

// 响应式数据
const rooms = ref([])
const loading = ref(true)
const error = ref(null)

const platformInfo = ref(null)

// 计算属性
const totalViewers = computed(() => rooms.value.reduce((sum, room) => sum + (room.viewerCount || 0), 0))

// 格式化观看次数
const formatViews = (views) => {
  if (views >= 1000000) {
    return (views / 1000000).toFixed(1) + 'M'
  } else if (views >= 1000) {
    return (views / 1000).toFixed(1) + 'K'
  }
  return views.toString()
}

// API 工具
const { apiUser } = useApi()

// 获取房间列表
const fetchRooms = async () => {
  try {
    loading.value = true
    error.value = null

    // 调用后端API
    const response = await apiUser(`/api/live/${platform}/rooms`)
    
    if (response.success) {
      rooms.value = response.data
      platformInfo.value = {
        name: response.platformName || response.platform,
        id: response.platform
      }
    } else {
      throw new Error(response.message || '获取房间数据失败')
    }
    
  } catch (err) {
    console.error('获取房间列表失败:', err)
    error.value = err.message || '获取房间列表失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 打开房间 - 跳转到播放页面
const openRoom = async (room) => {
  // 跳转到房间播放页面
  const targetUrl = `/live/${route.params.platform}/${room.id}`
  console.log('🚀 跳转到房间:', targetUrl)
  console.log('📦 传递房间数据:', room)

  // 使用 Nuxt 的 useState 存储房间数据
  const currentRoomData = useState('currentRoomData', () => null)
  currentRoomData.value = {
    roomData: room,
    platformData: {
      name: platformInfo.value?.name || platform,
      id: route.params.platform
    },
    timestamp: Date.now()
  }

  // 使用 Nuxt 的 navigateTo 进行路由跳转
  await navigateTo(targetUrl)
}



// 处理图片加载开始
const handleImageLoadStart = (room) => {
  room.imageLoading = true
  room.imageError = false
}

// 处理图片加载成功
const handleImageLoad = (room) => {
  room.imageLoading = false
  room.imageError = false
}

// 处理图片加载错误
const handleImageError = (room) => {
  room.imageLoading = false
  room.imageError = true
}

// 页面加载时获取数据
onMounted(() => {
  fetchRooms()
})
</script>

<style scoped>
/* 房间网格布局 */
.rooms-grid {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

/* 响应式调整 */
@media (max-width: 768px) {
  .rooms-grid {
    grid-template-columns: 1fr;
  }
}

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 卡片悬停效果 */
.room-card:hover {
  transform: translateY(-2px);
}

/* 加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 骨架屏动画 */
.skeleton-card {
  animation: skeleton-fade 2s ease-in-out infinite alternate;
}

@keyframes skeleton-fade {
  0% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

/* 流光动画 */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}
</style>

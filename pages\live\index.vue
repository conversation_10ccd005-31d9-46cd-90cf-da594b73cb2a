<template>
  <div class="bg-gray-900 min-h-screen">
    <!-- 主内容区 - 70%宽度布局 -->
    <main class="w-full px-4 lg:px-8 py-6">
      <div class="w-full lg:w-[70%] mx-auto">

        <!-- 面包屑导航 -->
        <nav class="mb-6">
          <div class="flex items-center space-x-2 text-sm">
            <NuxtLink
              to="/"
              class="text-gray-400 hover:text-orange-400 transition-colors flex items-center"
            >
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
              </svg>
              首页
            </NuxtLink>
            <span class="text-gray-600">/</span>
            <span class="text-white font-medium">直播平台</span>
          </div>
        </nav>

        <!-- 统计信息横幅 -->
        <section class="mb-8">
          <div class="relative bg-gradient-to-r from-red-500 to-orange-500 rounded-2xl overflow-hidden shadow-2xl">
            <div class="aspect-[4/1] flex items-center justify-center relative">
              <!-- 背景装饰 -->
              <div class="absolute inset-0 bg-gradient-to-br from-red-400/20 to-orange-600/20"></div>
              <div class="absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
              <div class="absolute bottom-4 left-4 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>

              <!-- 统计内容 -->
              <div class="relative z-10 text-center text-white">
                <h2 class="text-2xl md:text-3xl font-bold mb-4">🔴 实时直播统计</h2>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto">
                  <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold">{{ totalPlatforms }}</div>
                    <div class="text-sm opacity-90">平台总数</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold">{{ formatViews(totalRooms) }}</div>
                    <div class="text-sm opacity-90">房间总数</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold">{{ formatViews(totalViewers) }}</div>
                    <div class="text-sm opacity-90">观看人数</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold">{{ onlinePlatforms }}</div>
                    <div class="text-sm opacity-90">在线平台</div>
                  </div>
                </div>
              </div>

              <!-- 装饰元素 -->
              <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
            </div>
          </div>
        </section>
        <!-- 直播平台列表标题 -->
        <section class="mb-8">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h2 class="text-2xl font-bold text-white mb-2 flex items-center">
                <span class="text-orange-500 mr-3">📺</span>
                直播平台列表
              </h2>
              <p class="text-gray-400 text-sm">选择你喜欢的直播平台，开始观看精彩内容</p>
            </div>
            <div v-if="!loading && platforms.length > 0" class="text-orange-500 font-medium text-sm">
              {{ platforms.length }} 个平台可用
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-6">
            <div v-for="i in 12" :key="i" class="bg-white/5 backdrop-blur-xl border border-white/10 rounded-3xl overflow-hidden animate-pulse">
              <div class="p-5">
                <div class="flex flex-col items-center mb-4">
                  <div class="w-24 h-24 bg-gray-700 rounded-3xl mb-4"></div>
                </div>
                <div class="text-center space-y-4">
                  <div class="h-5 bg-gray-700 rounded mx-auto w-3/4"></div>
                  <div class="flex justify-center space-x-6">
                    <div class="flex flex-col items-center space-y-1">
                      <div class="h-5 bg-gray-700 rounded w-10"></div>
                      <div class="h-3 bg-gray-700 rounded w-8"></div>
                    </div>
                    <div class="w-px h-10 bg-gray-600"></div>
                    <div class="flex flex-col items-center space-y-1">
                      <div class="h-5 bg-gray-700 rounded w-10"></div>
                      <div class="h-3 bg-gray-700 rounded w-8"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="error" class="text-center py-16">
            <div class="text-6xl mb-4">😞</div>
            <h2 class="text-2xl font-bold text-white mb-2">加载失败</h2>
            <p class="text-gray-400 mb-6">{{ error }}</p>
            <button @click="fetchPlatforms" class="px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors">
              重新加载
            </button>
          </div>

          <!-- 平台列表 -->
          <div v-else class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-6">
            <div
              v-for="platform in platforms"
              :key="platform.id"
              class="group relative bg-white/5 backdrop-blur-xl border border-white/10 rounded-3xl overflow-hidden hover:bg-white/10 hover:border-orange-500/30 transition-all duration-500 cursor-pointer hover:scale-105 hover:shadow-2xl hover:shadow-orange-500/20"
              @click="navigateToPlatform(platform.id)"
            >
              <!-- 背景装饰 -->
              <div class="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              <!-- 卡片内容 -->
              <div class="relative p-5">
                <!-- 平台图标区域 -->
                <div class="flex flex-col items-center mb-4">
                  <div class="relative">
                    <!-- 图标背景光晕 -->
                    <div class="absolute inset-0 bg-gradient-to-br from-orange-400 to-red-500 rounded-3xl blur-lg opacity-20 group-hover:opacity-40 transition-opacity duration-500"></div>

                    <!-- 图标容器 -->
                    <div class="relative w-24 h-24 bg-gradient-to-br from-gray-800 to-gray-900 rounded-3xl p-1 shadow-2xl">
                      <div class="w-full h-full bg-gradient-to-br from-gray-700 to-gray-800 rounded-[20px] flex items-center justify-center overflow-hidden">
                        <!-- 图片加载状态 -->
                        <div v-if="platform.imageLoading" class="w-full h-full flex items-center justify-center">
                          <div class="w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin"></div>
                        </div>

                        <!-- 图片加载失败状态 -->
                        <div v-else-if="platform.imageError" class="w-full h-full flex flex-col items-center justify-center text-gray-400">
                          <div class="text-2xl mb-1">📺</div>
                          <div class="text-xs">加载失败</div>
                        </div>

                        <!-- 正常图片显示 -->
                        <img
                          v-else-if="platform.icon && platform.icon.startsWith('http')"
                          :src="platform.icon"
                          :alt="platform.name"
                          class="w-full h-full object-cover rounded-[16px]"
                          @load="handleImageLoad(platform)"
                          @error="handleImageError(platform)"
                          @loadstart="handleImageLoadStart(platform)"
                        />

                        <!-- 默认图标 -->
                        <span v-else class="text-4xl">{{ platform.icon || '📺' }}</span>
                      </div>
                    </div>

                    <!-- 推荐标签 - 移到图标左上角 -->
                    <div v-if="platform.featured" class="absolute -top-1 -left-1">
                      <span class="inline-flex items-center justify-center w-6 h-6 bg-gradient-to-r from-yellow-500 to-orange-500 text-white text-sm rounded-full shadow-lg">
                        ⭐
                      </span>
                    </div>

                    <!-- 在线状态指示器 -->
                    <div v-if="platform.status === 'online'" class="absolute -top-1 -right-1">
                      <div class="w-6 h-6 bg-green-500 rounded-full border-3 border-gray-900 flex items-center justify-center shadow-lg">
                        <div class="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 平台信息 -->
                <div class="text-center space-y-4">
                  <!-- 平台名称 -->
                  <h3 class="text-white font-bold text-base group-hover:text-orange-400 transition-colors duration-300 line-clamp-1">
                    {{ platform.name }}
                  </h3>

                  <!-- 统计信息 -->
                  <div class="flex justify-center space-x-6 text-sm">
                    <div class="flex flex-col items-center space-y-1">
                      <span class="text-orange-400 font-bold text-lg">{{ platform.roomCount || 0 }}</span>
                      <span class="text-gray-400 text-xs">房间</span>
                    </div>
                    <div class="w-px h-10 bg-gray-600"></div>
                    <div class="flex flex-col items-center space-y-1">
                      <span class="text-green-400 font-bold text-lg">{{ formatViews(platform.viewerCount || 0) }}</span>
                      <span class="text-gray-400 text-xs">观看</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="!loading && platforms.length === 0 && !error" class="text-center py-16">
            <div class="text-6xl mb-4">📺</div>
            <h2 class="text-2xl font-bold text-white mb-2">暂无直播平台</h2>
            <p class="text-gray-400">请稍后再试或联系管理员</p>
          </div>
        </section>
      </div>
    </main>
  </div>
</template>

<script setup>
// 导入网站配置
import { siteConfig } from '~/config/site.js'

// 基本配置

// 基本SEO配置
useHead({
  title: `直播平台 | ${siteConfig.basic.siteName}`,
  meta: [
    { name: 'description', content: '观看各大直播平台的精彩内容，高清画质，实时互动' },
    { name: 'keywords', content: '直播,在线直播,直播平台,实时直播' },
    { name: 'author', content: siteConfig.basic.siteName }
  ]
})

// 响应式数据
const platforms = ref([])
const loading = ref(true)
const error = ref(null)

// 统计数据
const totalPlatforms = computed(() => platforms.value.length)
const totalRooms = computed(() => platforms.value.reduce((sum, p) => sum + (p.roomCount || 0), 0))
const totalViewers = computed(() => platforms.value.reduce((sum, p) => sum + (p.viewerCount || 0), 0))
const onlinePlatforms = computed(() => platforms.value.filter(p => p.status === 'online').length)

// 格式化观看次数
const formatViews = (views) => {
  if (views >= 1000000) {
    return (views / 1000000).toFixed(1) + 'M'
  } else if (views >= 1000) {
    return (views / 1000).toFixed(1) + 'K'
  }
  return views.toString()
}

// API 工具
const { apiUser } = useApi()

// 获取直播平台列表
const fetchPlatforms = async () => {
  try {
    loading.value = true
    error.value = null

    // 调用后端API
    const response = await apiUser('/api/live/platforms')
    
    if (response.success) {
      platforms.value = response.data
    } else {
      throw new Error(response.message || '获取平台数据失败')
    }
    
  } catch (err) {
    console.error('获取直播平台失败:', err)
    error.value = err.message || '获取直播平台失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 导航到平台页面
const navigateToPlatform = (platformId) => {
  navigateTo(`/live/${platformId}`)
}

// 页面加载时获取数据
onMounted(() => {
  fetchPlatforms()
})

// 处理图片加载开始
const handleImageLoadStart = (platform) => {
  platform.imageLoading = true
  platform.imageError = false
}

// 处理图片加载成功
const handleImageLoad = (platform) => {
  platform.imageLoading = false
  platform.imageError = false
}

// 处理图片加载错误
const handleImageError = (platform) => {
  platform.imageLoading = false
  platform.imageError = true
}

// 组件卸载时清理资源
onUnmounted(() => {
  // 清理定时器等资源
})
</script>

<style scoped>
/* 文本截断 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
</style>

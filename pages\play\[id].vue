<template>
  <div class="bg-gray-900 min-h-screen">
    <div class="w-full px-4 lg:px-8 py-6">
      <div class="w-full lg:w-[70%] mx-auto">
        <!-- 面包屑导航 -->
        <nav class="mb-6">
          <ol class="flex items-center space-x-2 text-sm">
            <li>
              <NuxtLink :to="$localePath('/')" class="text-gray-400 hover:text-orange-400 transition-colors">
                {{ $t('play.breadcrumb.home') }}
              </NuxtLink>
            </li>
            <li class="text-gray-600">/</li>
            <li>
              <span class="text-orange-400">{{ $t('play.breadcrumb.play') }}</span>
            </li>
          </ol>
        </nav>
        <!-- 加载状态 -->
        <div v-if="loading" class="animate-pulse">
          <div class="aspect-video bg-gray-700 rounded-xl mb-8"></div>
          <div class="bg-gray-800/60 rounded-xl p-6 lg:p-8 mb-8">
            <div class="h-8 bg-gray-700 rounded mb-6"></div>
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              <div v-for="i in 4" :key="i" class="h-20 bg-gray-700 rounded-xl"></div>
            </div>
            <div class="h-4 bg-gray-700 rounded mb-2"></div>
            <div class="h-4 bg-gray-700 rounded w-2/3"></div>
          </div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="text-center py-16">
          <div class="text-6xl mb-4">😞</div>
          <h2 class="text-2xl font-bold text-white mb-2">{{ $t('play.error.title') }}</h2>
          <p class="text-gray-400 mb-6">{{ error }}</p>
          <button @click="fetchVideoDetails" class="px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors">
            {{ $t('play.error.retry') }}
          </button>
        </div>

        <!-- 视频内容 -->
        <div v-else-if="movie">
          <div class="mb-8">
            <div class="aspect-video bg-black rounded-xl overflow-hidden relative group shadow-2xl">
              <ClientOnly>
                <!-- 使用新的 VideoPlayer 组件 -->
                <VideoPlayer
                  v-if="movie.videoUrl"
                  :src="movie.videoUrl"
                  :poster="movie.coverUrl"
                  :autoplay="false"
                  @ready="onPlayerReady"
                  @play="onPlayerPlay"
                  @error="onPlayerError"
                  class="w-full h-full"
                />
                <template v-else>
                  <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900">
                    <div class="text-center">
                      <div class="w-24 h-24 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mb-4 mx-auto shadow-lg">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M8 5v14l11-7z"/>
                        </svg>
                      </div>
                      <p class="text-white text-xl font-medium">{{ $t('play.player.noVideoSource') }}</p>
                      <p class="text-gray-400 text-sm mt-2">{{ movie.duration || $t('play.player.unknownDuration') }}</p>
                    </div>
                  </div>
                </template>
                <template #fallback>
                  <!-- 服务端渲染时的占位符 -->
                  <div class="w-full h-full flex items-center justify-center bg-gray-900">
                    <div class="text-center">
                      <div class="w-20 h-20 bg-gray-700 rounded-full flex items-center justify-center mb-4 mx-auto animate-pulse">
                        <svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M8 5v10l8-5-8-5z"/>
                        </svg>
                      </div>
                      <p class="text-gray-400">{{ $t('play.player.loading') }}</p>
                    </div>
                  </div>
                </template>
              </ClientOnly>
            </div>
          </div>

          <div class="bg-gray-800/60 backdrop-blur-sm rounded-xl p-6 lg:p-8 mb-8">
            <h1 class="text-2xl lg:text-4xl font-bold text-white mb-6 leading-tight">
              {{ movie.title }}
            </h1>

            <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              <div v-if="movie.rating" class="text-center p-4 bg-gray-700/50 rounded-xl">
                <div class="text-orange-400 text-xl font-bold">{{ movie.rating }}</div>
                <div class="text-gray-400 text-sm mt-1">{{ $t('play.info.rating') }}</div>
              </div>
              <div class="text-center p-4 bg-gray-700/50 rounded-xl">
                <div class="text-orange-400 text-xl font-bold">{{ formatViews(movie.views || 0) }}</div>
                <div class="text-gray-400 text-sm mt-1">{{ $t('play.info.views') }}</div>
              </div>
              <div v-if="movie.duration" class="text-center p-4 bg-gray-700/50 rounded-xl">
                <div class="text-orange-400 text-xl font-bold">{{ movie.duration }}</div>
                <div class="text-gray-400 text-sm mt-1">{{ $t('play.info.duration') }}</div>
              </div>
              <div class="text-center p-4 bg-gray-700/50 rounded-xl">
                <div class="text-orange-400 text-xl font-bold">{{ movie.status === 'active' ? $t('play.info.statusActive') : $t('play.info.statusInactive') }}</div>
                <div class="text-gray-400 text-sm mt-1">{{ $t('play.info.status') }}</div>
              </div>
            </div>

            <div class="flex flex-wrap items-center gap-3 mb-8">
              <span v-if="movie.categoryName" class="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-sm rounded-lg font-medium shadow-md">
                {{ movie.categoryName }}
              </span>
              <span v-if="movie.featured" class="px-4 py-2 bg-gradient-to-r from-red-500 to-pink-600 text-white text-sm rounded-lg font-medium shadow-md">
                {{ $t('play.info.featured') }}
              </span>
              <span
                v-for="tag in (movie.tags || [])"
                :key="tag"
                class="px-3 py-1 bg-gray-700/80 text-gray-300 text-sm rounded-lg hover:bg-gray-600/80 transition-all duration-200 cursor-pointer border border-gray-600/50"
              >
                {{ tag }}
              </span>
            </div>


          </div>

          <!-- 视频描述 -->
          <div v-if="movie.description" class="bg-gray-800/60 backdrop-blur-sm rounded-xl p-6 mb-8">
            <h3 class="text-lg font-bold text-white mb-4">{{ $t('play.sections.description') }}</h3>
            <p class="text-gray-300 leading-relaxed">
              {{ movie.description }}
            </p>
          </div>

          <!-- 推荐视频 -->
          <div v-if="recommendedVideos.length > 0" class="bg-gray-800/60 backdrop-blur-sm rounded-xl p-6 mb-8">
            <h3 class="text-lg font-bold text-white mb-6">{{ $t('play.sections.recommended') }}</h3>
            <div class="grid video-grid gap-5">
              <div
                v-for="video in recommendedVideos"
                :key="video.id"
                class="group bg-gray-700/50 rounded-xl overflow-hidden hover:bg-gray-600/50 transition-all duration-300 cursor-pointer"
              >
                <NuxtLink :to="$localePath(`/play/${video.id}`)" class="block">
                  <div class="aspect-video bg-gradient-to-br from-gray-600 to-gray-700 flex items-center justify-center relative overflow-hidden">
                    <!-- 骨架屏 -->
                    <div
                      v-if="video.coverUrl && !video.imageLoaded"
                      class="absolute inset-0 bg-gradient-to-r from-gray-700 via-gray-600 to-gray-700 animate-pulse"
                    >
                      <div class="w-full h-full bg-gradient-to-r from-transparent via-gray-500/20 to-transparent animate-shimmer"></div>
                    </div>

                    <!-- 图片 -->
                    <img
                      v-if="video.coverUrl && !video.imageError"
                      :src="video.coverUrl"
                      :alt="video.title"
                      class="w-full h-full object-cover transition-opacity duration-300"
                      :class="{ 'opacity-0': !video.imageLoaded, 'opacity-100': video.imageLoaded }"
                      @load="video.imageLoaded = true"
                      @error="handleImageError(video)"
                      crossorigin="anonymous"
                    />

                    <!-- 无图片时的占位符 -->
                    <div v-else-if="!video.coverUrl" class="text-white text-sm text-center p-2">
                      {{ video.title }}
                    </div>

                    <!-- 图片加载失败时的占位符 -->
                    <div v-else-if="video.imageError" class="text-white text-sm text-center p-2">
                      {{ video.title }}
                    </div>

                    <!-- 时长标签 -->
                    <div v-if="video.duration" class="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded">
                      {{ video.duration }}
                    </div>

                    <!-- 播放按钮 -->
                    <div class="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                      <div class="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-white ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M8 5v14l11-7z"/>
                        </svg>
                      </div>
                    </div>
                  </div>
                  <div class="p-3">
                    <h4 class="text-white text-sm font-medium line-clamp-2 mb-1">{{ video.title }}</h4>
                    <div class="flex items-center justify-between text-xs">
                      <span class="text-gray-400">{{ video.categoryName || $t('play.info.uncategorized') }}</span>
                      <span v-if="video.rating" class="text-orange-400">⭐ {{ video.rating }}</span>
                    </div>
                  </div>
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 播放器库变量
let Plyr = null
let Hls = null

const route = useRoute()
const movieId = route.params.id
const { t } = useI18n()

// SEO优化
const { setVideoSEO } = useSEO()

// 响应式数据
const movie = ref(null)
const recommendedVideos = ref([])
const loading = ref(true)
const error = ref(null)

// 播放器事件处理
const onPlayerReady = (player) => {
  console.log('播放器准备就绪')
}

const onPlayerPlay = () => {
  console.log('开始播放')
  // 统计播放事件
  if (movie.value) {
    trackVideoPlay(movie.value.id, movie.value.title)
  }
}

// 视频播放统计函数
const trackVideoPlay = (videoId, videoTitle) => {
  try {
    console.log('统计视频播放:', { videoId, videoTitle })
    // 这里可以添加实际的统计逻辑，比如发送到分析服务
    // 例如：发送到 Google Analytics, 百度统计等
  } catch (error) {
    console.error('统计播放事件失败:', error)
  }
}

const onPlayerError = (error) => {
  console.error('播放器错误:', error)
  showVideoError('视频播放失败')
}

// 图片错误处理
const handleImageError = (video) => {
  console.log('图片加载失败:', video.coverUrl)
  video.imageError = true
  video.imageLoaded = true
}

// 验证视频URL是否有效
const validateVideoUrl = (url) => {
  if (!url) {
    console.error('视频URL为空')
    return false
  }

  // 检查URL格式
  try {
    const urlObj = new URL(url)
    // 检查协议是否有效
    if (!['http:', 'https:', 'blob:', 'data:'].includes(urlObj.protocol)) {
      console.error('不支持的URL协议:', urlObj.protocol)
      return false
    }
    return true
  } catch (error) {
    console.error('无效的URL格式:', url)
    return false
  }
}

// 显示视频错误信息
const showVideoError = (message) => {
  console.error('视频播放错误:', message)
  // 可以在这里添加用户友好的错误提示
}

// 获取视频详情
const fetchVideoDetails = async () => {
  try {
    const { apiUser } = useApi()
    const response = await apiUser(`/api/videos/${movieId}`)

    if (response.success) {
      movie.value = response.data.video
      // 为推荐视频添加图片加载状态
      recommendedVideos.value = (response.data.recommendedVideos || []).map(video => ({
        ...video,
        imageLoaded: false,
        imageError: false
      }))

      console.log('获取到的视频数据:', movie.value)
      console.log('视频URL:', movie.value.videoUrl)
      console.log('封面URL:', movie.value.coverUrl)

      // 更新页面标题
      useHead({
        title: computed(() => `${movie.value.title} ${t('play.meta.titleSuffix')}`),
        meta: [
          { name: 'description', content: computed(() => movie.value.description || t('play.meta.defaultDescription')) },
          { name: 'keywords', content: computed(() => movie.value.tags ? movie.value.tags.join(',') : t('play.meta.defaultKeywords')) }
        ]
      })
    } else {
      error.value = t('play.error.videoNotFound')
    }
  } catch (err) {
    console.error('获取视频详情失败:', err)
    error.value = t('play.error.loadFailed')
  }
}

// 增加观看次数
const incrementViews = async () => {
  try {
    const { apiUser } = useApi()
    await apiUser(`/api/videos/${movieId}/view`, {
      method: 'POST'
    })
  } catch (err) {
    console.error('增加观看次数失败:', err)
  }
}

// 旧的 DOM 等待函数已移除

// 旧的播放器代码已移除，现在使用 VideoPlayer 组件

// 页面加载时获取数据
onMounted(async () => {
  try {
    loading.value = true
    await fetchVideoDetails()
    await incrementViews()

    // 等待DOM更新后初始化播放器
    await nextTick()

    // 播放器现在由 VideoPlayer 组件自动处理
  } catch (err) {
    console.error('页面数据加载失败:', err)
    error.value = '页面数据加载失败'
  } finally {
    loading.value = false
  }
})

// 监听movie数据变化，设置SEO和初始化播放器
watch(() => movie.value, (newMovie) => {
  if (newMovie) {
    // 设置视频页面SEO
    setVideoSEO({
      title: newMovie.title,
      description: newMovie.description || `观看${newMovie.title}，高清在线播放`,
      thumbnail: newMovie.coverUrl,
      tags: newMovie.tags || [],
      uploadDate: newMovie.createdAt,
      duration: newMovie.duration,
      contentUrl: newMovie.videoUrl,
      embedUrl: `https://91jspg.com/play/${newMovie.id}`,
      categoryName: newMovie.categoryName,
      categoryId: newMovie.categoryId
    })

    // 播放器现在由 VideoPlayer 组件自动处理
  }
}, { immediate: false })

// 播放器清理现在由 VideoPlayer 组件自动处理



const formatViews = (views) => {
  if (views >= 1000000) {
    return (views / 1000000).toFixed(1) + 'M'
  } else if (views >= 1000) {
    return (views / 1000).toFixed(1) + 'K'
  }
  return views.toString()
}
</script>

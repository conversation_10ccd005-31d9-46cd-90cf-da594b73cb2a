<template>
  <div class="bg-gray-900 min-h-screen">
    <!-- 主要内容区域 -->
    <main class="w-full px-4 lg:px-8 py-6">
      <div class="w-full lg:w-[70%] mx-auto">
        <!-- 面包屑导航 -->
        <nav class="mb-6">
          <ol class="flex items-center space-x-2 text-sm">
            <li>
              <NuxtLink :to="$localePath('/')" class="text-gray-400 hover:text-orange-400 transition-colors">
                {{ $t('rankings.breadcrumb.home') }}
              </NuxtLink>
            </li>
            <li class="text-gray-600">/</li>
            <li>
              <span class="text-orange-400 font-medium">{{ $t('rankings.breadcrumb.rankings') }}</span>
            </li>
          </ol>
        </nav>

        <!-- 页面标题区域 -->
        <section class="mb-8">
          <div class="relative bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl overflow-hidden shadow-2xl">
            <div class="aspect-[3/1] flex items-center justify-center relative">
              <!-- 背景图案 -->
              <div class="absolute inset-0 bg-gradient-to-br from-orange-400/20 to-red-600/20"></div>
              <div class="absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
              <div class="absolute bottom-4 left-4 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>

              <!-- 标题内容 -->
              <div class="relative z-10 text-center text-white">
                <h1 class="text-3xl md:text-4xl font-bold mb-2 flex items-center justify-center">
                  <span class="text-yellow-300 mr-3">🏆</span>
                  {{ $t('rankings.title') }}
                </h1>
                <p class="text-lg md:text-xl opacity-90">{{ $t('rankings.subtitle') }}</p>
              </div>

              <!-- 装饰元素 -->
              <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
            </div>
          </div>
        </section>
        <!-- 排行榜分类选项卡 -->
        <section class="mb-12">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h2 class="text-2xl font-bold text-white mb-2 flex items-center">
                <span class="text-orange-500 mr-3">📊</span>
                {{ $t('rankings.categories.title') }}
              </h2>
              <p class="text-gray-400 text-sm">{{ $t('rankings.categories.subtitle') }}</p>
            </div>
          </div>

          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button
              v-for="tab in rankingTabs"
              :key="tab.key"
              @click="activeTab = tab.key"
              :class="[
                'p-4 rounded-xl text-sm font-medium transition-all duration-300 flex flex-col items-center space-y-2 border',
                activeTab === tab.key
                  ? 'bg-orange-500 text-white border-orange-500 shadow-lg'
                  : 'bg-gray-800/60 text-gray-300 border-gray-700 hover:bg-gray-700/60 hover:text-white'
              ]"
            >
              <span class="text-2xl">{{ tab.icon }}</span>
              <span>{{ tab.name }}</span>
            </button>
          </div>
        </section>

        <!-- 排行榜内容 -->
        <section class="mb-12">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h2 class="text-2xl font-bold text-white mb-2 flex items-center">
                <span class="text-green-500 mr-3">{{ getCurrentTabInfo().icon }}</span>
                {{ getCurrentTabInfo().name }}
              </h2>
              <p class="text-gray-400 text-sm">{{ getCurrentTabInfo().period }}数据</p>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="grid video-grid gap-5">
            <div v-for="i in 24" :key="i" class="bg-gray-800/60 rounded-xl overflow-hidden animate-pulse">
              <div class="aspect-video bg-gray-700"></div>
              <div class="p-4">
                <div class="h-4 bg-gray-700 rounded mb-2"></div>
                <div class="h-3 bg-gray-700 rounded w-2/3"></div>
              </div>
            </div>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="error" class="text-center py-8">
            <p class="text-red-400">{{ error }}</p>
            <button @click="fetchRankingData()" class="mt-4 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors">
              {{ $t('rankings.error.reload') }}
            </button>
          </div>

          <!-- 排行榜列表 -->
          <div v-else class="grid video-grid gap-5">
            <div
              v-for="(video, index) in currentRankingList"
              :key="video.id"
              class="group bg-gray-800/60 backdrop-blur-sm rounded-xl overflow-hidden hover:bg-gray-700/60 transition-all duration-500 cursor-pointer hover:scale-[1.03] hover:shadow-xl hover:shadow-orange-500/10 relative"
              @click="goToVideo(video)"
            >
              <!-- 排名徽章 -->
              <div class="absolute top-2 left-2 z-10">
                <div
                  :class="[
                    'w-8 h-8 rounded-lg flex items-center justify-center font-bold text-sm shadow-lg',
                    index === 0 ? 'bg-gradient-to-r from-yellow-400 to-yellow-500 text-yellow-900' :
                    index === 1 ? 'bg-gradient-to-r from-gray-300 to-gray-400 text-gray-800' :
                    index === 2 ? 'bg-gradient-to-r from-orange-400 to-orange-500 text-orange-900' :
                    'bg-gray-600 text-gray-300'
                  ]"
                >
                  {{ index + 1 }}
                </div>
              </div>

              <!-- 视频封面 -->
              <div class="aspect-video bg-gradient-to-br from-gray-700 via-gray-800 to-gray-900 flex items-center justify-center relative overflow-hidden">
                <!-- 图片加载组件 -->
                <div v-if="video.coverUrl" class="w-full h-full relative">
                  <!-- 骨架屏 -->
                  <div
                    v-show="imageLoadingStates[video.id] !== false"
                    class="absolute inset-0 bg-gradient-to-br from-gray-600 via-gray-700 to-gray-800 animate-pulse flex items-center justify-center"
                  >
                    <div class="text-center">
                      <div class="w-12 h-12 border-4 border-gray-500 border-t-orange-500 rounded-full animate-spin mb-3"></div>
                      <div class="text-gray-400 text-xs">加载中...</div>
                    </div>
                  </div>

                  <!-- 实际图片 -->
                  <img
                    :src="video.coverUrl"
                    :alt="video.title"
                    class="w-full h-full object-cover transition-opacity duration-300"
                    :class="{ 'opacity-0': imageLoadingStates[video.id] !== false }"
                    @load="handleImageLoad(video.id)"
                    @error="handleImageError(video.id)"
                  />
                </div>

                <!-- 无图片时的占位符 -->
                <div v-else class="w-full h-full flex items-center justify-center">
                  <span class="text-white font-medium text-center p-3 text-sm leading-tight">{{ video.title }}</span>
                </div>

                <!-- 排行数据标签 -->
                <div class="absolute bottom-2 right-2 bg-black/80 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-lg">
                  {{ formatRankingData(video) }}
                </div>

                <!-- 播放按钮 -->
                <div class="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                  <div class="w-14 h-14 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center hover:scale-110 transition-all duration-300 shadow-xl shadow-orange-500/50">
                    <svg class="w-6 h-6 text-white ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
              </div>

              <!-- 视频信息 -->
              <div class="p-4">
                <h3 class="text-white font-medium text-sm mb-2 line-clamp-2 leading-tight">{{ video.title }}</h3>
                <div class="flex items-center justify-between text-xs">
                  <span class="text-gray-400 truncate mr-2">{{ video.categoryName || $t('rankings.labels.uncategorized') }}</span>
                  <span class="text-orange-400 font-medium">{{ getRankingLabel() }}</span>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </main>
  </div>
</template>

<script setup>
// 页面元信息
definePageMeta({
  title: '排行榜 - 91JSPG.COM'
})

// 多语言和页面标题
const { t } = useI18n()

// SEO优化
const { setRankingsSEO } = useSEO()

// 设置排行榜页面SEO
setRankingsSEO()

// 响应式数据
const loading = ref(true)
const error = ref(null)
const activeTab = ref('hot')

// 图片加载状态管理
const imageLoadingStates = ref({})

// 处理图片加载完成
const handleImageLoad = (videoId) => {
  imageLoadingStates.value[videoId] = false
}

// 处理图片加载错误
const handleImageError = (event, videoId) => {
  imageLoadingStates.value[videoId] = false
  event.target.style.display = 'none'
}

// 初始化图片加载状态
const initImageLoadingStates = (videos) => {
  videos.forEach(video => {
    if (video.coverUrl) {
      imageLoadingStates.value[video.id] = true
    }
  })
}

// 排行榜数据
const rankingData = ref({
  hot: [],
  latest: [],
  rating: [],
  views: []
})

// 排行榜分类
const rankingTabs = computed(() => [
  { key: 'hot', name: t('rankings.tabs.hot'), icon: '🔥', period: t('rankings.periods.thisWeek') },
  { key: 'latest', name: t('rankings.tabs.latest'), icon: '🆕', period: t('rankings.periods.today') },
  { key: 'rating', name: t('rankings.tabs.rating'), icon: '⭐', period: t('rankings.periods.thisMonth') },
  { key: 'views', name: t('rankings.tabs.views'), icon: '👁️', period: t('rankings.periods.thisMonth') }
])

// 当前排行榜列表
const currentRankingList = computed(() => {
  return rankingData.value[activeTab.value] || []
})

// 获取当前标签页信息
const getCurrentTabInfo = () => {
  return rankingTabs.value.find(tab => tab.key === activeTab.value) || rankingTabs.value[0]
}

// 获取排行榜数据
const fetchRankingData = async () => {
  try {
    loading.value = true
    error.value = null

    // 并行获取排行榜数据
    const { apiUser } = useApi()
    const [hotResponse, latestResponse, featuredResponse, viewsResponse] = await Promise.all([
      apiUser('/api/videos/featured/hot', { query: { limit: 20 } }),
      apiUser('/api/videos/featured/latest', { query: { limit: 20 } }),
      apiUser('/api/videos/featured/recommended', { query: { limit: 20 } }),
      apiUser('/api/videos', { query: { sort: 'views', limit: 20 } })
    ])

    if (hotResponse.success) {
      rankingData.value.hot = hotResponse.data.map(video => ({ ...video, rankingValue: video.views }))
      initImageLoadingStates(hotResponse.data)
    }

    if (latestResponse.success) {
      rankingData.value.latest = latestResponse.data.map(video => ({ ...video, rankingValue: new Date(video.createdAt).getTime() }))
      initImageLoadingStates(latestResponse.data)
    }

    if (featuredResponse.success) {
      rankingData.value.rating = featuredResponse.data.map(video => ({
        ...video,
        rating: parseFloat(video.rating) || 0,
        rankingValue: parseFloat(video.rating) || 0
      }))
      initImageLoadingStates(featuredResponse.data)
    }

    if (viewsResponse.success) {
      rankingData.value.views = (viewsResponse.data.videos || viewsResponse.data).map(video => ({ ...video, rankingValue: video.views }))
      initImageLoadingStates(viewsResponse.data.videos || viewsResponse.data)
    }

  } catch (err) {
    console.error('获取排行榜数据失败:', err)
    error.value = t('rankings.error.loadFailed')
  } finally {
    loading.value = false
  }
}

// 跳转到视频详情页
const { $localePath } = useNuxtApp()
const goToVideo = (video) => {
  navigateTo($localePath(`/play/${video.id}`))
}

// 格式化观看次数
const formatViews = (views) => {
  const numViews = parseInt(views) || 0
  if (numViews >= 1000000) return (numViews / 1000000).toFixed(1) + 'M'
  if (numViews >= 1000) return (numViews / 1000).toFixed(1) + 'K'
  return numViews.toString()
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now - date)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 1) return t('rankings.dateFormat.today')
  if (diffDays === 2) return t('rankings.dateFormat.yesterday')
  if (diffDays <= 7) return t('rankings.dateFormat.daysAgo', { days: diffDays })
  if (diffDays <= 30) return t('rankings.dateFormat.weeksAgo', { weeks: Math.ceil(diffDays / 7) })
  return t('rankings.dateFormat.monthsAgo', { months: Math.ceil(diffDays / 30) })
}

// 格式化排行数据
const formatRankingData = (video) => {
  if (!video) return '0'

  switch (activeTab.value) {
    case 'hot':
    case 'views':
      return formatViews(video.views)
    case 'latest':
      return formatDate(video.createdAt)
    case 'rating':
      const rating = parseFloat(video.rating) || 0
      return rating.toFixed(1)
    default:
      return formatViews(video.views)
  }
}

// 获取排行标签
const getRankingLabel = () => {
  switch (activeTab.value) {
    case 'hot':
    case 'views':
      return t('rankings.labels.viewCount')
    case 'latest':
      return t('rankings.labels.publishTime')
    case 'rating':
      return t('rankings.labels.rating')
    default:
      return t('rankings.labels.viewCount')
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchRankingData()
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}
</style>
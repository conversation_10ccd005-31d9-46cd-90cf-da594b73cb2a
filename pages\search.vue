<template>
  <div class="bg-gray-900 min-h-screen">
    <!-- 主内容区 - 70%宽度布局 -->
    <main class="w-full px-4 lg:px-8 py-6">
      <div class="w-full lg:w-[70%] mx-auto">
      <!-- 搜索框 -->
      <div class="mb-8">
        <div class="max-w-2xl mx-auto">
          <div class="relative">
            <input
              v-model="searchQuery"
              @keyup.enter="performSearch"
              type="text"
              :placeholder="$t('search.placeholder')"
              class="w-full bg-gray-800 text-white border border-gray-600 rounded-xl px-6 py-4 pr-12 text-lg focus:outline-none focus:border-orange-500 transition-colors"
            />
            <button
              @click="performSearch"
              class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-orange-500 transition-colors"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- 搜索结果标题 -->
      <div v-if="hasSearched" class="mb-6">
        <h1 class="text-2xl font-bold text-white mb-2">
          {{ $t('search.title') }}
          <span v-if="searchQuery" class="text-orange-500">"{{ searchQuery }}"</span>
        </h1>
        <p class="text-gray-400">
          {{ $t('search.resultsCount', { count: videos.length }) }}
        </p>
      </div>

      <!-- 筛选和排序 -->
      <div v-if="hasSearched" class="flex items-center justify-between mb-6">
        <div class="flex items-center space-x-4">
          <select
            v-model="sortBy"
            @change="performSearch"
            class="bg-gray-800 text-white border border-gray-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-orange-500"
          >
            <option value="latest">{{ $t('search.sort.latest') }}</option>
            <option value="popular">{{ $t('search.sort.popular') }}</option>
            <option value="rating">{{ $t('search.sort.rating') }}</option>
            <option value="views">{{ $t('search.sort.views') }}</option>
          </select>
          
          <select
            v-model="selectedCategory"
            @change="performSearch"
            class="bg-gray-800 text-white border border-gray-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-orange-500"
          >
            <option value="">{{ $t('search.filter.allCategories') }}</option>
            <option v-for="category in categories" :key="category.id" :value="category.id">
              {{ category.name }}
            </option>
          </select>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="grid video-grid gap-5">
        <div v-for="i in 12" :key="i" class="bg-gray-800/60 rounded-xl overflow-hidden animate-pulse">
          <div class="aspect-video bg-gray-700"></div>
          <div class="p-4">
            <div class="h-4 bg-gray-700 rounded mb-2"></div>
            <div class="h-3 bg-gray-700 rounded w-2/3"></div>
          </div>
        </div>
      </div>

      <!-- 搜索结果 -->
      <div v-else-if="hasSearched && videos.length > 0" class="grid video-grid gap-5">
        <div 
          v-for="video in videos" 
          :key="video.id"
          class="group bg-gray-800/60 backdrop-blur-sm rounded-xl overflow-hidden hover:bg-gray-700/60 transition-all duration-500 cursor-pointer hover:scale-[1.03] hover:shadow-xl hover:shadow-orange-500/10"
        >
          <div class="aspect-video bg-gradient-to-br from-gray-700 via-gray-800 to-gray-900 flex items-center justify-center relative overflow-hidden">
            <!-- 图片加载组件 -->
            <div v-if="video.coverUrl" class="w-full h-full relative">
              <!-- 骨架屏 -->
              <div
                v-show="imageLoadingStates[video.id] !== false"
                class="absolute inset-0 bg-gradient-to-br from-gray-600 via-gray-700 to-gray-800 animate-pulse flex items-center justify-center"
              >
                <div class="text-center">
                  <div class="w-12 h-12 border-4 border-gray-500 border-t-orange-500 rounded-full animate-spin mb-3"></div>
                  <div class="text-gray-400 text-xs">{{ $t('search.loading') }}</div>
                </div>
              </div>

              <!-- 实际图片 -->
              <img
                :src="video.coverUrl"
                :alt="video.title"
                class="w-full h-full object-cover transition-opacity duration-300"
                :class="{ 'opacity-0': imageLoadingStates[video.id] !== false }"
                @load="handleImageLoad(video.id)"
                @error="handleImageError(video.id)"
              />
            </div>

            <!-- 无图片时的占位符 -->
            <div v-else class="w-full h-full flex items-center justify-center">
              <span class="text-white font-medium text-center p-3 text-sm leading-tight">{{ video.title }}</span>
            </div>
            
            <!-- 时长标签 -->
            <div v-if="video.duration" class="absolute bottom-2 right-2 bg-black/80 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-lg">
              {{ video.duration }}
            </div>
            <!-- 评分标签 -->
            <div v-if="video.rating" class="absolute top-2 right-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-lg font-bold">
              {{ video.rating }}
            </div>

            <!-- 播放按钮 -->
            <div class="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
              <NuxtLink
                :to="$localePath(`/play/${video.id}`)"
                class="w-14 h-14 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center hover:scale-110 transition-all duration-300 shadow-xl shadow-orange-500/50"
              >
                <svg class="w-6 h-6 text-white ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </NuxtLink>
            </div>
          </div>
          <div class="p-4">
            <h3 class="text-white font-medium text-sm mb-2 line-clamp-2 leading-tight">{{ video.title }}</h3>
            <div class="flex items-center justify-between text-xs">
              <span class="text-gray-400 truncate mr-2">{{ video.categoryName || $t('search.uncategorized') }}</span>
              <span v-if="video.rating" class="text-orange-400 font-medium">⭐ {{ video.rating }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 无搜索结果 -->
      <div v-else-if="hasSearched && videos.length === 0 && !loading" class="text-center py-16">
        <div class="text-6xl mb-4">🔍</div>
        <h2 class="text-2xl font-bold text-white mb-2">{{ $t('search.noResults.title') }}</h2>
        <p class="text-gray-400 mb-6">
          {{ $t('search.noResults.message', { query: searchQuery }) }}
        </p>
        <div class="text-sm text-gray-500">
          <p>{{ $t('search.noResults.suggestions.title') }}</p>
          <ul class="mt-2 space-y-1">
            <li>• {{ $t('search.noResults.suggestions.checkSpelling') }}</li>
            <li>• {{ $t('search.noResults.suggestions.useSimpleKeywords') }}</li>
            <li>• {{ $t('search.noResults.suggestions.browseCategories') }}</li>
          </ul>
        </div>
      </div>

      <!-- 默认状态 -->
      <div v-else-if="!hasSearched" class="text-center py-16">
        <div class="text-6xl mb-4">🎬</div>
        <h2 class="text-2xl font-bold text-white mb-2">{{ $t('search.defaultState.title') }}</h2>
        <p class="text-gray-400">{{ $t('search.defaultState.subtitle') }}</p>
      </div>
      </div>
    </main>
  </div>
</template>

<script setup>
// 多语言和页面元数据
const { t } = useI18n()

// SEO优化
const { setSearchSEO } = useSEO()

// 获取路由参数
const route = useRoute()

// 响应式数据
const searchQuery = ref(route.query.q || '')
const videos = ref([])
const categories = ref([])
const loading = ref(false)
const hasSearched = ref(false)
const sortBy = ref('latest')
const selectedCategory = ref('')

// 监听搜索查询变化，更新SEO
watch([searchQuery, videos], ([query, results]) => {
  if (query) {
    setSearchSEO(query, results)
  }
}, { immediate: true })

// 图片加载状态管理
const imageLoadingStates = ref({})

// 处理图片加载完成
const handleImageLoad = (videoId) => {
  imageLoadingStates.value[videoId] = false
}

// 处理图片加载错误
const handleImageError = (videoId) => {
  imageLoadingStates.value[videoId] = false
  console.warn(`${t('search.errors.imageLoadFailed')}: 视频ID ${videoId}`)
}

// 初始化图片加载状态
const initImageLoadingStates = (videos) => {
  videos.forEach(video => {
    if (video.coverUrl) {
      imageLoadingStates.value[video.id] = true
    }
  })
}

// 执行搜索
const performSearch = async () => {
  if (!searchQuery.value.trim()) return
  
  try {
    loading.value = true
    hasSearched.value = true
    
    const query = {
      search: searchQuery.value,
      sort: sortBy.value,
      limit: 100
    }
    
    if (selectedCategory.value) {
      query.category = selectedCategory.value
    }
    
    const { apiUser } = useApi()
    const response = await apiUser('/api/videos', {
      query
    })
    
    if (response.success) {
      videos.value = response.data.videos
      // 初始化图片加载状态
      initImageLoadingStates(response.data.videos)
    }
    
    // 更新URL
    const { $localePath } = useNuxtApp()
    await navigateTo({
      path: $localePath('/search'),
      query: { q: searchQuery.value }
    })
    
  } catch (err) {
    console.error(`${t('search.errors.searchFailed')}:`, err)
  } finally {
    loading.value = false
  }
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    const { apiUser } = useApi()
    const response = await apiUser('/api/categories')

    if (response.success) {
      categories.value = response.data
    }
  } catch (err) {
    console.error(`${t('search.errors.categoriesFailed')}:`, err)
  }
}

// 格式化观看次数
const formatViews = (views) => {
  if (views >= 1000000) {
    return (views / 1000000).toFixed(1) + 'M'
  } else if (views >= 1000) {
    return (views / 1000).toFixed(1) + 'K'
  }
  return views?.toString() || '0'
}

// 页面加载时执行
onMounted(async () => {
  await fetchCategories()
  
  // 如果URL中有搜索参数，自动执行搜索
  if (searchQuery.value) {
    await performSearch()
  }
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>

export default defineNuxtPlugin(() => {
  // 客户端启动时同步localStorage和cookie
  const { getToken, getUser, setAuth } = useAuth()
  
  // 检查localStorage中是否有认证信息
  const localToken = localStorage.getItem('admin_token')
  const localUser = localStorage.getItem('admin_user')
  
  // 检查cookie中是否有认证信息
  const cookieToken = useCookie('admin_token').value
  const cookieUser = useCookie('admin_user').value
  
  // 如果localStorage有但cookie没有，同步到cookie
  if (localToken && !cookieToken) {
    const user = localUser ? JSON.parse(localUser) : null
    if (user) {
      setAuth(localToken, user)
    }
  }
  
  // 如果cookie有但localStorage没有，同步到localStorage
  if (cookieToken && !localToken && cookieUser) {
    try {
      // 检查cookieUser是否已经是对象
      const user = typeof cookieUser === 'string' ? JSON.parse(cookieUser) : cookieUser
      const userStr = typeof cookieUser === 'string' ? cookieUser : JSON.stringify(cookieUser)

      localStorage.setItem('admin_token', cookieToken)
      localStorage.setItem('admin_user', userStr)
    } catch (error) {
      console.error('Error parsing cookie user data:', error)
      // 如果解析失败，清除有问题的cookie
      const tokenCookie = useCookie('admin_token')
      const userCookie = useCookie('admin_user')
      tokenCookie.value = null
      userCookie.value = null
    }
  }
})

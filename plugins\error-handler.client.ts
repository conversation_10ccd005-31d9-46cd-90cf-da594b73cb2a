export default defineNuxtPlugin(() => {
  // 处理未捕获的Promise错误
  window.addEventListener('unhandledrejection', (event) => {
    console.error('未捕获的Promise错误:', event.reason)

    // 检查是否是播放器相关错误
    const errorMessage = event.reason?.message || ''
    const isPlayerError = errorMessage.includes('dispose') ||
                         errorMessage.includes('destroy') ||
                         errorMessage.includes('Cannot read properties of undefined')

    if (isPlayerError) {
      event.preventDefault()
      console.warn('播放器资源清理错误已被捕获并忽略:', errorMessage)
      return
    }
  })

  // 处理全局JavaScript错误
  window.addEventListener('error', (event) => {
    console.error('全局JavaScript错误:', event.error)

    // 检查是否是播放器相关错误
    const errorMessage = event.error?.message || ''
    const isPlayerError = errorMessage.includes('dispose') ||
                         errorMessage.includes('destroy') ||
                         errorMessage.includes('Cannot read properties of undefined')

    if (isPlayerError) {
      event.preventDefault()
      console.warn('播放器资源清理错误已被捕获并忽略:', errorMessage)
      return
    }
  })

  // Vue错误处理
  const nuxtApp = useNuxtApp()

  nuxtApp.hook('vue:error', (error, context) => {
    console.error('Vue错误:', error, context)

    // 检查是否是播放器相关错误
    const errorMessage = error?.message || ''
    const isPlayerError = errorMessage.includes('dispose') ||
                         errorMessage.includes('destroy') ||
                         errorMessage.includes('Cannot read properties of undefined') ||
                         errorMessage.includes('Cannot access') ||
                         errorMessage.includes('before initialization')

    if (isPlayerError) {
      console.warn('播放器资源清理错误已被捕获并忽略:', errorMessage)
      return false // 阻止错误继续传播
    }
  })

  // 添加页面卸载前的清理
  window.addEventListener('beforeunload', () => {
    console.log('页面即将卸载，清理所有资源')
    // 这里可以添加全局清理逻辑
  })
})

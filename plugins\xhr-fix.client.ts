/**
 * 修复XMLHttpRequest responseText访问问题
 * 解决HLS.js等库在使用arraybuffer响应类型时，
 * 开发者工具或扩展程序访问responseText导致的错误
 */

// 扩展Window接口，添加自定义属性
declare global {
  interface Window {
    _xhrResponseTextFixed?: boolean
  }
}

export default defineNuxtPlugin(() => {
  if (typeof window !== 'undefined' && !window._xhrResponseTextFixed) {
    try {
      // 获取原始的responseText属性描述符
      const originalDescriptor = Object.getOwnPropertyDescriptor(XMLHttpRequest.prototype, 'responseText')
      
      if (originalDescriptor) {
        // 重写responseText属性的getter
        Object.defineProperty(XMLHttpRequest.prototype, 'responseText', {
          get: function() {
            try {
              // 如果responseType不是空字符串或'text'，返回空字符串而不是抛出错误
              if (this.responseType !== '' && this.responseType !== 'text') {
                return ''
              }
              // 否则调用原始的getter
              return originalDescriptor.get?.call(this) || ''
            } catch (error) {
              // 如果访问失败，返回空字符串而不是抛出错误
              console.debug('XMLHttpRequest responseText access intercepted:', error instanceof Error ? error.message : 'Unknown error')
              return ''
            }
          },
          configurable: true,
          enumerable: true
        })
        
        // 标记已修复，避免重复修复
        window._xhrResponseTextFixed = true
        console.debug('XMLHttpRequest responseText access fix applied')
      }
    } catch (error) {
      console.warn('Failed to apply XMLHttpRequest responseText fix:', error)
    }
  }
})

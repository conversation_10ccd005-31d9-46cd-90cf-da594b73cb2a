<svg width="400" height="200" viewBox="0 0 400 200" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f97316;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ea580c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fbbf24;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景 -->
  <rect width="400" height="200" fill="url(#bgGradient)" rx="16"/>

  <!-- 装饰性圆圈 -->
  <circle cx="350" cy="40" r="25" fill="rgba(255,255,255,0.2)" opacity="0.8"/>
  <circle cx="50" cy="160" r="18" fill="rgba(255,255,255,0.2)" opacity="0.6"/>
  <circle cx="320" cy="170" r="12" fill="rgba(255,255,255,0.2)" opacity="0.7"/>
  <circle cx="80" cy="30" r="8" fill="rgba(255,255,255,0.3)" opacity="0.5"/>

  <!-- 主标题 -->
  <text x="200" y="80" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="28" font-weight="bold">
    🎉 特别推荐
  </text>

  <!-- 副标题 -->
  <text x="200" y="110" text-anchor="middle" fill="rgba(255,255,255,0.9)" font-family="Arial, sans-serif" font-size="14" font-weight="500">
    发现更多精彩内容，立即体验！
  </text>

  <!-- 按钮背景 -->
  <rect x="130" y="130" width="140" height="40" fill="rgba(255,255,255,0.25)" rx="20" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>

  <!-- 按钮文字 -->
  <text x="200" y="155" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
    点击任意位置访问
  </text>

  <!-- 边框装饰 -->
  <rect x="2" y="2" width="396" height="196" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="14"/>
</svg>

/**
 * 管理后台构建脚本 npm run build:admin
 */

import { spawn } from 'child_process'
import path from 'path'

// 设置环境变量
process.env.BUILD_TYPE = 'admin'

console.log('🚀 开始构建管理后台...')
console.log('📦 构建类型: ADMIN (排除用户前端)')

// 执行构建命令
const buildProcess = spawn('npx', ['nuxt', 'build'], {
  stdio: 'inherit',
  shell: true,
  env: {
    ...process.env,
    BUILD_TYPE: 'admin'
  }
})

buildProcess.on('close', (code) => {
  if (code === 0) {
    console.log('✅ 管理后台构建完成！')
    console.log('📁 输出目录: .output/')
    console.log('🔒 用户前端代码已排除')
  } else {
    console.error('❌ 构建失败，退出码:', code)
    process.exit(code)
  }
})

buildProcess.on('error', (error) => {
  console.error('❌ 构建过程出错:', error)
  process.exit(1)
})

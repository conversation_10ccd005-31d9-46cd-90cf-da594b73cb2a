/**
 * 用户前端构建脚本 npm run build:user
 */

import { spawn } from 'child_process'
import path from 'path'

// 设置环境变量
process.env.BUILD_TYPE = 'user'

console.log('🚀 开始构建用户前端...')
console.log('📦 构建类型: USER (排除管理后台)')

// 设置构建环境变量，解决路径编码问题
const buildEnv = {
  ...process.env,
  BUILD_TYPE: 'user',
  // 强制使用UTF-8编码
  LANG: 'en_US.UTF-8',
  LC_ALL: 'en_US.UTF-8',
  // 禁用Git检查避免路径问题
  CI: 'true',
  // Nitro配置
  NITRO_PRESET: 'node-server',
  // 禁用一些可能导致路径问题的功能
  NUXT_TELEMETRY_DISABLED: '1'
}

console.log('🔧 环境配置完成，开始构建...')

// 执行构建命令
const buildProcess = spawn('npx', ['nuxt', 'build'], {
  stdio: 'inherit',
  shell: true,
  env: buildEnv,
  cwd: process.cwd()
})

buildProcess.on('close', (code) => {
  if (code === 0) {
    console.log('✅ 用户前端构建完成！')
    console.log('📁 输出目录: .output/')
    console.log('🔒 管理后台代码已排除')
    console.log('🚀 可以使用 npm run start:user 启动服务')

    // 检查是否有警告
    console.log('\n💡 如果看到路径编码警告，这是正常的，不影响功能')
    console.log('   建议将项目移动到英文路径以避免此警告')
  } else {
    console.error('❌ 构建失败，退出码:', code)
    console.log('\n🔧 常见解决方案:')
    console.log('   1. 将项目移动到英文路径 (推荐)')
    console.log('   2. 检查Node.js版本是否兼容')
    console.log('   3. 清理缓存: npm run clean')
    process.exit(code)
  }
})

buildProcess.on('error', (error) => {
  console.error('❌ 构建过程出错:', error.message)
  console.log('\n🔧 请检查:')
  console.log('   1. Node.js和npm是否正确安装')
  console.log('   2. 项目依赖是否完整: npm install')
  console.log('   3. 项目路径是否包含特殊字符')
  process.exit(1)
})

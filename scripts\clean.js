/**
 * 清理缓存和构建文件脚本
 */

import { rmSync, existsSync } from 'fs'
import path from 'path'

console.log('🧹 开始清理项目缓存和构建文件...')

// 需要清理的目录和文件
const cleanTargets = [
  '.output',
  '.nuxt',
  'node_modules/.cache',
  'dist',
  '.nitro',
  '.cache'
]

// 清理函数
function cleanDirectory(targetPath) {
  const fullPath = path.resolve(targetPath)
  
  if (existsSync(fullPath)) {
    try {
      rmSync(fullPath, { recursive: true, force: true })
      console.log(`✅ 已清理: ${targetPath}`)
    } catch (error) {
      console.log(`⚠️  清理失败: ${targetPath} - ${error.message}`)
    }
  } else {
    console.log(`ℹ️  不存在: ${targetPath}`)
  }
}

// 执行清理
cleanTargets.forEach(cleanDirectory)

console.log('\n🎉 清理完成！')
console.log('💡 建议执行以下命令:')
console.log('   1. npm install (重新安装依赖)')
console.log('   2. npm run build:user (重新构建)')

#!/bin/bash

# 影视CMS部署脚本
# 使用方法: ./scripts/deploy.sh [user|admin|all] [production|staging]

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "影视CMS部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [BUILD_TYPE] [ENVIRONMENT]"
    echo ""
    echo "参数:"
    echo "  BUILD_TYPE    构建类型: user | admin | all"
    echo "  ENVIRONMENT   环境: production | staging | development"
    echo ""
    echo "示例:"
    echo "  $0 user production     # 构建用户前端生产版本"
    echo "  $0 admin staging       # 构建管理后台测试版本"
    echo "  $0 all development     # 构建完整开发版本"
    echo ""
    echo "环境变量:"
    echo "  API_URL               用户前端API地址"
    echo "  ADMIN_API_URL         管理后台API地址"
    echo "  DEPLOY_HOST           部署服务器地址"
    echo "  DEPLOY_USER           部署用户名"
    echo "  DEPLOY_PATH           部署路径"
}

# 检查参数
BUILD_TYPE=${1:-"user"}
ENVIRONMENT=${2:-"production"}

if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    show_help
    exit 0
fi

# 验证构建类型
if [[ ! "$BUILD_TYPE" =~ ^(user|admin|all)$ ]]; then
    log_error "无效的构建类型: $BUILD_TYPE"
    log_info "支持的构建类型: user, admin, all"
    exit 1
fi

# 验证环境
if [[ ! "$ENVIRONMENT" =~ ^(production|staging|development)$ ]]; then
    log_error "无效的环境: $ENVIRONMENT"
    log_info "支持的环境: production, staging, development"
    exit 1
fi

log_info "开始部署流程..."
log_info "构建类型: $BUILD_TYPE"
log_info "环境: $ENVIRONMENT"

# 检查必要的工具
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 设置环境变量
setup_environment() {
    log_info "设置环境变量..."
    
    export NODE_ENV=$ENVIRONMENT
    export BUILD_TYPE=$BUILD_TYPE
    
    case $ENVIRONMENT in
        production)
            export API_URL=${API_URL:-"https://api.yourdomain.com"}
            export ADMIN_API_URL=${ADMIN_API_URL:-"https://admin-api.yourdomain.com"}
            ;;
        staging)
            export API_URL=${API_URL:-"https://staging-api.yourdomain.com"}
            export ADMIN_API_URL=${ADMIN_API_URL:-"https://staging-admin-api.yourdomain.com"}
            ;;
        development)
            export API_URL=${API_URL:-"http://localhost:3001"}
            export ADMIN_API_URL=${ADMIN_API_URL:-"http://localhost:3001"}
            ;;
    esac
    
    log_success "环境变量设置完成"
    log_info "API_URL: $API_URL"
    log_info "ADMIN_API_URL: $ADMIN_API_URL"
}

# 安装依赖
install_dependencies() {
    log_info "安装依赖..."
    
    if [[ ! -d "node_modules" ]]; then
        npm ci
    else
        log_info "依赖已存在，跳过安装"
    fi
    
    log_success "依赖安装完成"
}

# 构建项目
build_project() {
    log_info "开始构建项目..."
    
    case $BUILD_TYPE in
        user)
            npm run build:user
            npm run verify:user
            ;;
        admin)
            npm run build:admin
            npm run verify:admin
            ;;
        all)
            npm run build:all
            ;;
    esac
    
    log_success "项目构建完成"
}

# 创建部署包
create_deployment_package() {
    log_info "创建部署包..."
    
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    PACKAGE_NAME="${BUILD_TYPE}_${ENVIRONMENT}_${TIMESTAMP}.tar.gz"
    
    # 创建临时目录
    TEMP_DIR="temp_deploy_$$"
    mkdir -p $TEMP_DIR
    
    # 复制构建文件
    cp -r .output/* $TEMP_DIR/
    
    # 复制配置文件
    if [[ -f ".env.${ENVIRONMENT}" ]]; then
        cp .env.${ENVIRONMENT} $TEMP_DIR/.env
    fi
    
    # 创建启动脚本
    cat > $TEMP_DIR/start.sh << EOF
#!/bin/bash
# 自动生成的启动脚本

export NODE_ENV=$ENVIRONMENT
export BUILD_TYPE=$BUILD_TYPE

# 启动服务
node server/index.mjs
EOF
    
    chmod +x $TEMP_DIR/start.sh
    
    # 打包
    tar -czf $PACKAGE_NAME -C $TEMP_DIR .
    
    # 清理临时目录
    rm -rf $TEMP_DIR
    
    log_success "部署包创建完成: $PACKAGE_NAME"
    echo "PACKAGE_NAME=$PACKAGE_NAME" > .deploy_info
}

# 部署到服务器 (可选)
deploy_to_server() {
    if [[ -z "$DEPLOY_HOST" || -z "$DEPLOY_USER" || -z "$DEPLOY_PATH" ]]; then
        log_warning "未配置部署服务器信息，跳过自动部署"
        log_info "如需自动部署，请设置以下环境变量:"
        log_info "  DEPLOY_HOST - 服务器地址"
        log_info "  DEPLOY_USER - 用户名"
        log_info "  DEPLOY_PATH - 部署路径"
        return
    fi
    
    log_info "部署到服务器: $DEPLOY_HOST"
    
    # 读取包名
    source .deploy_info
    
    # 上传文件
    scp $PACKAGE_NAME $DEPLOY_USER@$DEPLOY_HOST:/tmp/
    
    # 远程部署
    ssh $DEPLOY_USER@$DEPLOY_HOST << EOF
        set -e
        
        # 备份当前版本
        if [[ -d "$DEPLOY_PATH" ]]; then
            sudo cp -r $DEPLOY_PATH ${DEPLOY_PATH}_backup_$(date +%Y%m%d_%H%M%S)
        fi
        
        # 创建部署目录
        sudo mkdir -p $DEPLOY_PATH
        
        # 解压新版本
        cd $DEPLOY_PATH
        sudo tar -xzf /tmp/$PACKAGE_NAME
        
        # 设置权限
        sudo chown -R $DEPLOY_USER:$DEPLOY_USER $DEPLOY_PATH
        
        # 重启服务 (如果使用PM2)
        if command -v pm2 &> /dev/null; then
            pm2 restart ${BUILD_TYPE}-frontend || pm2 start start.sh --name ${BUILD_TYPE}-frontend
        fi
        
        # 清理临时文件
        rm -f /tmp/$PACKAGE_NAME
        
        echo "部署完成"
EOF
    
    log_success "服务器部署完成"
}

# 清理构建文件
cleanup() {
    log_info "清理构建文件..."
    
    if [[ -f ".deploy_info" ]]; then
        source .deploy_info
        if [[ -f "$PACKAGE_NAME" ]]; then
            rm -f $PACKAGE_NAME
        fi
        rm -f .deploy_info
    fi
    
    log_success "清理完成"
}

# 主流程
main() {
    log_info "=========================================="
    log_info "影视CMS自动部署脚本"
    log_info "=========================================="
    
    check_dependencies
    setup_environment
    install_dependencies
    build_project
    create_deployment_package
    deploy_to_server
    
    log_success "=========================================="
    log_success "部署流程完成！"
    log_success "=========================================="
    
    # 显示部署信息
    echo ""
    log_info "部署信息:"
    log_info "  构建类型: $BUILD_TYPE"
    log_info "  环境: $ENVIRONMENT"
    log_info "  API地址: $API_URL"
    
    if [[ "$BUILD_TYPE" == "admin" ]]; then
        log_info "  管理API: $ADMIN_API_URL"
    fi
    
    echo ""
    log_info "启动命令:"
    log_info "  node .output/server/index.mjs"
    echo ""
    log_info "或使用PM2:"
    log_info "  pm2 start .output/server/index.mjs --name ${BUILD_TYPE}-frontend"
}

# 错误处理
trap cleanup EXIT

# 执行主流程
main

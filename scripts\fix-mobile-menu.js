/**
 * 移动端菜单问题修复脚本
 * 检测和修复常见的移动端菜单问题
 */

const fs = require('fs')
const path = require('path')

console.log('🔧 移动端菜单问题诊断和修复工具\n')

// 检查布局文件
function checkLayoutFile() {
  const layoutPath = path.resolve('layouts/default.vue')
  
  if (!fs.existsSync(layoutPath)) {
    console.log('❌ 布局文件不存在:', layoutPath)
    return false
  }
  
  const content = fs.readFileSync(layoutPath, 'utf8')
  
  console.log('📋 检查布局文件...')
  
  // 检查关键元素
  const checks = [
    {
      name: '移动端菜单按钮',
      pattern: /id="mobile-menu-button"/,
      required: true
    },
    {
      name: '触摸事件处理',
      pattern: /@touchstart="handleTouchStart"/,
      required: true
    },
    {
      name: '菜单切换函数',
      pattern: /const toggleMobileMenu/,
      required: true
    },
    {
      name: '点击外部关闭逻辑',
      pattern: /handleClickOutside/,
      required: true
    },
    {
      name: '移动端菜单容器',
      pattern: /id="mobile-menu"/,
      required: true
    },
    {
      name: 'z-index设置',
      pattern: /z-40|z-50/,
      required: true
    }
  ]
  
  let allPassed = true
  
  checks.forEach(check => {
    const found = check.pattern.test(content)
    const status = found ? '✅' : '❌'
    console.log(`   ${status} ${check.name}`)
    
    if (check.required && !found) {
      allPassed = false
    }
  })
  
  return allPassed
}

// 生成修复建议
function generateFixSuggestions() {
  console.log('\n💡 移动端菜单修复建议:\n')
  
  console.log('1. **CSS修复**:')
  console.log('   - 确保按钮有足够的点击区域 (padding: 8px)')
  console.log('   - 添加 touch-action: manipulation')
  console.log('   - 设置正确的z-index层级')
  console.log('')
  
  console.log('2. **JavaScript修复**:')
  console.log('   - 添加触摸事件处理 (@touchstart)')
  console.log('   - 改进点击外部关闭逻辑')
  console.log('   - 添加事件防抖和阻止冒泡')
  console.log('')
  
  console.log('3. **HTML修复**:')
  console.log('   - 添加正确的ARIA属性')
  console.log('   - 使用语义化的ID和类名')
  console.log('   - 确保按钮可访问性')
  console.log('')
  
  console.log('4. **测试方法**:')
  console.log('   - 访问 /mobile-menu-test 页面进行测试')
  console.log('   - 在真实移动设备上测试')
  console.log('   - 检查浏览器开发者工具的控制台')
  console.log('')
}

// 生成移动端优化的CSS
function generateMobileCSS() {
  console.log('📱 移动端优化CSS:\n')
  
  const css = `
/* 移动端菜单优化 */
@media (max-width: 768px) {
  /* 确保按钮可点击 */
  #mobile-menu-button,
  #mobile-search-button {
    min-width: 44px;
    min-height: 44px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    position: relative;
    z-index: 60;
  }
  
  /* 菜单容器 */
  #mobile-menu {
    position: relative;
    z-index: 50;
    transform: translateZ(0); /* 启用硬件加速 */
  }
  
  /* 防止滚动穿透 */
  body.menu-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
  }
  
  /* 菜单项优化 */
  #mobile-menu a {
    min-height: 48px;
    display: flex;
    align-items: center;
    touch-action: manipulation;
  }
}

/* 通用触摸优化 */
button, a {
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}
`
  
  console.log(css)
}

// 生成JavaScript修复代码
function generateJavaScriptFix() {
  console.log('🔧 JavaScript修复代码:\n')
  
  const js = `
// 移动端菜单修复
const fixMobileMenu = () => {
  // 防抖函数
  const debounce = (func, wait) => {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  }
  
  // 改进的菜单切换
  const toggleMobileMenu = debounce((event) => {
    if (event) {
      event.preventDefault()
      event.stopPropagation()
    }
    
    const menu = document.getElementById('mobile-menu')
    const button = document.getElementById('mobile-menu-button')
    
    if (menu && button) {
      const isOpen = menu.style.display !== 'none'
      menu.style.display = isOpen ? 'none' : 'block'
      button.setAttribute('aria-expanded', !isOpen)
      
      // 防止滚动穿透
      document.body.classList.toggle('menu-open', !isOpen)
    }
  }, 100)
  
  // 绑定事件
  const menuButton = document.getElementById('mobile-menu-button')
  if (menuButton) {
    menuButton.addEventListener('click', toggleMobileMenu)
    menuButton.addEventListener('touchstart', (e) => {
      e.stopPropagation()
    })
  }
}

// 页面加载后执行修复
if (typeof window !== 'undefined') {
  document.addEventListener('DOMContentLoaded', fixMobileMenu)
}
`
  
  console.log(js)
}

// 主函数
function main() {
  const layoutOK = checkLayoutFile()
  
  if (layoutOK) {
    console.log('\n✅ 布局文件检查通过')
  } else {
    console.log('\n❌ 布局文件存在问题')
  }
  
  generateFixSuggestions()
  generateMobileCSS()
  generateJavaScriptFix()
  
  console.log('🎯 快速测试方法:')
  console.log('1. 启动开发服务器: npm run dev')
  console.log('2. 在手机上访问: http://your-ip:3000/mobile-menu-test')
  console.log('3. 测试菜单按钮是否可以正常点击')
  console.log('4. 检查浏览器控制台是否有错误信息')
  console.log('')
  console.log('📱 如果问题仍然存在:')
  console.log('- 检查CSS是否正确加载')
  console.log('- 确认JavaScript没有错误')
  console.log('- 在不同浏览器中测试')
  console.log('- 检查是否有其他CSS或JS冲突')
}

main()

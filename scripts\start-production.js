/**
 * 生产环境启动脚本
 * 用于启动构建后的服务，支持端口配置
 */

import { spawn } from 'child_process'
import { siteConfig, getServerPort, getServerHost } from '../config/site.js'
import fs from 'fs'
import path from 'path'

// 获取命令行参数
const args = process.argv.slice(2)
const buildType = args[0] || 'user' // user | admin
const customPort = args[1] // 可选的自定义端口

// 检查构建文件是否存在
const outputPath = '.output/server/index.mjs'
if (!fs.existsSync(outputPath)) {
  console.error('❌ 构建文件不存在，请先运行构建命令:')
  if (buildType === 'admin') {
    console.error('   npm run build:admin')
  } else {
    console.error('   npm run build:user')
  }
  process.exit(1)
}

// 设置环境变量
const env = {
  ...process.env,
  NODE_ENV: 'production'
}

// 设置端口
const finalPort = customPort || getServerPort(buildType)
const host = getServerHost()

// 设置Nitro端口环境变量
env.NITRO_PORT = finalPort
env.NITRO_HOST = host

console.log('🚀 启动生产环境服务...')
console.log(`📦 构建类型: ${buildType.toUpperCase()}`)
console.log(`🌐 服务地址: http://${host}:${finalPort}`)
console.log(`🔧 API地址: http://localhost:${getServerPort('api')}`)

if (buildType === 'admin') {
  console.log('🔒 管理后台模式')
} else {
  console.log('👥 用户前端模式')
}

console.log('─'.repeat(60))

// 启动生产服务器
const startProcess = spawn('node', [outputPath], {
  stdio: 'inherit',
  shell: true,
  env: env
})

startProcess.on('close', (code) => {
  if (code === 0) {
    console.log('✅ 服务关闭完成')
  } else {
    console.error('❌ 服务异常关闭，退出码:', code)
    process.exit(code)
  }
})

startProcess.on('error', (error) => {
  console.error('❌ 启动过程出错:', error)
  process.exit(1)
})

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务...')
  startProcess.kill('SIGINT')
})

process.on('SIGTERM', () => {
  console.log('\n🛑 正在关闭服务...')
  startProcess.kill('SIGTERM')
})

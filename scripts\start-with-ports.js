/**
 * 带端口配置的启动脚本
 * 用于启动前台或后台服务，支持自定义端口
 */

import { spawn } from 'child_process'
import { siteConfig, getServerPort, getServerHost, getServerUrl } from '../config/site.js'

// 获取命令行参数
const args = process.argv.slice(2)
const buildType = args[0] || 'user' // user | admin
const customPort = args[1] // 可选的自定义端口

// 设置环境变量
const env = {
  ...process.env,
  BUILD_TYPE: buildType
}

// 如果指定了自定义端口，设置相应的环境变量
if (customPort) {
  if (buildType === 'admin') {
    env.ADMIN_PORT = customPort
  } else {
    env.USER_PORT = customPort
  }
}

// 获取最终端口
const finalPort = customPort || getServerPort(buildType)
const host = getServerHost()

console.log('🚀 启动Nuxt.js影视CMS服务...')
console.log(`📦 构建类型: ${buildType.toUpperCase()}`)
console.log(`🌐 服务地址: http://${host}:${finalPort}`)
console.log(`🔧 API地址: http://localhost:${getServerPort('api')}`)

if (buildType === 'admin') {
  console.log('🔒 管理后台模式 - 用户前端代码已排除')
} else {
  console.log('👥 用户前端模式 - 管理后台代码已排除')
}

console.log('─'.repeat(60))

// 启动开发服务器
const startProcess = spawn('npx', ['nuxt', 'dev'], {
  stdio: 'inherit',
  shell: true,
  env: env
})

startProcess.on('close', (code) => {
  if (code === 0) {
    console.log('✅ 服务启动完成！')
  } else {
    console.error('❌ 服务启动失败，退出码:', code)
    process.exit(code)
  }
})

startProcess.on('error', (error) => {
  console.error('❌ 启动过程出错:', error)
  process.exit(1)
})

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务...')
  startProcess.kill('SIGINT')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n🛑 正在关闭服务...')
  startProcess.kill('SIGTERM')
  process.exit(0)
})

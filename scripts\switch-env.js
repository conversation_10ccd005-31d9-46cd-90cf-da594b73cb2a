/**
 * 环境切换脚本
 * 在开发环境和生产环境之间切换配置
 */

const fs = require('fs')
const path = require('path')

const ENV_CONFIGS = {
  development: {
    frontend: {
      'NODE_ENV': 'development',
      'API_URL': 'http://localhost:3001',
      'ADMIN_API_URL': 'http://localhost:3001',
      'BUILD_TYPE': 'all',
      'NUXT_PUBLIC_SITE_URL': 'http://localhost:3000',
      'SITE_URL': 'http://localhost:3000'
    },
    backend: {
      'NODE_ENV': 'development',
      'PORT': '3001',
      'HOST': '0.0.0.0',
      'SITE_URL': 'http://localhost:3000'
    }
  },
  production: {
    frontend: {
      'NODE_ENV': 'production',
      'API_URL': 'https://api.91jspg.com',
      'ADMIN_API_URL': 'https://api.91jspg.com',
      'BUILD_TYPE': 'user',
      'NUXT_PUBLIC_SITE_URL': 'https://91jspg.com',
      'SITE_URL': 'https://91jspg.com'
    },
    backend: {
      'NODE_ENV': 'production',
      'PORT': '3001',
      'HOST': '0.0.0.0',
      'SITE_URL': 'https://91jspg.com'
    }
  }
}

function updateEnvFile(filePath, config) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  文件不存在: ${filePath}`)
    return false
  }

  let content = fs.readFileSync(filePath, 'utf8')
  
  // 更新配置
  Object.entries(config).forEach(([key, value]) => {
    const regex = new RegExp(`^${key}=.*$`, 'm')
    if (content.match(regex)) {
      content = content.replace(regex, `${key}=${value}`)
    } else {
      content += `\n${key}=${value}`
    }
  })
  
  fs.writeFileSync(filePath, content)
  return true
}

function switchEnvironment(targetEnv) {
  if (!ENV_CONFIGS[targetEnv]) {
    console.error(`❌ 无效的环境: ${targetEnv}`)
    console.log('可用环境: development, production')
    return
  }
  
  console.log(`🔄 切换到 ${targetEnv} 环境...`)
  
  // 更新前端 .env
  const frontendEnvPath = path.resolve('.env')
  if (updateEnvFile(frontendEnvPath, ENV_CONFIGS[targetEnv].frontend)) {
    console.log('✅ 前端环境配置已更新')
  }
  
  // 更新后端 .env
  const backendEnvPath = path.resolve('backend/.env')
  if (updateEnvFile(backendEnvPath, ENV_CONFIGS[targetEnv].backend)) {
    console.log('✅ 后端环境配置已更新')
  }
  
  console.log(`\n🎉 环境切换完成！当前环境: ${targetEnv}`)
  
  if (targetEnv === 'development') {
    console.log('\n💡 开发环境特点:')
    console.log('   - API保护相对宽松，便于调试')
    console.log('   - 允许直接浏览器访问API')
    console.log('   - 详细的访问日志')
  } else if (targetEnv === 'production') {
    console.log('\n🛡️  生产环境特点:')
    console.log('   - 严格的API保护')
    console.log('   - 必须有有效的Referer才能访问')
    console.log('   - 阻止直接API访问')
  }
  
  console.log('\n🔄 请重启服务以应用新配置:')
  console.log('   前端: npm run dev')
  console.log('   后端: cd backend && npm run dev')
}

// 获取命令行参数
const targetEnv = process.argv[2]

if (!targetEnv) {
  console.log('🔧 环境切换工具')
  console.log('\n用法:')
  console.log('  npm run env:dev     # 切换到开发环境')
  console.log('  npm run env:prod    # 切换到生产环境')
  console.log('\n或者:')
  console.log('  node scripts/switch-env.js development')
  console.log('  node scripts/switch-env.js production')
} else {
  switchEnvironment(targetEnv)
}

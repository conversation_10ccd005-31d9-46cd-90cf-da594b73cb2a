/**
 * API保护测试脚本
 * 测试直播API的保护措施是否生效
 */

const axios = require('axios')

const API_BASE = 'http://localhost:3001'
const TEST_ENDPOINTS = [
  '/api/live/platforms',
  '/api/live/hongzhuang/rooms',
  '/api/proxy/stats'
]

// 测试无Referer的直接访问（应该被拒绝）
async function testDirectAccess() {
  console.log('🔍 测试直接访问（无Referer）...')
  
  for (const endpoint of TEST_ENDPOINTS) {
    try {
      const response = await axios.get(`${API_BASE}${endpoint}`, {
        timeout: 5000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      })
      console.log(`❌ ${endpoint}: 应该被拒绝但成功访问 (${response.status})`)
    } catch (error) {
      if (error.response?.status === 403) {
        console.log(`✅ ${endpoint}: 正确拒绝访问 (403)`)
      } else {
        console.log(`⚠️  ${endpoint}: 意外错误 (${error.response?.status || error.message})`)
      }
    }
  }
}

// 测试有效Referer的访问（应该被允许）
async function testValidReferer() {
  console.log('\n🔍 测试有效Referer访问...')
  
  const validReferers = [
    'https://91jspg.com',
    'https://www.91jspg.com'
  ]
  
  for (const referer of validReferers) {
    for (const endpoint of TEST_ENDPOINTS) {
      try {
        const response = await axios.get(`${API_BASE}${endpoint}`, {
          timeout: 5000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': referer
          }
        })
        console.log(`✅ ${endpoint} (${referer}): 正确允许访问 (${response.status})`)
      } catch (error) {
        console.log(`❌ ${endpoint} (${referer}): 应该被允许但被拒绝 (${error.response?.status || error.message})`)
      }
    }
  }
}

// 测试API工具访问（应该被拒绝）
async function testAPITools() {
  console.log('\n🔍 测试API工具访问...')
  
  const suspiciousUserAgents = [
    'PostmanRuntime/7.28.4',
    'curl/7.68.0',
    'python-requests/2.25.1',
    'axios/0.21.1'
  ]
  
  for (const userAgent of suspiciousUserAgents) {
    try {
      const response = await axios.get(`${API_BASE}${TEST_ENDPOINTS[0]}`, {
        timeout: 5000,
        headers: {
          'User-Agent': userAgent
        }
      })
      console.log(`❌ ${userAgent}: 应该被拒绝但成功访问 (${response.status})`)
    } catch (error) {
      if (error.response?.status === 403) {
        console.log(`✅ ${userAgent}: 正确拒绝访问 (403)`)
      } else {
        console.log(`⚠️  ${userAgent}: 意外错误 (${error.response?.status || error.message})`)
      }
    }
  }
}

// 测试无效Referer访问（应该被拒绝）
async function testInvalidReferer() {
  console.log('\n🔍 测试无效Referer访问...')
  
  const invalidReferers = [
    'https://evil.com',
    'https://attacker.com',
    'http://localhost:8080'
  ]
  
  for (const referer of invalidReferers) {
    try {
      const response = await axios.get(`${API_BASE}${TEST_ENDPOINTS[0]}`, {
        timeout: 5000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Referer': referer
        }
      })
      console.log(`❌ ${referer}: 应该被拒绝但成功访问 (${response.status})`)
    } catch (error) {
      if (error.response?.status === 403) {
        console.log(`✅ ${referer}: 正确拒绝访问 (403)`)
      } else {
        console.log(`⚠️  ${referer}: 意外错误 (${error.response?.status || error.message})`)
      }
    }
  }
}

// 主测试函数
async function runProtectionTests() {
  console.log('🛡️  开始API保护测试...\n')
  
  try {
    await testDirectAccess()
    await testValidReferer()
    await testAPITools()
    await testInvalidReferer()
    
    console.log('\n🎉 API保护测试完成！')
    console.log('\n💡 说明:')
    console.log('   ✅ = 保护措施正常工作')
    console.log('   ❌ = 保护措施可能有问题')
    console.log('   ⚠️  = 需要进一步检查')
    
  } catch (error) {
    console.error('❌ 测试运行失败:', error.message)
  }
}

// 运行测试
runProtectionTests()

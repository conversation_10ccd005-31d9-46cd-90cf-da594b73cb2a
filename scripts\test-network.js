/**
 * 网络连接测试脚本
 * 用于测试前后端连接是否正常
 */

const axios = require('axios')

// 测试配置
const TEST_CONFIG = {
  frontend: 'http://localhost:3000',
  backend: 'http://localhost:3001',
  timeout: 10000
}

// 测试前端连接
async function testFrontend() {
  try {
    console.log('🔍 测试前端连接...')
    const response = await axios.get(TEST_CONFIG.frontend, {
      timeout: TEST_CONFIG.timeout
    })
    console.log('✅ 前端连接正常:', response.status)
    return true
  } catch (error) {
    console.error('❌ 前端连接失败:', error.message)
    return false
  }
}

// 测试后端连接
async function testBackend() {
  try {
    console.log('🔍 测试后端连接...')
    const response = await axios.get(`${TEST_CONFIG.backend}/api`, {
      timeout: TEST_CONFIG.timeout
    })
    console.log('✅ 后端连接正常:', response.status)
    return true
  } catch (error) {
    console.error('❌ 后端连接失败:', error.message)
    return false
  }
}

// 测试直播API
async function testLiveAPI() {
  try {
    console.log('🔍 测试直播API...')
    const response = await axios.get(`${TEST_CONFIG.backend}/api/live/platforms`, {
      timeout: TEST_CONFIG.timeout
    })
    console.log('✅ 直播API正常:', response.status)
    return true
  } catch (error) {
    console.error('❌ 直播API失败:', error.message)
    return false
  }
}

// 测试代理API
async function testProxyAPI() {
  try {
    console.log('🔍 测试代理API状态...')
    const response = await axios.get(`${TEST_CONFIG.backend}/api/proxy/stats`, {
      timeout: TEST_CONFIG.timeout
    })
    console.log('✅ 代理API正常:', response.status)
    return true
  } catch (error) {
    console.error('❌ 代理API失败:', error.message)
    return false
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始网络连接测试...\n')
  
  const results = {
    frontend: await testFrontend(),
    backend: await testBackend(),
    liveAPI: await testLiveAPI(),
    proxyAPI: await testProxyAPI()
  }
  
  console.log('\n📊 测试结果汇总:')
  console.log('前端服务:', results.frontend ? '✅ 正常' : '❌ 异常')
  console.log('后端服务:', results.backend ? '✅ 正常' : '❌ 异常')
  console.log('直播API:', results.liveAPI ? '✅ 正常' : '❌ 异常')
  console.log('代理API:', results.proxyAPI ? '✅ 正常' : '❌ 异常')
  
  const allPassed = Object.values(results).every(result => result)
  
  if (allPassed) {
    console.log('\n🎉 所有测试通过！网络连接正常')
  } else {
    console.log('\n⚠️  部分测试失败，请检查相关服务')
    console.log('\n💡 解决建议:')
    if (!results.frontend) console.log('   - 启动前端: npm run dev')
    if (!results.backend) console.log('   - 启动后端: cd backend && npm run dev')
    if (!results.liveAPI) console.log('   - 检查后端直播API配置')
    if (!results.proxyAPI) console.log('   - 检查后端代理API配置')
  }
  
  process.exit(allPassed ? 0 : 1)
}

// 运行测试
runTests().catch(error => {
  console.error('❌ 测试运行失败:', error)
  process.exit(1)
})

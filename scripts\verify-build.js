/**
 * 构建验证脚本
 * 验证分离构建是否正确排除了相应的文件
 */

import fs from 'fs'
import path from 'path'

const buildType = process.argv[2] || 'user'

console.log(`🔍 验证 ${buildType.toUpperCase()} 构建结果...`)

// 检查 .output 目录是否存在
const outputDir = '.output'
if (!fs.existsSync(outputDir)) {
  console.error('❌ .output 目录不存在，请先运行构建命令')
  process.exit(1)
}

// 读取构建的服务器文件
const serverDir = path.join(outputDir, 'server')
const chunksDir = path.join(serverDir, 'chunks', 'build')

if (!fs.existsSync(chunksDir)) {
  console.error('❌ 构建文件不存在')
  process.exit(1)
}

// 获取所有构建文件
const buildFiles = fs.readdirSync(chunksDir)
console.log(`📁 找到 ${buildFiles.length} 个构建文件`)

// 定义检查规则
const checkRules = {
  user: {
    shouldNotContain: [
      'admin',      // 不应包含管理后台相关文件
      'useAuth',    // 不应包含认证组合函数
      'login'       // 不应包含登录相关文件
    ],
    shouldContain: [
      'index',      // 应包含首页
      'search',     // 应包含搜索页面
      'rankings',   // 应包含排行榜页面
      'default'     // 应包含默认布局
    ]
  },
  admin: {
    shouldNotContain: [
      'search',     // 不应包含搜索页面
      'rankings',   // 不应包含排行榜页面
      'default'     // 不应包含默认布局
    ],
    shouldContain: [
      'admin',      // 应包含管理后台文件
      'auth',       // 应包含认证相关文件
      'login'       // 应包含登录相关文件
    ]
  }
}

const rules = checkRules[buildType]
if (!rules) {
  console.error('❌ 无效的构建类型:', buildType)
  process.exit(1)
}

let passed = true

// 检查不应包含的文件
console.log('\n🚫 检查排除的文件...')
rules.shouldNotContain.forEach(keyword => {
  const found = buildFiles.some(file => file.toLowerCase().includes(keyword.toLowerCase()))
  if (found) {
    console.log(`❌ 发现不应存在的文件: ${keyword}`)
    passed = false
  } else {
    console.log(`✅ 正确排除: ${keyword}`)
  }
})

// 检查应包含的文件
console.log('\n✅ 检查必需的文件...')
rules.shouldContain.forEach(keyword => {
  const found = buildFiles.some(file => file.toLowerCase().includes(keyword.toLowerCase()))
  if (found) {
    console.log(`✅ 正确包含: ${keyword}`)
  } else {
    console.log(`❌ 缺少必需文件: ${keyword}`)
    passed = false
  }
})

// 显示文件大小信息
console.log('\n📊 构建统计:')
const totalSize = buildFiles.reduce((total, file) => {
  const filePath = path.join(chunksDir, file)
  const stats = fs.statSync(filePath)
  return total + stats.size
}, 0)

console.log(`📁 文件数量: ${buildFiles.length}`)
console.log(`📦 总大小: ${(totalSize / 1024 / 1024).toFixed(2)} MB`)

// 最终结果
console.log('\n' + '='.repeat(50))
if (passed) {
  console.log(`🎉 ${buildType.toUpperCase()} 构建验证通过！`)
  console.log('✅ 所有文件都正确包含/排除')
} else {
  console.log(`❌ ${buildType.toUpperCase()} 构建验证失败！`)
  console.log('⚠️  请检查构建配置')
  process.exit(1)
}

console.log('='.repeat(50))

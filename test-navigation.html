<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-link {
            display: inline-block;
            padding: 10px 20px;
            margin: 10px;
            background-color: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .test-link:hover {
            background-color: #2563eb;
        }
        .test-button {
            display: inline-block;
            padding: 10px 20px;
            margin: 10px;
            background-color: #10b981;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .test-button:hover {
            background-color: #059669;
        }
        .info {
            background-color: #374151;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .step {
            margin: 15px 0;
            padding: 10px;
            background-color: #4b5563;
            border-radius: 5px;
        }
        .console-output {
            background-color: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 导航功能测试</h1>
        
        <div class="info">
            <h2>📋 测试目的</h2>
            <p>测试任务管理页面的"查看详情"按钮是否正常工作。</p>
        </div>

        <div class="step">
            <h3>步骤 1: 直接访问页面</h3>
            <p>点击下面的链接直接访问各个页面：</p>
            
            <a href="http://localhost:3000/admin/collect/tasks" class="test-link" target="_blank">
                📋 任务管理页面
            </a>
            
            <a href="http://localhost:3000/admin/collect/tasks/13" class="test-link" target="_blank">
                📄 任务 13 详情页面
            </a>
            
            <a href="http://localhost:3000/admin/collect/tasks/12" class="test-link" target="_blank">
                📄 任务 12 详情页面
            </a>
        </div>

        <div class="step">
            <h3>步骤 2: 测试JavaScript导航</h3>
            <p>测试不同的JavaScript导航方法：</p>
            
            <button class="test-button" onclick="testWindowLocation()">
                🌐 测试 window.location
            </button>
            
            <button class="test-button" onclick="testWindowOpen()">
                🔗 测试 window.open
            </button>
            
            <button class="test-button" onclick="testHistoryPush()">
                📚 测试 history.pushState
            </button>
        </div>

        <div class="step">
            <h3>步骤 3: 检查控制台</h3>
            <p>打开浏览器开发者工具，查看控制台输出：</p>
            <div id="console-output" class="console-output">
控制台输出将显示在这里...
            </div>
        </div>

        <div class="step">
            <h3>步骤 4: 诊断问题</h3>
            <p>可能的问题原因：</p>
            <ul>
                <li>✅ JavaScript错误阻止了函数执行</li>
                <li>✅ 事件冒泡问题</li>
                <li>✅ Vue.js组件渲染问题</li>
                <li>✅ 路由配置问题</li>
                <li>✅ 认证或权限问题</li>
            </ul>
        </div>

        <div class="info">
            <h2>🔍 调试建议</h2>
            <ol>
                <li>在任务管理页面按F12打开开发者工具</li>
                <li>切换到Console标签</li>
                <li>点击"🧪 测试"按钮，查看是否有输出</li>
                <li>点击"查看详情"按钮，查看是否有错误</li>
                <li>检查Network标签是否有API请求失败</li>
            </ol>
        </div>
    </div>

    <script>
        // 重定向console.log到页面
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            consoleOutput.textContent += '[LOG] ' + args.join(' ') + '\n';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            consoleOutput.textContent += '[ERROR] ' + args.join(' ') + '\n';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };

        function testWindowLocation() {
            console.log('🌐 测试 window.location 跳转...');
            try {
                window.location.href = 'http://localhost:3000/admin/collect/tasks/13';
                console.log('✅ window.location 调用成功');
            } catch (error) {
                console.error('❌ window.location 调用失败:', error);
            }
        }

        function testWindowOpen() {
            console.log('🔗 测试 window.open 跳转...');
            try {
                window.open('http://localhost:3000/admin/collect/tasks/13', '_blank');
                console.log('✅ window.open 调用成功');
            } catch (error) {
                console.error('❌ window.open 调用失败:', error);
            }
        }

        function testHistoryPush() {
            console.log('📚 测试 history.pushState...');
            try {
                history.pushState({}, '', '/admin/collect/tasks/13');
                console.log('✅ history.pushState 调用成功');
                console.log('⚠️ 注意：这只会改变URL，不会实际导航');
            } catch (error) {
                console.error('❌ history.pushState 调用失败:', error);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 导航测试页面已加载');
            console.log('📍 当前时间:', new Date().toLocaleString());
            
            // 测试基本的JavaScript功能
            console.log('🧪 测试基本JavaScript功能...');
            console.log('✅ console.log 正常工作');
            
            // 测试DOM操作
            const testElement = document.createElement('div');
            testElement.textContent = 'Test';
            console.log('✅ DOM操作正常工作');
            
            // 测试异步功能
            setTimeout(() => {
                console.log('✅ setTimeout 正常工作');
            }, 1000);
        });
    </script>
</body>
</html>

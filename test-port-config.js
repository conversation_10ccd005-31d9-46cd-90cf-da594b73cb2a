#!/usr/bin/env node
/**
 * 测试端口配置
 */

import { siteConfig, getServerPort, getServerHost, getServerUrl } from './config/site.js'

console.log('🧪 测试端口配置')
console.log('=' * 60)

// 测试默认配置
console.log('📋 默认端口配置:')
console.log(`用户前端端口: ${getServerPort('user')}`)
console.log(`管理后台端口: ${getServerPort('admin')}`)
console.log(`后端API端口: ${getServerPort('api')}`)
console.log(`服务器主机: ${getServerHost()}`)

console.log('\n🌐 服务器地址:')
console.log(`用户前端: ${getServerUrl('user')}`)
console.log(`管理后台: ${getServerUrl('admin')}`)
console.log(`后端API: ${getServerUrl('api')}`)

// 测试环境变量覆盖
console.log('\n🔧 环境变量测试:')
console.log('模拟设置环境变量: USER_PORT=4000, ADMIN_PORT=4002, API_PORT=4001')
console.log('注意: 在实际使用中，请在启动前设置环境变量，例如:')
console.log('USER_PORT=4000 ADMIN_PORT=4002 npm run dev:user')

console.log('\n✅ 端口配置测试完成!')
console.log('\n📖 使用说明:')
console.log('1. 开发环境:')
console.log('   npm run dev:user    # 用户前端 (默认端口 3000)')
console.log('   npm run dev:admin   # 管理后台 (默认端口 3002)')
console.log('')
console.log('2. 生产环境:')
console.log('   npm run build:user && npm run start:user')
console.log('   npm run build:admin && npm run start:admin')
console.log('')
console.log('3. 自定义端口:')
console.log('   node scripts/start-with-ports.js user 3005')
console.log('   node scripts/start-with-ports.js admin 3006')
console.log('')
console.log('4. 环境变量配置:')
console.log('   USER_PORT=3005 ADMIN_PORT=3006 npm run dev:user')
console.log('')
console.log('5. 反向代理配置:')
console.log('   参考 .env.ports.example 文件中的 Nginx 配置示例')

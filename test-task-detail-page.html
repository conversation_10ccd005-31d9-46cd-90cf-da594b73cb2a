<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试任务详情页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-link {
            display: inline-block;
            padding: 10px 20px;
            margin: 10px;
            background-color: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .test-link:hover {
            background-color: #2563eb;
        }
        .info {
            background-color: #374151;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .step {
            margin: 15px 0;
            padding: 10px;
            background-color: #4b5563;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 任务详情页面测试</h1>
        
        <div class="info">
            <h2>📋 测试说明</h2>
            <p>这个页面用于测试任务详情页面的路由和数据加载功能。</p>
        </div>

        <div class="step">
            <h3>步骤 1: 访问任务管理页面</h3>
            <p>首先访问任务管理页面，查看现有的任务列表：</p>
            <a href="http://localhost:3000/admin/collect/tasks" class="test-link" target="_blank">
                📋 任务管理页面
            </a>
        </div>

        <div class="step">
            <h3>步骤 2: 测试任务详情页面</h3>
            <p>点击下面的链接测试不同任务的详情页面：</p>
            
            <a href="http://localhost:3000/admin/collect/tasks/13" class="test-link" target="_blank">
                📄 任务 13 详情
            </a>
            
            <a href="http://localhost:3000/admin/collect/tasks/12" class="test-link" target="_blank">
                📄 任务 12 详情
            </a>
            
            <a href="http://localhost:3000/admin/collect/tasks/11" class="test-link" target="_blank">
                📄 任务 11 详情
            </a>
        </div>

        <div class="step">
            <h3>步骤 3: 验证功能</h3>
            <p>在任务详情页面中验证以下功能：</p>
            <ul>
                <li>✅ 页面标题显示任务名称</li>
                <li>✅ 任务基本信息正确显示</li>
                <li>✅ 状态显示正确的颜色和文本</li>
                <li>✅ 操作按钮正常工作</li>
                <li>✅ 路由变化时页面内容更新</li>
            </ul>
        </div>

        <div class="step">
            <h3>步骤 4: 测试路由变化</h3>
            <p>在任务详情页面中，手动修改URL中的任务ID，验证页面是否自动更新：</p>
            <ul>
                <li>从 <code>/admin/collect/tasks/13</code> 改为 <code>/admin/collect/tasks/12</code></li>
                <li>观察页面内容是否自动更新</li>
                <li>检查浏览器控制台是否有相关日志</li>
            </ul>
        </div>

        <div class="info">
            <h2>🐛 调试信息</h2>
            <p>如果页面没有正常工作，请检查：</p>
            <ul>
                <li>浏览器控制台的错误信息</li>
                <li>网络请求是否成功</li>
                <li>API响应格式是否正确</li>
                <li>前端路由是否正确配置</li>
            </ul>
        </div>

        <div class="step">
            <h3>🔍 API测试</h3>
            <p>直接测试API接口：</p>
            <a href="http://localhost:3001/api/collect/tasks/13" class="test-link" target="_blank">
                🔗 API: 任务 13 详情
            </a>
            <a href="http://localhost:3001/api/collect/tasks" class="test-link" target="_blank">
                🔗 API: 任务列表
            </a>
        </div>
    </div>

    <script>
        console.log('🔧 任务详情页面测试工具已加载');
        console.log('📍 当前时间:', new Date().toLocaleString());
        
        // 检查前端服务是否运行
        fetch('http://localhost:3000/admin/collect/tasks')
            .then(response => {
                if (response.ok) {
                    console.log('✅ 前端服务正常运行');
                } else {
                    console.log('❌ 前端服务响应异常:', response.status);
                }
            })
            .catch(error => {
                console.log('❌ 前端服务连接失败:', error.message);
            });

        // 检查后端API是否运行
        fetch('http://localhost:3001/api/collect/tasks')
            .then(response => {
                if (response.ok) {
                    console.log('✅ 后端API正常运行');
                } else {
                    console.log('❌ 后端API响应异常:', response.status);
                }
            })
            .catch(error => {
                console.log('❌ 后端API连接失败:', error.message);
            });
    </script>
</body>
</html>

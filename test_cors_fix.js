/**
 * 测试CORS修复的简单脚本
 */

const express = require('express');
const cors = require('cors');
require('dotenv').config({ path: './backend/.env' });

const app = express();

// 使用修复后的CORS配置
const corsOptions = {
  origin: function (origin, callback) {
    // 允许的域名列表
    const allowedOrigins = process.env.CORS_ORIGIN 
      ? process.env.CORS_ORIGIN.split(',').map(url => url.trim())
      : ['http://localhost:3000'];
    
    console.log(`🔍 Checking origin: ${origin}`);
    console.log(`📋 Allowed origins: ${allowedOrigins.join(', ')}`);
    
    // 开发环境允许无origin的请求（如Postman）
    if (!origin && process.env.NODE_ENV === 'development') {
      console.log('✅ Allowing request without origin (development mode)');
      return callback(null, true);
    }
    
    // 检查origin是否在允许列表中
    if (allowedOrigins.indexOf(origin) !== -1) {
      console.log('✅ Origin allowed');
      callback(null, true);
    } else {
      console.log(`❌ CORS blocked origin: ${origin}`);
      console.log(`📋 Allowed origins: ${allowedOrigins.join(', ')}`);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

app.use(cors(corsOptions));

// 测试路由
app.get('/api/test', (req, res) => {
  res.json({
    message: 'CORS test successful!',
    origin: req.get('Origin'),
    timestamp: new Date().toISOString()
  });
});

app.get('/api/videos/featured/latest', (req, res) => {
  res.json({
    message: 'Mock video data',
    data: [],
    origin: req.get('Origin'),
    corsFixed: true
  });
});

// 启动服务器
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log('🚀 CORS Test Server started');
  console.log(`📡 Server running on port ${PORT}`);
  console.log('🌐 CORS Configuration:');
  console.log(`   Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`   CORS_ORIGIN: ${process.env.CORS_ORIGIN || 'not set'}`);
  
  const allowedOrigins = process.env.CORS_ORIGIN 
    ? process.env.CORS_ORIGIN.split(',').map(url => url.trim())
    : ['http://localhost:3000'];
  console.log(`   Allowed origins: ${allowedOrigins.join(', ')}`);
  
  console.log('\n📝 Test URLs:');
  console.log(`   http://localhost:${PORT}/api/test`);
  console.log(`   http://localhost:${PORT}/api/videos/featured/latest`);
  console.log('\n🧪 To test CORS:');
  console.log('   1. Open browser console on https://91jspg.com');
  console.log(`   2. Run: fetch('http://localhost:${PORT}/api/test').then(r=>r.json()).then(console.log)`);
});

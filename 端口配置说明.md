# 🚀 Nuxt.js影视CMS端口配置说明

## 📋 概述

本项目已经集成了灵活的端口配置系统，支持前台用户端和后台管理端使用不同端口，完美解决反向代理部署时的端口冲突问题。

## 🔧 端口配置

### 默认端口分配
- **用户前端**: `3000`
- **管理后台**: `3002` 
- **后端API**: `3001`
- **服务器主机**: `0.0.0.0`

### 配置文件位置
- **主配置**: `config/site.js`
- **环境变量**: `.env` 文件
- **示例配置**: `.env.ports.example`

## 🚀 启动方式

### 1. 开发环境

```bash
# 启动用户前端 (端口 3000)
npm run dev:user

# 启动管理后台 (端口 3002)  
npm run dev:admin

# 使用自定义端口启动
node scripts/start-with-ports.js user 3005    # 用户前端端口 3005
node scripts/start-with-ports.js admin 3006   # 管理后台端口 3006
```

### 2. 生产环境

```bash
# 构建并启动用户前端
npm run build:user && npm run start:user

# 构建并启动管理后台
npm run build:admin && npm run start:admin

# 使用自定义端口启动生产环境
node scripts/start-production.js user 3005    # 用户前端端口 3005
node scripts/start-production.js admin 3006   # 管理后台端口 3006
```

### 3. 环境变量配置

```bash
# 方式1: 临时设置环境变量
USER_PORT=3005 ADMIN_PORT=3006 npm run dev:user

# 方式2: 在 .env 文件中设置
USER_PORT=3005
ADMIN_PORT=3006
API_PORT=3001
HOST=0.0.0.0
```

## 🚀 PM2 生产环境配置

### ecosystem.config.cjs 完整配置

```javascript
module.exports = {
    apps: [
        // 用户前端 - 高并发，建议用Cluster
        {
            name: "NuxtUser",
            script: "./.output/server/index.mjs",
            exec_mode: "cluster",
            instances: "max",           // 使用所有CPU核心
            env: {
                NITRO_PORT: 3000,
                NODE_ENV: 'production'
            }
        },

        // 管理后台 - 低并发，用Fork即可
        {
            name: "NuxtAdmin",
            script: "./.output/server/index.mjs",
            exec_mode: "fork",          // 或者不写
            instances: 1,
            env: {
                NITRO_PORT: 3002,
                NODE_ENV: 'production'
            }
        },

        // 后端API - 看情况，通常用Cluster
        {
            name: "ExpressAPI",
            script: "./backend/src/app.js",
            exec_mode: "cluster",
            instances: 2,               // 适中的进程数
            env: {
                PORT: 3001,
                NODE_ENV: 'production'
            }
        }
    ],
};
```

### PM2 启动命令

```bash
# 启动所有服务
pm2 start ecosystem.config.cjs

# 重启所有服务
pm2 restart ecosystem.config.cjs

# 停止所有服务
pm2 stop ecosystem.config.cjs

# 查看服务状态
pm2 status

# 查看日志
pm2 logs
```

### 模式选择说明

| 服务类型 | 并发量 | 推荐模式 | 进程数 | 原因 |
|----------|--------|----------|--------|------|
| **用户前端** | 高 | Cluster | max | 大量用户访问，需要多核处理 |
| **管理后台** | 低 | Fork | 1 | 少数管理员使用，单进程够用 |
| **后端API** | 中-高 | Cluster | 2-4 | 为前端提供数据，需要处理能力 |

## 🌐 反向代理配置

### Nginx 配置示例

```nginx
# 用户前端 (端口 3000)
server {
    listen 80;
    server_name yourdomain.com;
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# 管理后台 (端口 3002)
server {
    listen 80;
    server_name admin.yourdomain.com;
    location / {
        proxy_pass http://127.0.0.1:3002;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# 后端API (端口 3001)
server {
    listen 80;
    server_name api.yourdomain.com;
    location / {
        proxy_pass http://127.0.0.1:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📁 新增文件说明

### 配置文件
- `config/site.js` - 增加了服务器端口配置
- `.env.ports.example` - 端口配置示例文件

### 启动脚本
- `scripts/start-with-ports.js` - 开发环境端口启动脚本
- `scripts/start-production.js` - 生产环境端口启动脚本

### 测试文件
- `test-port-config.js` - 端口配置测试脚本

## 🧪 测试配置

```bash
# 测试端口配置
node test-port-config.js
```

## � PM2 配置快速参考

### 简化版配置（推荐新手）

```javascript
// ecosystem.config.cjs
module.exports = {
    apps: [
        {
            name: "NuxtAdmin",
            script: "./.output/server/index.mjs",
            env: {
                NITRO_PORT: 3002,
                NODE_ENV: 'production'
            }
        }
    ],
};
```

### 部署步骤

```bash
# 1. 构建项目
npm run build:admin

# 2. 启动PM2
pm2 start ecosystem.config.cjs

# 3. 保存PM2配置
pm2 save

# 4. 设置开机自启
pm2 startup
```

### 常用PM2命令

```bash
pm2 status              # 查看所有进程状态
pm2 logs NuxtAdmin      # 查看指定进程日志
pm2 restart NuxtAdmin   # 重启指定进程
pm2 stop NuxtAdmin      # 停止指定进程
pm2 delete NuxtAdmin    # 删除指定进程
pm2 monit              # 实时监控
```

## �📖 使用场景

### 场景1: 本地开发
```bash
npm run dev:user    # 用户前端开发
npm run dev:admin   # 管理后台开发
```

### 场景2: 分离部署
```bash
# 服务器A: 用户前端
npm run build:user && npm run start:user

# 服务器B: 管理后台  
npm run build:admin && npm run start:admin
```

### 场景3: 同服务器不同端口
```bash
# 用户前端使用端口 3000
npm run build:user && npm run start:user

# 管理后台使用端口 3002
npm run build:admin && npm run start:admin
```

## ⚠️ 注意事项

1. **端口冲突**: 确保配置的端口没有被其他服务占用
2. **防火墙**: 确保配置的端口在防火墙中已开放
3. **环境变量**: 生产环境建议使用环境变量而不是硬编码端口
4. **API代理**: 前端会自动代理API请求到配置的后端端口

## 🔍 故障排除

### 端口被占用
```bash
# Windows 查看端口占用
netstat -ano | findstr :3000

# Linux/Mac 查看端口占用
lsof -i :3000
```

### 配置不生效
1. 检查环境变量是否正确设置
2. 重新构建项目
3. 清除缓存: `rm -rf .nuxt .output node_modules/.cache`

## 📞 技术支持

如有问题，请检查：
1. 端口配置是否正确
2. 环境变量是否生效
3. 构建是否成功
4. 网络连接是否正常

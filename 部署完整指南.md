# 91JSPG 完整部署指南

## 📋 部署架构概览

```
用户前端: https://91jspg.com (端口3000)
管理后台: https://admin.91jspg.com (端口3002)  
后端API: https://api.91jspg.com (端口3001)
```

## 🔧 第一步：本地编译准备

### 1.1 环境配置文件准备

#### 用户前端 `.env` 配置
```bash
# 用户前端生产环境配置
API_URL=https://api.91jspg.com
NODE_ENV=production
NUXT_PUBLIC_SITE_URL=https://91jspg.com
PORT=3000
HOST=0.0.0.0
# 注意: BUILD_TYPE=user 由 npm run build:user 自动设置
```

#### 管理后台 `.env` 配置
```bash
# 管理后台生产环境配置
ADMIN_API_URL=https://api.91jspg.com
NODE_ENV=production
NUXT_PUBLIC_SITE_URL=https://admin.91jspg.com
PORT=3002
HOST=0.0.0.0
# 注意: BUILD_TYPE=admin 由 npm run build:admin 自动设置
```

#### 后端 `backend/.env` 配置
```bash
# 服务器配置
NODE_ENV=production
PORT=3001
HOST=0.0.0.0

# 数据库配置 (请修改为您的服务器信息)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=video_cms
DB_USER=root
DB_PASSWORD=您的数据库密码

# JWT配置
JWT_SECRET=your_super_secure_jwt_secret_key_change_this_in_production_91jspg_2024

# 网站配置
SITE_NAME=91JSPG
SITE_URL=https://91jspg.com
ADMIN_EMAIL=<EMAIL>

# 安全配置
DEBUG=false
ENABLE_CORS=true
CORS_ORIGIN=https://91jspg.com,https://admin.91jspg.com
```

### 1.2 本地编译步骤

#### 编译用户前端
```bash
# 1. 设置用户前端环境
cp .env.user .env

# 2. 安装依赖
npm install

# 3. 编译用户前端 (使用专门的构建脚本)
npm run build:user

# 4. 打包用户前端文件
tar -czf 91jspg-user.tar.gz .output node_modules package.json package-lock.json .env scripts/
```

#### 编译管理后台
```bash
# 1. 设置管理后台环境
cp .env.admin .env

# 2. 编译管理后台 (使用专门的构建脚本)
npm run build:admin

# 3. 打包管理后台文件
tar -czf 91jspg-admin.tar.gz .output node_modules package.json package-lock.json .env scripts/
```

#### 打包后端
```bash
# 1. 进入后端目录
cd backend

# 2. 安装生产依赖
npm install --production

# 3. 打包后端文件
tar -czf 91jspg-backend.tar.gz src package.json package-lock.json .env node_modules
```

## 📤 第二步：上传到服务器

### 2.1 上传文件
```bash
# 上传到服务器 (请替换为您的服务器信息)
scp 91jspg-user.tar.gz root@您的服务器IP:/var/www/
scp 91jspg-admin.tar.gz root@您的服务器IP:/var/www/
scp 91jspg-backend.tar.gz root@您的服务器IP:/var/www/
```

### 2.2 服务器解压
```bash
# 登录服务器
ssh root@您的服务器IP

# 创建目录
mkdir -p /var/www/91jspg-user
mkdir -p /var/www/91jspg-admin  
mkdir -p /var/www/91jspg-backend

# 解压文件
cd /var/www
tar -xzf 91jspg-user.tar.gz -C 91jspg-user
tar -xzf 91jspg-admin.tar.gz -C 91jspg-admin
tar -xzf 91jspg-backend.tar.gz -C 91jspg-backend

# 清理压缩包
rm *.tar.gz
```

## 🗄️ 第三步：数据库配置

### 3.1 安装MySQL (如果未安装)
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# CentOS/RHEL
sudo yum install mysql-server
```

### 3.2 创建数据库
```bash
# 登录MySQL
mysql -u root -p

# 创建数据库
CREATE DATABASE video_cms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 创建用户 (可选，更安全)
CREATE USER 'video_cms_user'@'localhost' IDENTIFIED BY '您的密码';
GRANT ALL PRIVILEGES ON video_cms.* TO 'video_cms_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3.3 初始化数据库
```bash
# 进入后端目录
cd /var/www/91jspg-backend

# 运行数据库迁移
node src/scripts/migrate.js
```

## 🌐 第四步：Nginx反向代理配置

### 4.1 安装Nginx
```bash
# Ubuntu/Debian
sudo apt install nginx

# CentOS/RHEL  
sudo yum install nginx
```

### 4.2 创建Nginx配置
```bash
# 创建配置文件
sudo nano /etc/nginx/sites-available/91jspg
```

配置内容：
```nginx
# 用户前端配置 (91jspg.com)
server {
    listen 80;
    server_name 91jspg.com www.91jspg.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name 91jspg.com www.91jspg.com;

    # SSL 证书配置
    ssl_certificate /etc/ssl/certs/91jspg.com.crt;
    ssl_certificate_key /etc/ssl/private/91jspg.com.key;

    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}

# 管理后台配置 (admin.91jspg.com)
server {
    listen 80;
    server_name admin.91jspg.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name admin.91jspg.com;

    ssl_certificate /etc/ssl/certs/91jspg.com.crt;
    ssl_certificate_key /etc/ssl/private/91jspg.com.key;

    location / {
        proxy_pass http://127.0.0.1:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}

# 后端API配置 (api.91jspg.com)
server {
    listen 80;
    server_name api.91jspg.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.91jspg.com;

    ssl_certificate /etc/ssl/certs/91jspg.com.crt;
    ssl_certificate_key /etc/ssl/private/91jspg.com.key;

    location / {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300;
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
    }
}
```

### 4.3 启用配置
```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/91jspg /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

## 🔒 第五步：SSL证书配置

### 5.1 使用Let's Encrypt (免费SSL)
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d 91jspg.com -d www.91jspg.com -d admin.91jspg.com -d api.91jspg.com

# 设置自动续期
sudo crontab -e
# 添加以下行：
0 12 * * * /usr/bin/certbot renew --quiet
```

## 🚀 第六步：启动服务

### 6.1 安装PM2 (进程管理器)
```bash
# 安装PM2
sudo npm install -g pm2
```

### 6.2 创建PM2配置文件
```bash
# 创建PM2配置
sudo nano /var/www/ecosystem.config.js
```

配置内容：
```javascript
module.exports = {
  apps: [
    {
      name: '91jspg-user',
      cwd: '/var/www/91jspg-user',
      script: 'node_modules/nuxt/bin/nuxt.mjs',
      args: 'start',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        HOST: '0.0.0.0'
      }
    },
    {
      name: '91jspg-admin',
      cwd: '/var/www/91jspg-admin',
      script: 'node_modules/nuxt/bin/nuxt.mjs',
      args: 'start',
      env: {
        NODE_ENV: 'production',
        PORT: 3002,
        HOST: '0.0.0.0'
      }
    },
    {
      name: '91jspg-backend',
      cwd: '/var/www/91jspg-backend',
      script: 'src/app.js',
      env: {
        NODE_ENV: 'production',
        PORT: 3001,
        HOST: '0.0.0.0'
      }
    }
  ]
};
```

### 6.3 启动所有服务
```bash
# 启动所有应用
cd /var/www
sudo pm2 start ecosystem.config.js

# 保存PM2配置
sudo pm2 save

# 设置开机自启
sudo pm2 startup
sudo pm2 save
```

## 📋 第七步：验证部署

### 7.1 检查服务状态
```bash
# 查看PM2状态
sudo pm2 status

# 查看日志
sudo pm2 logs

# 查看特定应用日志
sudo pm2 logs 91jspg-user
sudo pm2 logs 91jspg-admin
sudo pm2 logs 91jspg-backend
```

### 7.2 测试访问
```bash
# 测试后端API
curl https://api.91jspg.com/api/videos

# 测试用户前端
curl https://91jspg.com

# 测试管理后台
curl https://admin.91jspg.com
```

## 🔧 第八步：常用维护命令

### 8.1 PM2 管理命令
```bash
# 重启所有服务
sudo pm2 restart all

# 重启特定服务
sudo pm2 restart 91jspg-user

# 停止服务
sudo pm2 stop all

# 查看监控
sudo pm2 monit
```

### 8.2 更新部署
```bash
# 停止服务
sudo pm2 stop all

# 备份当前版本
sudo cp -r /var/www/91jspg-user /var/www/91jspg-user.backup

# 上传新版本并解压
# ... (重复第二步)

# 重启服务
sudo pm2 restart all
```

## ⚠️ 重要注意事项

1. **域名解析**: 确保以下域名都解析到您的服务器IP
   - 91jspg.com
   - www.91jspg.com
   - admin.91jspg.com
   - api.91jspg.com

2. **防火墙设置**: 开放必要端口
   ```bash
   sudo ufw allow 80
   sudo ufw allow 443
   sudo ufw allow 22
   ```

3. **数据库安全**:
   - 修改默认密码
   - 限制远程访问
   - 定期备份

4. **文件权限**: 确保上传目录权限正确
   ```bash
   sudo chown -R www-data:www-data /var/www/91jspg-backend/uploads
   sudo chmod -R 755 /var/www/91jspg-backend/uploads
   ```

## 🎉 部署完成

如果所有步骤都正确执行，您现在应该可以访问：
- 用户前端: https://91jspg.com
- 管理后台: https://admin.91jspg.com
- API接口: https://api.91jspg.com

部署成功！🚀
